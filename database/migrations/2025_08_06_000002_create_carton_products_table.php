<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCartonProductsTable extends Migration
{
    public function up(): void
    {
        Schema::create('carton_products', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('carton_id');
            $table->unsignedInteger('product_id');
            $table->unsignedInteger('quantity')->default(1);
            $table->timestamps();

            $table->index(['carton_id']);
            $table->index(['carton_id', 'product_id']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('carton_products');
    }
};
