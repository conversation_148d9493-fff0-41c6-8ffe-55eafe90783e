<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddCartonPermissionsToSellerUserPermissionsTable extends Migration
{
    public function up(): void
    {
        Schema::table('seller_user_permissions', function (Blueprint $table) {
            $table->boolean('carton_view')->default(false);
            $table->boolean('carton_create')->default(false);
            $table->boolean('carton_edit')->default(false);
            $table->boolean('carton_delete')->default(false);
        });
    }

    public function down(): void
    {
        Schema::table('seller_user_permissions', function (Blueprint $table) {
            $table->dropColumn(['carton_view', 'carton_create', 'carton_edit', 'carton_delete']);
        });
    }
};
