<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSellerCourierMoveosTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('seller_courier_moveo', function (Blueprint $table) {
            $table->id();
            $table->integer('seller_id');
            $table->string('account_title')->nullable();
            $table->string('account_id')->nullable();
            $table->text('api_token');
            $table->boolean('is_default')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('seller_courier_moveo');
    }
}
