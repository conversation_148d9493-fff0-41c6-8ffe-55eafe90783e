<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCartonsTable extends Migration
{
    public function up(): void
    {
        Schema::create('cartons', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('seller_id');
            $table->string('name');
            $table->string('barcode', 100);
            $table->unsignedTinyInteger('total_product')->default(0); // New column to track the number of products in the carton
            $table->unsignedTinyInteger('total_quantity')->default(0); // New column to track the number of quantities in the carton
            $table->boolean('status')->default(1);
            $table->timestamps();

            $table->index(['seller_id', 'barcode', 'status']);
            $table->index(['seller_id', 'status']);
            $table->index(['seller_id', 'created_at']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('cartons');
    }
};
