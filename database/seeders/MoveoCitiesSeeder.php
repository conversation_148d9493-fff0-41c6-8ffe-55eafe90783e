<?php

namespace Database\Seeders;

use App\Models\Courier\City;
use App\Models\Courier\CourierCity;
use Illuminate\Database\Seeder;

class MoveoCitiesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $cities = ['Karachi'];

        foreach ($cities as $value) {
            $city = City::whereName($value);
            if ($city->exists()) {
                $city = $city->first();

                if (! CourierCity::whereCourierId(43)->whereCityId($city->id)->exists()) {
                    $courier_city = new CourierCity;
                    $courier_city->courier_id = 43;
                    $courier_city->city_id = $city->id;
                    $courier_city->courier_city_code = $city->name;
                    $courier_city->courier_city_name = $city->name;
                    $courier_city->origin = 1;
                    $courier_city->save();
                }
            }
        }
    }
}
