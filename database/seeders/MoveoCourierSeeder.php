<?php

namespace Database\Seeders;

use App\Models\Courier\Courier;
use Illuminate\Database\Seeder;

class MoveoCourierSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        if (!Courier::where('id', 43)->exists()) {
            Courier::create([
                'id' => 43,
                'name' => 'Moveo',
                'status' => 1,
                'multi_package_shipment' => 0,
                'image' => 's-logo-43.png',
                // Add other fields as needed
            ]);
        }
    }
}
