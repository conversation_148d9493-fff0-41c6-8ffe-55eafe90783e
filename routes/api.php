<?php

use App\Events\AddressValidationLikelihoodEvent;
use App\Events\OrderCreatedEvent;
use App\Events\OrderShippingEvent;
use App\Helpers\KhaadiHackedCode;
use App\Mail\IntegrationError;
use App\Models\AutoShipped;
use App\Models\Courier\City;
use App\Models\Courier\SellerCourierBlueEx;
use App\Models\Courier\SellerCourierBykea;
use App\Models\Courier\SellerCourierCallCourier;
use App\Models\Courier\SellerCourierDeliveryExpress;
use App\Models\Courier\SellerCourierDHL;
use App\Models\Courier\SellerCourierLCS;
use App\Models\Courier\SellerCourierLCSUAE;
use App\Models\Courier\SellerCourierMNP;
use App\Models\Courier\SellerCourierMoveX;
use App\Models\Courier\SellerCourierRider;
use App\Models\Courier\SellerCourierSwyft;
use App\Models\Courier\SellerCourierTCS;
use App\Models\Courier\SellerCourierTCSNew;
use App\Models\Courier\SellerCourierTraxNew;
use App\Models\Order;
use App\Events\BulkShipmentCancellationEvent;
use App\Events\ShipmentStatusChangeEvent;
use App\Events\ShopifyOrderItemBarcodeEvent;
use App\Helpers\AutoShippingMethodBook;
use App\Helpers\CustomerOrderConfirmation;
use App\Helpers\RecordOrderConfirmation;
use App\Http\Controllers\GHQController;
use App\Http\Controllers\ShipmentController;
use App\Http\Controllers\OrderController;
use App\Jobs\CancelFulfillment;
use App\Models\AddOn;
use App\Models\Inventory;
use App\Models\SellerFfcLocation;
use App\Models\SellerFfcInventory;
use App\Models\OrderTempLocation;
use App\Models\OrderCancellationReason;
use App\Models\OrderComments;
use App\Models\OrderItem;
use App\Models\OrderTag;
use App\Models\Product;
use App\Models\Reason;
use App\Models\Seller;
use App\Models\SellerUser;
use App\Models\SellerLocation;
use App\Models\SellerShipmentMethod;
use App\Models\SellerPaymentMethod;
use App\Models\Setting;
use App\Models\Shipment;
use App\Models\ShipmentHistory;
use App\Models\ShopifyApp;
use App\Models\Tag;
use App\Models\StockOrder;
use App\Models\StockOrderItem;
use App\Service\CodeReuse\CustomerIntimationService;
use App\Service\CodeReuse\LoadsheetService;
use App\Service\Integration\CustomAPI\OrderCancel;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\API\AuthController;
use App\Http\Controllers\API\FFCController;
use App\Http\Controllers\API\FlashController;
use App\Http\Controllers\ShipperAdviceController;
use App\Models\SellerOrderConfirmation;
use App\Http\Controllers\API\ERPController;
use App\Helpers\FBR;
use App\Events\ShopifyGetPaymentIdEvent;
use App\Helpers\GetMagentoCatalog;
use App\Http\Controllers\API\MyAliceController;
use App\Jobs\CreateOrUpdateShopifyProduct;
use App\Jobs\DeleteShopifyProduct;
use App\Models\FulfillmentOrder;
use App\Models\OmniEngineHoldReason;
use Illuminate\Support\Facades\Mail;

// use App\Traits\ShipmentStatusChangeEventHandlerTrait;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

//$hook='{"id":58,"parent_id":0,"number":"58","order_key":"wc_order_5b12bb01d4374","created_via":"checkout","version":"3.4.1","status":"processing","currency":"PKR","date_created":"2018-06-02T15:42:57","date_created_gmt":"2018-06-02T15:42:57","date_modified":"2018-06-02T15:42:57","date_modified_gmt":"2018-06-02T15:42:57","discount_total":"0.00","discount_tax":"0.00","shipping_total":"200.00","shipping_tax":"0.00","cart_tax":"0.00","total":"245.00","total_tax":"0.00","prices_include_tax":false,"customer_id":1,"customer_ip_address":"::1","customer_user_agent":"mozilla\/5.0 (macintosh; intel mac os x 10_13_4) applewebkit\/537.36 (khtml, like gecko) chrome\/66.0.3359.181 safari\/537.36","customer_note":"","billing":{"first_name":"Test","last_name":"Unity","company":"Unity","address_1":"Test House","address_2":"Test Apartment","city":"Karachi","state":"SD","postcode":"20001","country":"PK","email":"<EMAIL>","phone":"03458225212"},"shipping":{"first_name":"Test","last_name":"Unity","company":"Unity","address_1":"Test House","address_2":"Test Apartment","city":"Karachi","state":"SD","postcode":"20001","country":"PK"},"payment_method":"cod","payment_method_title":"Cash on delivery","transaction_id":"","date_paid":null,"date_paid_gmt":null,"date_completed":null,"date_completed_gmt":null,"cart_hash":"a343f09d70a8ccda1097948104001d7a","meta_data":[],"line_items":[{"id":1,"name":"Hoodie - Green, No","product_id":11,"variation_id":28,"quantity":1,"tax_class":"","subtotal":"45.00","subtotal_tax":"0.00","total":"45.00","total_tax":"0.00","taxes":[],"meta_data":[{"id":10,"key":"pa_color","value":"green"},{"id":11,"key":"logo","value":"No"}],"sku":"woo-hoodie-green","price":45}],"tax_lines":[],"shipping_lines":[{"id":2,"method_title":"Flat rate","method_id":"flat_rate","instance_id":"1","total":"200.00","total_tax":"0.00","taxes":[],"meta_data":[{"id":17,"key":"Items","value":"Hoodie - Green, No &times; 1"}]}],"fee_lines":[],"coupon_lines":[],"refunds":[]}';

Route::middleware('auth:api')->get('/user', function (Request $request) {
    return $request->user();
});

Route::get('order/{id}', function ($id) {
    return Order::find($id);
});

Route::get('test', function (Request $request) {
    $hook = '{"id":58,"parent_id":0,"number":"58","order_key":"wc_order_5b12bb01d4374","created_via":"checkout","version":"3.4.1","status":"processing","currency":"PKR","date_created":"2018-06-02T15:42:57","date_created_gmt":"2018-06-02T15:42:57","date_modified":"2018-06-02T15:42:57","date_modified_gmt":"2018-06-02T15:42:57","discount_total":"0.00","discount_tax":"0.00","shipping_total":"200.00","shipping_tax":"0.00","cart_tax":"0.00","total":"245.00","total_tax":"0.00","prices_include_tax":false,"customer_id":1,"customer_ip_address":"::1","customer_user_agent":"mozilla\/5.0 (macintosh; intel mac os x 10_13_4) applewebkit\/537.36 (khtml, like gecko) chrome\/66.0.3359.181 safari\/537.36","customer_note":"","billing":{"first_name":"Test","last_name":"Unity","company":"Unity","address_1":"Test House","address_2":"Test Apartment","city":"Karachi","state":"SD","postcode":"20001","country":"PK","email":"<EMAIL>","phone":"03458225212"},"shipping":{"first_name":"Test","last_name":"Unity","company":"Unity","address_1":"Test House","address_2":"Test Apartment","city":"Karachi","state":"SD","postcode":"20001","country":"PK"},"payment_method":"cod","payment_method_title":"Cash on delivery","transaction_id":"","date_paid":null,"date_paid_gmt":null,"date_completed":null,"date_completed_gmt":null,"cart_hash":"a343f09d70a8ccda1097948104001d7a","meta_data":[],"line_items":[{"id":1,"name":"Hoodie - Green, No","product_id":11,"variation_id":28,"quantity":1,"tax_class":"","subtotal":"45.00","subtotal_tax":"0.00","total":"45.00","total_tax":"0.00","taxes":[],"meta_data":[{"id":10,"key":"pa_color","value":"green"},{"id":11,"key":"logo","value":"No"}],"sku":"woo-hoodie-green","price":45}],"tax_lines":[],"shipping_lines":[{"id":2,"method_title":"Flat rate","method_id":"flat_rate","instance_id":"1","total":"200.00","total_tax":"0.00","taxes":[],"meta_data":[{"id":17,"key":"Items","value":"Hoodie - Green, No &times; 1"}]}],"fee_lines":[],"coupon_lines":[],"refunds":[]}';
    $secret = 'abcd123secret';

    return base64_encode(hash_hmac('sha256', $hook, $secret, true));
})->middleware('wcapiauth');

Route::post('wc/order', function (Request $request) {
    try {
        if (in_array(\Session::get('seller_id'), [78, 90])) {
            \Log::info('Seller ID = '.\Session::get('seller_id').' | Woocommerce DUMP '.json_encode($request->all()));
        }

        $hook = $request;
        //$hook='{"id":79,"parent_id":0,"number":"58","order_key":"wc_order_5b12bb01d4374","created_via":"checkout","version":"3.4.1","status":"processing","currency":"PKR","date_created":"2018-06-02T15:42:57","date_created_gmt":"2018-06-02T15:42:57","date_modified":"2018-06-02T15:42:57","date_modified_gmt":"2018-06-02T15:42:57","discount_total":"0.00","discount_tax":"0.00","shipping_total":"200.00","shipping_tax":"0.00","cart_tax":"0.00","total":"245.00","total_tax":"0.00","prices_include_tax":false,"customer_id":1,"customer_ip_address":"::1","customer_user_agent":"mozilla\/5.0 (macintosh; intel mac os x 10_13_4) applewebkit\/537.36 (khtml, like gecko) chrome\/66.0.3359.181 safari\/537.36","customer_note":"","billing":{"first_name":"Test","last_name":"Unity","company":"Unity","address_1":"Test House","address_2":"Test Apartment","city":"Karachi","state":"SD","postcode":"20001","country":"PK","email":"<EMAIL>","phone":"03458225212"},"shipping":{"first_name":"Test","last_name":"Unity","company":"Unity","address_1":"Test House","address_2":"Test Apartment","city":"Karachi","state":"SD","postcode":"20001","country":"PK"},"payment_method":"cod","payment_method_title":"Cash on delivery","transaction_id":"","date_paid":null,"date_paid_gmt":null,"date_completed":null,"date_completed_gmt":null,"cart_hash":"a343f09d70a8ccda1097948104001d7a","meta_data":[],"line_items":[{"id":1,"name":"Hoodie - Green, No","product_id":11,"variation_id":28,"quantity":1,"tax_class":"","subtotal":"45.00","subtotal_tax":"0.00","total":"45.00","total_tax":"0.00","taxes":[],"meta_data":[{"id":10,"key":"pa_color","value":"green"},{"id":11,"key":"logo","value":"No"}],"sku":"woo-hoodie-green","price":45}],"tax_lines":[],"shipping_lines":[{"id":2,"method_title":"Flat rate","method_id":"flat_rate","instance_id":"1","total":"200.00","total_tax":"0.00","taxes":[],"meta_data":[{"id":17,"key":"Items","value":"Hoodie - Green, No &times; 1"}]}],"fee_lines":[],"coupon_lines":[],"refunds":[]}';
        //return $request;
        //$hook=json_decode($hook);
        //var_dump($hook->id);
        //die;
        $marketplace_reference_id = $hook->id;
        $seller_id = \Session::get('seller_id');
        $order_check = Order::onWriteConnection()->where('marketplace_reference_id', $marketplace_reference_id)->where('seller_id', $seller_id);
        if ($order_check->count() == 0) {
            $order = new Order();
            $order->source_id = 1;
            $order->marketplace_id;
            $order->seller_location_id = 1;
            $order->marketplace_reference_id = $hook->id;
            $order->seller_id = $seller_id;
            $order->created_date = date('Y-m-d H:i:s');

            $order->customer_email = ($hook->billing['email'] ? $hook->billing['email'] : Null);
            $order->customer_number = str_replace('+92', '0', $hook->billing['phone']);
            $order->customer_number = ( substr( $order->customer_number, 0, 2 ) == "92" ? "0".substr( $order->customer_number, 2) : $order->customer_number );
            $order->customer_name = ($hook->shipping['first_name'] ? $hook->shipping['first_name'] : $hook->billing['first_name']).' '.($hook->shipping['last_name'] ? $hook->shipping['last_name'] : $hook->billing['last_name']);
            $order->destination_city = ucwords($hook->shipping['city'] ? $hook->shipping['city'] : $hook->billing['city'] );
            $order->shipping_address = ($hook->shipping['address_1'] ? $hook->shipping['address_1'] : $hook->billing['address_1']) . " " . ($hook->shipping['address_2'] ? $hook->shipping['address_2'] : $hook->billing['address_2']).' '.($hook->shipping['state'] ? $hook->shipping['state'] : $hook->billing['state']).' '.$order->destination_city;

            $order->discount = ($hook->discount_total ? $hook->discount_total+$hook->discount_tax : 0);
            $order->grand_total = ($hook->total ? $hook->total : 0);
            $order->cod_payment = ($hook->payment_method == 'cod' ? 1 : 0);
            $order->shipping_fee = ($hook->shipping_total ? $hook->shipping_total : 0);
            $order->status =  config('enum.order_status')['PENDING'];

            // $order->placement_date = $hook->date_created ;
            $order->placement_date = Carbon::parse($hook->date_created_gmt,'gmt')->setTimezone(config('app.timezone'))->toDateTimeString();

            $payment_method_id = SellerPaymentMethod::where('seller_id',$seller_id)->where('machine_name',$hook->payment_method)->first();
            if($payment_method_id){
                $order->seller_payment_method_id = $payment_method_id->id;
                $order->cod_payment = ($payment_method_id->payment_on_delivery == 1 ? 1 : 0);                
            }

            foreach($hook->shipping_lines as $line)
            {
                $method_id = SellerShipmentMethod::where('seller_id',$seller_id)->where('machine_name',$line['method_title'])->first();
                if($method_id){
                    $order->seller_shipment_method_id = $method_id->id;
                    
                }
            
                
            }
            $order->customer_number = str_replace(' ','',$order->customer_number);
            $isOrderSaved=$order->save();
            $concatinated_customer_details = $order->customer_number."|".$order->customer_email;

            //dd($request->items);
            foreach ($hook->line_items as $item) {
                $orderItem = new OrderItem();
                $orderItem->order_id = $order->id;

                //This to cater if product created on shopify is also on Unity - realised when working on FFC
                $item_sku = ($item['sku'] ? $item['sku'] : '');
                $product = Product::where('seller_id',$seller_id)->where('SKU', $item_sku)->select('id')->first();
                if($product){
                    $orderItem->product_id = $product->id;
                }else{
                    $orderItem->product_id = ($item['product_id'] ? $item['product_id'] : 'Not Present');
                }

                $orderItem->product_name = ($item['name'] ? $item['name'] : 'Not present');
                $orderItem->SKU = ($item['sku'] ? $item['sku'] : 'Not present');
                // foreach($item['meta_data'] as $meta)
                // {
                //     $orderItem->description .= ($meta['key'] ? $meta['key'] : 'Not present') . " : " . ($meta['value'] ? $meta['value'] : 'Not present') . "<br/>";
                // }
                $orderItem->description;
                $orderItem->description2;
                $orderItem->description3;
                $orderItem->seller_unique_code = ($item['product_id'] ? $item['product_id'] : 'Not Present');
                $orderItem->material_of_product;
                $orderItem->cost = ($item['subtotal'] ? $item['subtotal'] : 0);

                /// For Discount
                $discount = $item['subtotal'] - $item['total'];
                if ($item['subtotal_tax']) {
                    $discount = $discount + ($item['subtotal_tax'] - $item['total_tax']);
                }
                $orderItem->discount = $discount;

                $orderItem->unit_price = ($item['subtotal'] ? ($item['quantity'] ? $item['subtotal'] / $item['quantity'] : 0) : 0);
                $orderItem->quantity = ($item['quantity'] ? $item['quantity'] : 0);
                $orderItem->sub_total = ($item['subtotal'] ? $item['subtotal']-$discount : 0 );

                if(isset($hook->tax_lines[0]['rate_percent'])){
                    $orderItem->tax_rate = ($hook->tax_lines[0]['rate_percent'] ? $hook->tax_lines[0]['rate_percent'] : 0 );
                } else{
                    $orderItem->tax_rate = 0;
                }
                $orderItem->tax_rate = ($orderItem->tax_rate / 100);
                $orderItem->tax = $item['subtotal_tax'];
                $orderItem->weight = 0;
                $orderItem->status = config('enum.item_status')['PENDING'];
                $orderItem->reason = '';
                $orderItem->save();
                $order_skus[] = $orderItem->SKU;
            }

            sort($order_skus);
            $concatinated_customer_details .= "|".implode('|', $order_skus);
            $concatinated_customer_details_md5_hash = md5($concatinated_customer_details);
            
            $order->order_hash = $concatinated_customer_details_md5_hash;
            $order->is_created = 1;
            $order->save();

            //check for order duplication
            $order_duplicate = OmniEngineHoldReason::checkForOrderDuplication($order);


            // OrderComments::add($order->id, 'Order Dropped Process', '#'.$order->marketplace_reference_id.' Order Dropped from website source <b>'.$request->header('x-wc-webhook-source').' (WooCommerce)</b>' , 'Success', '1');
            // if(Setting::where('seller_id', $seller_id)->where('key',config('enum.add_ons')['AUTOROBOCALL'])->value('value')){
            //     $client = new \GuzzleHttp\Client(['verify' => false]);
            //     $response = $client->post(env('APP_URL').'/api/robocall', [
            //         'http_errors' => FALSE,
            //         'form_params' => [
            //             'ids' => [$order->id]
			// 		]
            //     ]);
            // }

            // if(AddOn::where('seller_id', $order->seller_id)->where('key',config('enum.add_ons')['CUSTOMERORDERCONFIRMATION'])->where('value',1)->exists() && Setting::where('seller_id', $order->seller_id)->where('key',config('enum.settings')['CUSTOMERORDERCONFIRMATION'])->where('value',1)->exists()){
            //     CustomerOrderConfirmation::getUrl($order->id,$order->customer_number);
            // }
            $add_on_auto_confirm = \App\Models\AddOn::where('seller_id',$order->seller_id)->where('key',config('enum.add_ons')['AUTOORDERCONFIRMATION'])->where('value','1')->first();
            if($add_on_auto_confirm && !$order_duplicate)
                CustomerOrderConfirmation::autoConfirmProcess($order);

            AutoShippingMethodBook::addTag($order);

            event(new AddressValidationLikelihoodEvent($order));


            AutoShipped::auto_shipped_created_order($order->id);
            $order->passthrough();
            \Log::info('WooCommerce | '.$order->id.' | Order Created');

            if ($hook->customer_note) {
                OrderComments::add($order->id, 'Order Customer Notes', $hook->customer_note, 'Success', '1');
            }

        //Omni Location Assignment Item wise
        if(AddOn::omniLocationAssignment($order->seller_id)){
            $olDeterminationTrig = Setting::where('seller_id', $order->seller_id)->where('key', config('enum.settings')['OMNILOCATIONDETERMINATIONTRIGGER'])->first();
            if (isset($olDeterminationTrig) && $olDeterminationTrig->value == 'order_creation') {
                $order->assignLocationToOrderItems();
            }
        }

        //Single Auto Location Auto Assingment
        if(!AddOn::omniLocationAssignment($order->seller_id)){
            $assign_orders_to_ffc_setting = Setting::where('seller_id', $order->seller_id)->where('key', config('enum.settings')['ASSIGN-ORDER-TO-LOCATION'])->first();
            if (isset($assign_orders_to_ffc_setting)) {
                if ($assign_orders_to_ffc_setting->value == 'order_creation') {
                    $order->assignSingleLocation();
                }
            }
        }

            return 'Order Created';
        } else {
            \Log::info('WooCommerce | Duplicate Order');

            return 'Duplicate Order';
        }
    } catch (\Exception $e) {
        \Log::info('WooCommerce | '.$e->getMessage());
        $activity_id = activity()
                        ->causedBy(\Session::get('seller_id'))
                        ->withProperties(['response' => $e->getMessage(), 'dump' => json_encode($request->all())])
                        ->log('WooCommerce');
        Mail::to('<EMAIL>')->send(new IntegrationError($activity_id, 'WooCommerce'));
    }
})->middleware('wcapiauth');

// function traverseArray($array, &$string)
// {
//     // Loops through each element. If element again is array, function is recalled. If not, result is echoed.
//     foreach ($array as $key=>$value) {
//         if (is_array($value)) {
//             traverseArray($value, $string);
//         } else {
//             $string .= $key.' = '.$value."<br />\n";
//         }
//     }
// }

Route::post('mg19/order', function (Request $request) {
    $hook = $request->getContent();
    //print_r($hook);die;
    //$hook='{"items":[{"item_id":"678","order_id":"222","parent_item_id":null,"quote_item_id":"2558","store_id":"1","created_at":"2018-07-27 09:14:57","updated_at":"2018-07-27 09:15:22","product_id":"410","product_type":"configurable","product_options":"a:7:{s:15:\"info_buyRequest\";a:7:{s:4:\"uenc\";s:84:\"aHR0cDovL2J1a2hhcmEudGVjaG5vbGVjdHVhbHMuY29tL2NoZWxzZWEtdGVlLTcyMC5odG1sP19fX1NJRD1V\";s:7:\"product\";s:3:\"410\";s:8:\"form_key\";s:16:\"0342XMIz8C9J6lzh\";s:15:\"related_product\";s:0:\"\";s:15:\"super_attribute\";a:2:{i:92;s:2:\"20\";i:180;s:2:\"78\";}s:7:\"options\";a:2:{i:3;s:13:\"Monogram Logo\";i:2;s:1:\"1\";}s:3:\"qty\";s:1:\"1\";}s:7:\"options\";a:2:{i:0;a:7:{s:5:\"label\";s:8:\"monogram\";s:5:\"value\";s:13:\"Monogram Logo\";s:11:\"print_value\";s:13:\"Monogram Logo\";s:9:\"option_id\";s:1:\"3\";s:11:\"option_type\";s:4:\"area\";s:12:\"option_value\";s:13:\"Monogram Logo\";s:11:\"custom_view\";b:0;}i:1;a:7:{s:5:\"label\";s:19:\"Test Custom Options\";s:5:\"value\";s:7:\"model 1\";s:11:\"print_value\";s:7:\"model 1\";s:9:\"option_id\";s:1:\"2\";s:11:\"option_type\";s:9:\"drop_down\";s:12:\"option_value\";s:1:\"1\";s:11:\"custom_view\";b:0;}}s:15:\"attributes_info\";a:2:{i:0;a:2:{s:5:\"label\";s:5:\"Color\";s:5:\"value\";s:5:\"Black\";}i:1;a:2:{s:5:\"label\";s:4:\"Size\";s:5:\"value\";s:1:\"L\";}}s:11:\"simple_name\";s:11:\"Chelsea Tee\";s:10:\"simple_sku\";s:6:\"mtk005\";s:20:\"product_calculations\";i:1;s:13:\"shipment_type\";i:0;}","weight":"1.0000","is_virtual":"0","sku":"mtk005","name":"Chelsea Tee","description":null,"applied_rule_ids":null,"additional_data":null,"free_shipping":"0","is_qty_decimal":"0","no_discount":"0","qty_backordered":null,"qty_canceled":"0.0000","qty_invoiced":1,"qty_ordered":"1.0000","qty_refunded":"0.0000","qty_shipped":"0.0000","base_cost":null,"price":"154.0000","base_price":"154.0000","original_price":"154.0000","base_original_price":"154.0000","tax_percent":"0.0000","tax_amount":"0.0000","base_tax_amount":"0.0000","tax_invoiced":0,"base_tax_invoiced":0,"discount_percent":"0.0000","discount_amount":"0.0000","base_discount_amount":"0.0000","discount_invoiced":0,"base_discount_invoiced":0,"amount_refunded":"0.0000","base_amount_refunded":"0.0000","row_total":"154.0000","base_row_total":"154.0000","row_invoiced":154,"base_row_invoiced":154,"row_weight":"1.0000","base_tax_before_discount":null,"tax_before_discount":null,"ext_order_item_id":null,"locked_do_invoice":null,"locked_do_ship":null,"price_incl_tax":"154.0000","base_price_incl_tax":"154.0000","row_total_incl_tax":"154.0000","base_row_total_incl_tax":"154.0000","hidden_tax_amount":"0.0000","base_hidden_tax_amount":"0.0000","hidden_tax_invoiced":0,"base_hidden_tax_invoiced":0,"hidden_tax_refunded":null,"base_hidden_tax_refunded":null,"is_nominal":"0","tax_canceled":null,"hidden_tax_canceled":null,"tax_refunded":null,"base_tax_refunded":null,"discount_refunded":null,"base_discount_refunded":null,"gift_message_id":null,"gift_message_available":"1","base_weee_tax_applied_amount":"0.0000","base_weee_tax_applied_row_amnt":"0.0000","base_weee_tax_applied_row_amount":"0.0000","weee_tax_applied_amount":"0.0000","weee_tax_applied_row_amount":"0.0000","weee_tax_applied":"a:0:{}","weee_tax_disposition":"0.0000","weee_tax_row_disposition":"0.0000","base_weee_tax_disposition":"0.0000","base_weee_tax_row_disposition":"0.0000","event_id":null,"giftregistry_item_id":null,"gw_id":null,"gw_base_price":null,"gw_price":null,"gw_base_tax_amount":null,"gw_tax_amount":null,"gw_base_price_invoiced":null,"gw_price_invoiced":null,"gw_base_tax_amount_invoiced":null,"gw_tax_amount_invoiced":null,"gw_base_price_refunded":null,"gw_price_refunded":null,"gw_base_tax_amount_refunded":null,"gw_tax_amount_refunded":null,"qty_returned":"0.0000","has_children":true}],"OrderData":{"entity_id":"222","state":"processing","status":"processing","coupon_code":null,"protect_code":"be7a44","shipping_description":"Flat Rate - Fixed","is_virtual":"0","store_id":"1","customer_id":null,"base_discount_amount":"0.0000","base_discount_canceled":null,"base_discount_invoiced":0,"base_discount_refunded":null,"base_grand_total":"159.0000","base_shipping_amount":"5.0000","base_shipping_canceled":null,"base_shipping_invoiced":5,"base_shipping_refunded":null,"base_shipping_tax_amount":"0.0000","base_shipping_tax_refunded":null,"base_subtotal":"154.0000","base_subtotal_canceled":null,"base_subtotal_invoiced":154,"base_subtotal_refunded":null,"base_tax_amount":"0.0000","base_tax_canceled":null,"base_tax_invoiced":0,"base_tax_refunded":null,"base_to_global_rate":"1.0000","base_to_order_rate":"1.0000","base_total_canceled":null,"base_total_invoiced":159,"base_total_invoiced_cost":0,"base_total_offline_refunded":null,"base_total_online_refunded":null,"base_total_paid":159,"base_total_qty_ordered":null,"base_total_refunded":null,"discount_amount":"0.0000","discount_canceled":null,"discount_invoiced":0,"discount_refunded":null,"grand_total":"159.0000","shipping_amount":"5.0000","shipping_canceled":null,"shipping_invoiced":5,"shipping_refunded":null,"shipping_tax_amount":"0.0000","shipping_tax_refunded":null,"store_to_base_rate":"1.0000","store_to_order_rate":"1.0000","subtotal":"154.0000","subtotal_canceled":null,"subtotal_invoiced":154,"subtotal_refunded":null,"tax_amount":"0.0000","tax_canceled":null,"tax_invoiced":0,"tax_refunded":null,"total_canceled":null,"total_invoiced":159,"total_offline_refunded":null,"total_online_refunded":null,"total_paid":159,"total_qty_ordered":"1.0000","total_refunded":null,"can_ship_partially":null,"can_ship_partially_item":null,"customer_is_guest":"1","customer_note_notify":false,"billing_address_id":"436","customer_group_id":"0","edit_increment":null,"email_sent":"1","forced_shipment_with_invoice":null,"payment_auth_expiration":null,"quote_address_id":null,"quote_id":"708","shipping_address_id":"437","adjustment_negative":null,"adjustment_positive":null,"base_adjustment_negative":null,"base_adjustment_positive":null,"base_shipping_discount_amount":"0.0000","base_subtotal_incl_tax":"154.0000","base_total_due":null,"payment_authorization_amount":null,"shipping_discount_amount":"0.0000","subtotal_incl_tax":"154.0000","total_due":null,"weight":"1.0000","customer_dob":null,"increment_id":"*********","applied_rule_ids":null,"base_currency_code":"USD","customer_email":"<EMAIL>","customer_firstname":"Mohammed","customer_lastname":"Sami","customer_middlename":"A","customer_prefix":null,"customer_suffix":null,"customer_taxvat":null,"discount_description":null,"ext_customer_id":null,"ext_order_id":null,"global_currency_code":"USD","hold_before_state":null,"hold_before_status":null,"order_currency_code":"USD","original_increment_id":null,"relation_child_id":null,"relation_child_real_id":null,"relation_parent_id":null,"relation_parent_real_id":null,"remote_ip":"**************","shipping_method":"flatrate_flatrate","store_currency_code":"USD","store_name":"Main Website\nMadison Island\nEnglish","x_forwarded_for":null,"customer_note":null,"created_at":"2018-07-27 09:14:57","updated_at":"2018-07-27 09:15:22","total_item_count":"1","customer_gender":null,"hidden_tax_amount":"0.0000","base_hidden_tax_amount":"0.0000","shipping_hidden_tax_amount":"0.0000","base_shipping_hidden_tax_amnt":"0.0000","hidden_tax_invoiced":0,"base_hidden_tax_invoiced":0,"hidden_tax_refunded":null,"base_hidden_tax_refunded":null,"shipping_incl_tax":"5.0000","base_shipping_incl_tax":"5.0000","coupon_rule_name":null,"paypal_ipn_customer_notified":"0","gift_message_id":null,"base_customer_balance_amount":null,"customer_balance_amount":null,"base_customer_balance_invoiced":null,"customer_balance_invoiced":null,"base_customer_balance_refunded":null,"customer_balance_refunded":null,"bs_customer_bal_total_refunded":null,"customer_bal_total_refunded":null,"gift_cards":null,"base_gift_cards_amount":null,"gift_cards_amount":null,"base_gift_cards_invoiced":null,"gift_cards_invoiced":null,"base_gift_cards_refunded":null,"gift_cards_refunded":null,"gw_id":null,"gw_allow_gift_receipt":null,"gw_add_card":null,"gw_base_price":null,"gw_price":null,"gw_items_base_price":null,"gw_items_price":null,"gw_card_base_price":null,"gw_card_price":null,"gw_base_tax_amount":null,"gw_tax_amount":null,"gw_items_base_tax_amount":null,"gw_items_tax_amount":null,"gw_card_base_tax_amount":null,"gw_card_tax_amount":null,"gw_base_price_invoiced":null,"gw_price_invoiced":null,"gw_items_base_price_invoiced":null,"gw_items_price_invoiced":null,"gw_card_base_price_invoiced":null,"gw_card_price_invoiced":null,"gw_base_tax_amount_invoiced":null,"gw_tax_amount_invoiced":null,"gw_items_base_tax_invoiced":null,"gw_items_tax_invoiced":null,"gw_card_base_tax_invoiced":null,"gw_card_tax_invoiced":null,"gw_base_price_refunded":null,"gw_price_refunded":null,"gw_items_base_price_refunded":null,"gw_items_price_refunded":null,"gw_card_base_price_refunded":null,"gw_card_price_refunded":null,"gw_base_tax_amount_refunded":null,"gw_tax_amount_refunded":null,"gw_items_base_tax_refunded":null,"gw_items_tax_refunded":null,"gw_card_base_tax_refunded":null,"gw_card_tax_refunded":null,"reward_points_balance":null,"base_reward_currency_amount":null,"reward_currency_amount":null,"base_rwrd_crrncy_amt_invoiced":null,"rwrd_currency_amount_invoiced":null,"base_rwrd_crrncy_amnt_refnded":null,"rwrd_crrncy_amnt_refunded":null,"reward_points_balance_refund":null,"reward_points_balance_refunded":null,"reward_salesrule_points":null,"payment_authorization_expiration":null,"forced_do_shipment_with_invoice":null,"base_shipping_hidden_tax_amount":"0.0000","shipping_tax_invoiced":0,"base_shipping_tax_invoiced":0,"is_in_process":true},"OrderShipping":{"entity_id":"437","parent_id":"222","customer_address_id":null,"quote_address_id":null,"region_id":null,"customer_id":null,"fax":null,"region":null,"postcode":"75300","lastname":"Sami","street":"A-160 Block 3, Gulshan-e-Iqbal\nA-160 Block 3, Gulshan-e-Iqbal","city":"Karachi, Sindh","email":"<EMAIL>","telephone":"+923458225212","country_id":"PK","firstname":"Mohammed","address_type":"shipping","prefix":null,"middlename":"A","suffix":null,"company":"TCS ECOM","vat_id":null,"vat_is_valid":null,"vat_request_id":null,"vat_request_date":null,"vat_request_success":null,"giftregistry_item_id":null},"OrderPayment":"Cash On Delivery"}';
    //return $request;
    $hook = json_decode($hook);
    //var_dump($hook->items );die;
    /*   foreach($hook->items as $item)
       {
           $pOptions = unserialize($item->product_options);
           // Loops through each element. If element again is array, function is recalled. If not, result is echoed.
           $ss="";
           traverseArray($pOptions, $ss);
           echo $ss;

       }
       die;*/
    try {
        $order = new Order();
        $order->source_id = 1;
        $order->marketplace_id;
        $order->seller_location_id = 1;
        $order->marketplace_reference_id = ($hook->OrderData->increment_id ? $hook->OrderData->increment_id : 'Not Present');
        $order->seller_id = \Session::get('seller_id');
        $order->created_date = date('Y-m-d H:i:s');
        $order->customer_name = ($hook->OrderShipping->firstname ? $hook->OrderShipping->firstname : 'Not Present').' '.($hook->OrderShipping->middlename ? $hook->OrderShipping->middlename : ' ').' '.($hook->OrderShipping->lastname ? $hook->OrderShipping->lastname : ' ');
        $order->customer_email = ($hook->OrderData->customer_email ? $hook->OrderData->customer_email : 'Not Present');
        $order->customer_number = ($hook->OrderShipping->telephone ? str_replace('+92', '0', $hook->OrderShipping->telephone) : 'Not Present');
        $order->customer_number = (substr($order->customer_number, 0, 2) == '92' ? '0'.substr($order->customer_number, 2) : $order->customer_number);

        $hook->OrderShipping->city = str_replace("'", '', $hook->OrderShipping->city);
        $hook->OrderShipping->street = str_replace("'", '', $hook->OrderShipping->street);

        $destintion_city = City::where('name', $hook->OrderShipping->city)->first();
        $order->destination_city = ($hook->OrderShipping->city ? ($destintion_city ? $destintion_city->name : $hook->OrderShipping->city) : 'Not Present');
        $order->shipping_address = ($hook->OrderShipping->street ? $hook->OrderShipping->street : ' ').' '.$order->destination_city;
        $order->discount = ($hook->OrderData->base_discount_amount ? $hook->OrderData->base_discount_amount : 0);
        //TODO Change this Hack once Tarzz is gone -- Tarzz Hack
        if ($order->seller_id == '77') {
            $order->grand_total = ($hook->OrderData->base_grand_total ? $hook->OrderData->base_grand_total : 0);
            $order->total_invoiced = ($hook->OrderData->base_grand_total ? $hook->OrderData->base_grand_total : 0);
        } else {
            // this is done because these parameters were wrong before
            $order->grand_total = ($hook->OrderData->base_grand_total ? $hook->OrderData->base_grand_total : 0);
            $order->total_invoiced = ($hook->OrderData->base_grand_total ? $hook->OrderData->base_grand_total : 0);
            // $order->grand_total = ($hook->OrderData->total_invoiced ? $hook->OrderData->total_invoiced : 0);
            // $order->total_invoiced = ($hook->OrderData->total_invoiced ? $hook->OrderData->total_invoiced : 0);
	    }
        //Tarzz Hack end 
        $order->cod_payment = ($hook->OrderPayment == 'Cash On Delivery' ? 1 : 0);
        $order->shipping_fee = ($hook->OrderData->base_shipping_amount ? $hook->OrderData->base_shipping_amount : 0);
        $order->status =  config('enum.order_status')['PENDING'];

        // $order->placement_date = $hook->OrderData->created_at ;
        $order->placement_date = Carbon::parse($hook->OrderData->created_at,'utc')->setTimezone(config('app.timezone'))->toDateTimeString() ;

        $payment_method_id = SellerPaymentMethod::where('seller_id',\Session::get('seller_id'))->where('machine_name',$hook->OrderPayment)->first();
        if($payment_method_id){
            $order->seller_payment_method_id = $payment_method_id->id;
            $order->cod_payment = ($payment_method_id->payment_on_delivery == 1 ? 1 : 0);                
        }

        $method_id = SellerShipmentMethod::where('seller_id',\Session::get('seller_id'))->where('machine_name',$hook->OrderData->shipping_description)->first();
        if($method_id){
            $order->seller_shipment_method_id = $method_id->id;
            
        }

        $order->currency = ($hook->OrderData->base_currency_code ? $hook->OrderData->base_currency_code : 'PKR');
        $order->customer_number = str_replace(' ','',$order->customer_number);
        $isOrderSaved = $order->save();
        $concatinated_customer_details = $order->customer_number."|".$order->customer_email;

        //dd($request->items);
        foreach ($hook->items as $item) {
            $orderItem = new OrderItem();
            $orderItem->order_id = $order->id;
            //This to cater if product created on shopify is also on Unity - realised when working on FFC
            $item_sku = ( $item->sku ?  $item->sku : '');
            $product = Product::where('seller_id',\Session::get('seller_id'))->where('SKU', $item_sku)->select('id')->first();
            if($product){
                $orderItem->product_id = $product->id;
            }else{
                $orderItem->product_id = ($item->product_id ? $item->product_id : 'Not Present');
            }

            $orderItem->product_name = ($item->name ? $item->name : 'Not Present');
            $orderItem->SKU = ($item->sku ? $item->sku : 'Not Present');

            $pOptions_text = '';
            $pOptions = unserialize($item->product_options);

            if (array_key_exists('options', $pOptions)) {
                foreach ($pOptions['options'] as $pOption) {
                    $pOptions_text .= $pOption['label'].' = '.$pOption['value'];
                }
            }

            if (array_key_exists('attributes_info', $pOptions)) {
                $pOptions_text .= ' ';

                foreach ($pOptions['attributes_info'] as $pAttribute) {
                    $pOptions_text .= $pAttribute['label'].' = '.$pAttribute['value'];
                }
            }

            //dd($pOptions_text);

            //traverseArray($pOptions, $ss);
            $orderItem->description = $pOptions_text;

            $orderItem->description2;
            $orderItem->description3;
            $orderItem->seller_unique_code = ($item->product_id ? $item->product_id : 'Not Present');
            $orderItem->material_of_product;
            $orderItem->cost = $item->base_cost == null ? 0 : $item->base_cost;

            
            /// For Discount
            $discount = $item->original_price - $item->price;
            if ($discount) {
                $discount = $discount * $item->qty_ordered;
            }
            $orderItem->discount = $discount + ($item->base_discount_amount ? $item->base_discount_amount : 0);

            $orderItem->unit_price = ($item->original_price ? $item->original_price : 0);
            $orderItem->quantity = ($item->qty_ordered ? $item->qty_ordered : 0);
            $orderItem->sub_total = ($orderItem->unit_price * $item->qty_ordered) - $orderItem->discount;
            $orderItem->weight = ($item->weight ? $item->weight : 0);
            $orderItem->status = config('enum.item_status')['PENDING'];
            $orderItem->reason = "";

            $orderItem->tax = ($item->tax_amount ? $item->tax_amount : 0) + (isset($item->hidden_tax_amount) ? $item->hidden_tax_amount : 0);
            $orderItem->tax_rate = ($item->tax_percent ? $item->tax_percent : 0);
            $orderItem->tax_rate = ($item->tax_percent / 100);

            $orderItem->save();
            $order_skus[] = $orderItem->SKU;
        }
        
        OrderComments::add($order->id, 'Order Dropped Process', '#'.$order->marketplace_reference_id.' Order Dropped from website source <b>'.$request->header('X-Origin-Url').' (Magento 1.9)</b>' , 'Success', '1');

        sort($order_skus);
        $concatinated_customer_details .= "|".implode('|', $order_skus);
        $concatinated_customer_details_md5_hash = md5($concatinated_customer_details);
        
        $order->order_hash = $concatinated_customer_details_md5_hash;
        $order->is_created = 1;
        $order->save();

        //check for order duplication
        $order_duplicate = OmniEngineHoldReason::checkForOrderDuplication($order);

        
        // if(Setting::where('seller_id', \Session::get('seller_id'))->where('key',config('enum.add_ons')['AUTOROBOCALL'])->value('value')){
        //     $client = new \GuzzleHttp\Client(['verify' => false]);
        //     $response = $client->post(env('APP_URL').'/api/robocall', [
        //         'http_errors' => FALSE,
        //         'form_params' => [
        //             'ids' => [$order->id]
        //         ]
        //     ]);
        // }
        // if(AddOn::where('seller_id', $order->seller_id)->where('key',config('enum.add_ons')['CUSTOMERORDERCONFIRMATION'])->where('value',1)->exists() && Setting::where('seller_id', $order->seller_id)->where('key',config('enum.settings')['CUSTOMERORDERCONFIRMATION'])->where('value',1)->exists()){
        //     CustomerOrderConfirmation::getUrl($order->id,$order->customer_number);
        // }
        $add_on_auto_confirm = \App\Models\AddOn::where('seller_id',$order->seller_id)->where('key',config('enum.add_ons')['AUTOORDERCONFIRMATION'])->where('value','1')->first();
        if($add_on_auto_confirm && !$order_duplicate)
            CustomerOrderConfirmation::autoConfirmProcess($order);
        
        AutoShippingMethodBook::addTag($order);

        event(new AddressValidationLikelihoodEvent($order));


        AutoShipped::auto_shipped_created_order($order->id);
        $order->passthrough();

        //Omni Location Assignment Item wise
        if(AddOn::omniLocationAssignment($order->seller_id)){
            $olDeterminationTrig = Setting::where('seller_id', $order->seller_id)->where('key', config('enum.settings')['OMNILOCATIONDETERMINATIONTRIGGER'])->first();
            if (isset($olDeterminationTrig) && $olDeterminationTrig->value == 'order_creation') {
                $order->assignLocationToOrderItems();
            }
        }


        //Single Auto Location Auto Assingment
        if(!AddOn::omniLocationAssignment($order->seller_id)){
            $assign_orders_to_ffc_setting = Setting::where('seller_id', $order->seller_id)->where('key', config('enum.settings')['ASSIGN-ORDER-TO-LOCATION'])->first();
            if (isset($assign_orders_to_ffc_setting)) {
                if ($assign_orders_to_ffc_setting->value == 'order_creation') {
                    $order->assignSingleLocation();
                }
            }
        }

        \Log::info('Magento 1.9 | '.$order->id.' | Order Created');

        return 'good';
    } catch (Exception $e) {
        \Log::info('Magento 1.9 | '.$e->getMessage());
        echo 'Message: '.$e->getMessage();
        $activity_id = activity()
                        ->causedBy(\Session::get('seller_id'))
                        ->withProperties(['response' => $e->getMessage(), 'dump' => json_encode($request->all())])
                        ->log('Magento');
        Mail::to('<EMAIL>')->send(new IntegrationError($activity_id, 'Magento'));
    }
})->middleware('mg19apiauth');

Route::post('shopify/order', function (Request $request) {
    try {
        $hook = $request;

        $shopify_product_ids = '';    
        $order_item_variant_ids = [];  
        
        $marketplace_reference_id = ltrim($hook->name, '#');
        \Log::info("Shopify | Reference_id : ".$marketplace_reference_id." | first ");
        $seller_id = \Session::get('seller_id');
        \Log::info("Shopify | Reference_id : ".$marketplace_reference_id." | Seller ID : ".$seller_id." | Topic : ".\Session::get('topic'));
        $order_check = Order::onWriteConnection()->where('marketplace_reference_id', $marketplace_reference_id)->where('seller_id', $seller_id)->count();
        \Log::info("Shopify | Reference_id : ".$marketplace_reference_id." | Seller ID : ".$seller_id." | Topic : ".\Session::get('topic')." | Count : ".$order_check);
        $order_check_temp = Order::where('marketplace_reference_id', $marketplace_reference_id)->where('seller_id', $seller_id)->count();
        \Log::info("Shopify | Reference_id : ".$marketplace_reference_id." | Count : ".$order_check." | Temp Count : ".$order_check_temp." | Seller ID : ".$seller_id." | Topic : ".\Session::get('topic'));
        
        if(\Session::get('topic') == 'create'){
            if($order_check == 0)  {
                $order = new Order();
                $order->source_id = 1;
                $order->marketplace_id = $hook->id;
                $order->seller_location_id = 1; 
                $order->marketplace_reference_id = $marketplace_reference_id;
                $order->seller_id = $seller_id;
                $order->created_date = date('Y-m-d H:i:s');

                $order->customer_reference_id = ( isset($hook->customer['id']) && $hook->customer['id'] != "" ? $hook->customer['id'] : Null);

                $order->customer_email = ( isset($hook->customer['email']) && $hook->customer['email'] != "" ? $hook->customer['email'] : Null);
                $order->customer_number = ( substr( $order->customer_number, 0, 2 ) == "92" ? "0".substr( $order->customer_number, 2) : $order->customer_number );
                
                $customer_name = '';
                
                if(!isset($hook->shipping_address) && isset($hook->billing_address)){
                    $customer_name .= $hook->billing_address['first_name'].' ';
                    $customer_name .= $hook->billing_address['last_name'];

                    $order->customer_number = ( isset($hook->billing_address['phone']) && $hook->billing_address['phone'] != ""  ? str_replace("+92","0",str_replace(" ","",$hook->billing_address['phone'])) : Null);
                    $destintion_city = City::where('name',$hook->billing_address['city'])->first();
                    $order->destination_city = (  isset($hook->billing_address['city']) && $hook->billing_address['city'] != "" ? ( $destintion_city ? $destintion_city->name : $hook->billing_address['city'] ) : Null);
                    $order->shipping_address = (  isset($hook->billing_address['address1']) && $hook->billing_address['address1'] != "" ? $hook->billing_address['address1'] . " " . $hook->billing_address['address2'] : ' ').' '.$order->destination_city;
                    $order->postal_code = ( isset($hook->billing_address['zip']) ? $hook->billing_address['zip'] : Null );
                    $order->country = ( isset($hook->billing_address['country_code']) && $hook->billing_address['country_code'] != "" ? $hook->billing_address['country_code'] : 'PK' );
                }else {

                    $customer_name .= ( isset($hook->shipping_address['first_name']) ? $hook->shipping_address['first_name'].' ' : "");
                    $customer_name .= ( isset($hook->shipping_address['last_name']) ? $hook->shipping_address['last_name'] : "");

                    $order->customer_number = ( isset($hook->shipping_address['phone']) && $hook->shipping_address['phone'] != "" ? str_replace("+92","0",str_replace(" ","",$hook->shipping_address['phone'])) : Null);
                    $temp_city = ( isset($hook->shipping_address['city']) && $hook->shipping_address['city'] != ""  ? $hook->shipping_address['city'] : Null);
                    $destintion_city = City::where('name',$temp_city)->first();
                    $order->destination_city = ( isset($hook->shipping_address['city']) && $hook->shipping_address['city'] != ""  ? ( $destintion_city ? $destintion_city->name : $hook->shipping_address['city'] ) : Null);
                    $order->shipping_address = ( isset($hook->shipping_address['address1']) && $hook->shipping_address['address1'] != "" ? $hook->shipping_address['address1'] . " " . $hook->shipping_address['address2'] : ' ').' '.$order->destination_city;
                    $order->postal_code = ( isset($hook->shipping_address['zip']) ? $hook->shipping_address['zip'] : Null );
                    $order->country = ( isset($hook->shipping_address['country_code']) && $hook->shipping_address['country_code'] != "" ? $hook->shipping_address['country_code'] : 'PK' );
                }
                $order->customer_name = $customer_name;
                $order->discount = ( $hook->total_discounts ? $hook->total_discounts : 0 );
                $order->grand_total = $hook->total_price;

                if(!isset($hook->gateway)){

                    if (isset($hook->payment_gateway_names) && isset($hook->payment_gateway_names[0])) {
                        $payment_gateway = $hook->payment_gateway_names[0];
                    } else {
                        $payment_gateway = "NO_PAYMENT_GATEWAY";
                    }
                    
                } else {
                    $payment_gateway = $hook->gateway;
                }

                $order->cod_payment = ($payment_gateway == 'Cash on Delivery (COD)'? 1 : 0) ;
                $order->shipping_fee = ( isset($hook->shipping_lines[0]['price']) && $hook->shipping_lines[0]['price'] != "" ? $hook->shipping_lines[0]['price'] : 0 );
                
                $order->status =  config('enum.order_status')['PENDING'];

                // $order->placement_date = Carbon::parse($hook->created_at)->toDateTimeString() ;
                $order->placement_date = Carbon::parse($hook->created_at)->setTimezone(config('app.timezone'))->toDateTimeString() ;


                $payment_method_id = SellerPaymentMethod::where('seller_id',$seller_id)->where('machine_name',$payment_gateway)->first();
                if($payment_method_id){
                    $order->seller_payment_method_id = $payment_method_id->id;
                    $order->cod_payment = ($payment_method_id->payment_on_delivery == 1 ? 1 : 0);                
                }

                foreach($hook->shipping_lines as $line)
                {
                    $method_id = SellerShipmentMethod::where('seller_id',$seller_id)->where('machine_name',$line['title'])->first();
                    if($method_id){
                        $order->seller_shipment_method_id = $method_id->id;
                       
                    }
               
                    
                }

                //This is to cater FLH-1546
                if(AddOn::fbr($seller_id) && $order->country == "PK"){
                    $fbr_pos_fee_conf = Setting::where('seller_id', $seller_id)->where('key',config('enum.fbr')['FBR_POS_FEE_CONF']);
                    if($fbr_pos_fee_conf->exists() && $fbr_pos_fee_conf->value('value') == config('enum.fbr')['FBR_POS_FEE_OPTION_1']){
                        if($order->shipping_fee >= 1){
                            $order->shipping_fee = $order->shipping_fee - 1;
                            $order->fbr_tax = 1;
                        }
                    }
                }

                $order->customer_number = str_replace(' ','',$order->customer_number);
                $order->is_created = 0;

                $voucher_message = "";
                if(isset($hook->discount_applications)){
                    \Log::info($hook->discount_applications);
                    if (count($hook->discount_applications) > 0) {
                        foreach ($hook->discount_applications as $value) {
                            if($value['type'] == "discount_code"){
                                $order->voucher = $value['code'];
                                $voucher_message =  ", ".$value['code']." as discount voucher code";
                                //this is to identify if the voucher code was order wise discount or order wise 
                                //this was requested during gtech integration for pos insert
                                if($value['target_selection'] == "all"){
                                    $order->is_voucher_order_wise = 1;
                                }

                                if($value['target_selection'] == "entitled"){
                                    $order->is_voucher_order_wise = 2; // 2 mean voucher is product voucher
                                }
                            }
                            //this is to address voucher added through shopify backend
                            if($value['type'] == "manual"){
                                $order->voucher = $value['title'];
                                $voucher_message =  ", ".$value['title']." as discount voucher code";
                                //this is to identify if the voucher code was order wise discount or order wise 
                                //this was requested during gtech integration for pos insert
                                if($value['target_selection'] == "all"){
                                    $order->is_voucher_order_wise = 1;
                                }
                            }
                        }
                    }
                }

                $order->merchant_tags =isset($hook->tags) && $hook->tags != "" ? $hook->tags : Null;


                $isOrderSaved=$order->save();

                $concatinated_customer_details = $order->customer_number."|".$order->customer_email;
                //dd($request->items);
                foreach($hook->line_items as $item)
                {
                    $orderItem = new OrderItem();
                    $orderItem->order_id = $order->id;
                    $orderItem->marketplace_id = $item['id'];
                    //This to cater if product created on shopify is also on Unity - realised when working on FFC
                    $item_sku = ($item['sku'] ? $item['sku'] : '');
                    // $product = Product::where('seller_id',$seller_id)->where('SKU', $item_sku)->select('id')->first();
                    // if($product){
                    //     $orderItem->product_id = $product->id;
                    // }else{
                    //     $orderItem->product_id = $item['product_id'];
                    // }
                    $orderItem->product_id =  '';
                    $orderItem->product_name = $item['name'];
                    $orderItem->SKU = $item['sku'];
                    $orderItem->description = $item['name'];
                    $orderItem->description2;
                    $orderItem->description3;
                    $orderItem->seller_unique_code = $item['product_id'];
                    $orderItem->material_of_product;
                    $orderItem->cost = ( $item['price'] ? $item['price'] : 0 );
                    $orderItem->actual_price = ( $item['price'] ? $item['price'] : 0 );


                    /// For Discount
                    $discount = 0;
                    if (count($item['discount_allocations']) > 0) {
                        foreach ($item['discount_allocations'] as $value) {
                            $discount += $value['amount'];
                        }
                    }
                    $orderItem->discount = $discount;


                    /// For Tax
                    $tax = 0;
                    if (count($item['tax_lines']) > 0) {
                        foreach ($item['tax_lines'] as $value) {
                            $tax += $value['price'];
                        }
                        $orderItem->tax = $tax;
                        $tax = $tax / $item['quantity'];
                    }


                    /// For Unit Price
                    if($hook->taxes_included) {
                        $orderItem->unit_price = $item['price']-$tax;
                    } else {
                        $orderItem->unit_price = $item['price'];
                    }
                    
                    
                    $orderItem->quantity = $item['quantity'];
                    $orderItem->sub_total = ($orderItem->unit_price * $item['quantity']) - $discount;
                    $orderItem->weight = ( $item['grams'] ? $item['grams']/1000 : $item['grams'] );
                    $orderItem->status = config('enum.item_status')['PENDING'];
                    $orderItem->reason = "";
                    $orderItem->tax_rate = ( isset($item['tax_lines'][0]['rate']) ? $item['tax_lines'][0]['rate'] : 0 );
                    $orderItem->save();

                    $shopify_product_ids .= ($shopify_product_ids ? ',' : '').$item['product_id'];
                    $order_item_variant_ids[] = ['variant_id' => $item['variant_id'], 'order_item_id' => $orderItem->id ];
                    $order_skus[] = $orderItem->SKU;
                }
                OrderComments::add($order->id, 'Order Dropped Process', '#'.$order->marketplace_reference_id.' Order Dropped from website source <b>'.$request->header('X-Shopify-Shop-Domain').' (Shopify)</b> with <b>'.( $payment_gateway ).'</b> as its payment gateway'.$voucher_message.' and <b>'.$hook->total_outstanding.'</b> is its outstanding amount' , 'Success', '1');
                if(AddOn::where('seller_id', $seller_id)->where('key',"autoCorrectCities")->where('value',1)->exists()){
                    if($order->destination_city){
                        $oldCity = $order->destination_city;
                        $order->destination_city  =  App\Service\AutoCorrectCities\AutoCorrectCitiesService::findCorrectCity($order->destination_city);
                        if ($oldCity != $order->destination_city)
                            OrderComments::add($order->id, 'Auto Correct Destination City', $oldCity . ' auto changed to ' . $order->destination_city , 'Success', 1);
                    }
                }
                sort($order_skus);
                $concatinated_customer_details .= "|".implode('|', $order_skus);
                $concatinated_customer_details_md5_hash = md5($concatinated_customer_details);
                
                $order->order_hash = $concatinated_customer_details_md5_hash;
                $order->is_created = 1;
                $order->save();



                /// Adding Note into Comments
                if (isset($hook->note) && $hook->note) {
                    OrderComments::add($order->id, 'Order Customer Note', $hook->note, 'Success', '1');
                }

                /// Adding additional note into comments
                $note_attributes = "";
                if (isset($hook->note_attributes) && $hook->note_attributes) {

                    foreach ($hook->note_attributes as $value) {
                        $note_attributes .= $value['value']."<br>";
                    }

                    OrderComments::add($order->id, 'Order Additional Customer Note', $note_attributes, 'Success', '1');
                }



                //check for order duplication
                $order_duplicate = OmniEngineHoldReason::checkForOrderDuplication($order);

                $fetch_barcodes_from_storefront = Setting::where('seller_id', $order->seller_id)->where('key', config('enum.settings')['FETCH-BARCODES-STOREFRONT'])->first();
                if (isset($fetch_barcodes_from_storefront) && $fetch_barcodes_from_storefront->value == '1') {
                    /// GET ORDER ITEM BARCODE
                    $shopify = new ShopifyApp();
                    $order_item_barcode_creation_result = $shopify->getOrderItemBarcode($order->seller_id, $shopify_product_ids, $order_item_variant_ids);

                    if ($order_item_barcode_creation_result['error']) {

                        // if (in_array($order_item_barcode_creation_result['message'], ['Shopify access token not found'])) {

                            event(new ShopifyOrderItemBarcodeEvent($order->seller_id, $order->id, $shopify_product_ids, $order_item_variant_ids));
                            Log::critical($order_item_barcode_creation_result['message'].' | Seller ID : '.$order->seller_id.' | Shopify Product ID : '. $shopify_product_ids);                    
                        // } 
                    }
                }
                

                // if(Setting::where('seller_id', $seller_id)->where('key',config('enum.add_ons')['AUTOROBOCALL'])->value('value')){
                //     $client = new \GuzzleHttp\Client(['verify' => false]);
                //     $response = $client->post(env('APP_URL').'/api/robocall', [
                //         'http_errors' => FALSE,
                //         'form_params' => [
                //             'ids' => [$order->id]
                //         ]
                //     ]);
                // }
                // return $order->seller_id;
                // if(AddOn::where('seller_id', $order->seller_id)->where('key',config('enum.add_ons')['CUSTOMERORDERCONFIRMATION'])->where('value',1)->exists() && Setting::where('seller_id', $order->seller_id)->where('key',config('enum.settings')['CUSTOMERORDERCONFIRMATION'])->where('value',1)->exists()){
                //     // return'sad';
                //     CustomerOrderConfirmation::getUrl($order->id,$order->customer_number);
                // }
                $add_on_auto_confirm = \App\Models\AddOn::where('seller_id',$order->seller_id)->where('key',config('enum.add_ons')['AUTOORDERCONFIRMATION'])->where('value','1')->first();
                if($add_on_auto_confirm && !$order_duplicate)
                    CustomerOrderConfirmation::autoConfirmProcess($order);
                
                AutoShippingMethodBook::addTag($order);
                
                AutoShipped::auto_shipped_created_order($order->id);
                $order->passthrough();

                //Omni Location Assignment Item wise
                if(AddOn::omniLocationAssignment($order->seller_id)){
                    $olDeterminationTrig = Setting::where('seller_id', $order->seller_id)->where('key', config('enum.settings')['OMNILOCATIONDETERMINATIONTRIGGER'])->first();
                    if (isset($olDeterminationTrig) && $olDeterminationTrig->value == 'order_creation') {
                        $order->assignLocationToOrderItems();
                    }
                }

                //Single Auto Location Auto Assingment
                if(!AddOn::omniLocationAssignment($order->seller_id)){
                    $assign_orders_to_ffc_setting = Setting::where('seller_id', $order->seller_id)->where('key', config('enum.settings')['ASSIGN-ORDER-TO-LOCATION'])->first();
                    if (isset($assign_orders_to_ffc_setting)) {
                        if ($assign_orders_to_ffc_setting->value == 'order_creation') {
                            $order->assignSingleLocation();
                        }
                    }
                }

                \Log::info("Shopify | ".$order->id." | Order Created");

                event(new AddressValidationLikelihoodEvent($order));
                // event(new ShopifyOrderItemBarcodeEvent($order->seller_id, $order->id, $shopify_product_ids, $order_item_variant_ids));
                if($order->cod_payment == 0){
                    event(new ShopifyGetPaymentIdEvent($order));
                }

                if (Setting::crossSellerAccounts($order->seller_id)) {
                    event(new OrderCreatedEvent($order->seller_id, $order->id));
                }

                return "Order Created";
            } else  {
                \Log::info("Shopify | Duplicate Order");
                return "Duplicate Order";
            }
            
        }
        elseif(\Session::get('topic') == 'cancel'){
            return ShopifyApp::webhookCancellationOrder($seller_id, $hook, $marketplace_reference_id, $order_check);
        }
        elseif(\Session::get('topic') == 'update'){
            return ShopifyApp::webhookUpdateOrder($seller_id, $hook, $marketplace_reference_id, $order_check);
        }
    } catch (\Exception $e){
        Log::critical("Shopify | ".\Session::get('topic')." | ".$e->getMessage());
        Log::critical($e->getTraceAsString());
        $activity_id = activity()
                        ->causedBy(\Session::get('seller_id'))
                        ->withProperties(['response' => $e->getMessage(), 'dump' => json_encode($request->all())])
                        ->log('Shopify');
        Mail::to('<EMAIL>')->send(new IntegrationError($activity_id, 'Shopify '.\Session::get('topic')));

        if (str_contains($e->getMessage(), 'Incorrect string value')) {
            $code = 200;
        } else {
            $code = 500;
        }
        
        return response()->json(['error' => 1, 'message' => $e->getMessage()], $code);
    }
})->middleware('spapiauth');



Route::post('zeen/order', function (Request $request) {
    try {
        $hook = $request;

        $shopify_product_ids = '';    
        $order_item_variant_ids = [];  
        
        $marketplace_reference_id = ltrim($hook->name, '#');
        $seller_id = \Session::get('seller_id');
        $order_check = Order::onWriteConnection()->where('marketplace_reference_id', $marketplace_reference_id)->where('seller_id', $seller_id)->count();
        $order_check_temp = Order::where('marketplace_reference_id', $marketplace_reference_id)->where('seller_id', $seller_id)->count();
        \Log::info("Zeen | Reference_id : ".$marketplace_reference_id." | Count : ".$order_check." | Temp Count : ".$order_check_temp." | Seller ID : ".$seller_id);
        
        if(\Session::get('topic') == 'create'){
            if($order_check == 0)  {
                $order = new Order();
                $order->source_id = 1;
                $order->marketplace_id = $hook->id;
                $order->seller_location_id = 1; 
                $order->marketplace_reference_id = $marketplace_reference_id;
                $order->seller_id = $seller_id;
                $order->created_date = date('Y-m-d H:i:s');

                $order->customer_reference_id = ( isset($hook->customer['id']) && $hook->customer['id'] != "" ? $hook->customer['id'] : Null);

                $order->customer_email = ( isset($hook->customer['email']) && $hook->customer['email'] != "" ? $hook->customer['email'] : Null);
                $order->customer_number = ( substr( $order->customer_number, 0, 2 ) == "92" ? "0".substr( $order->customer_number, 2) : $order->customer_number );
                
                $customer_name = '';
                
                if(!isset($hook->shipping_address) && isset($hook->billing_address)){
                    $customer_name .= $hook->billing_address['first_name'].' ';
                    $customer_name .= $hook->billing_address['last_name'];

                    $order->customer_number = ( isset($hook->billing_address['phone']) && $hook->billing_address['phone'] != ""  ? str_replace("+92","0",str_replace(" ","",$hook->billing_address['phone'])) : Null);
                    $destintion_city = City::where('name',$hook->billing_address['city'])->first();
                    $order->destination_city = (  isset($hook->billing_address['city']) && $hook->billing_address['city'] != "" ? ( $destintion_city ? $destintion_city->name : $hook->billing_address['city'] ) : Null);
                    $order->shipping_address = (  isset($hook->billing_address['address1']) && $hook->billing_address['address1'] != "" ? $hook->billing_address['address1'] . " " . $hook->billing_address['address2'] : ' ').' '.$order->destination_city;
                    $order->postal_code = ( isset($hook->billing_address['zip']) ? $hook->billing_address['zip'] : Null );
                    $order->country = ( isset($hook->billing_address['country_code']) && $hook->billing_address['country_code'] != "" ? $hook->billing_address['country_code'] : 'PK' );
                }else {

                    $customer_name .= ( isset($hook->shipping_address['first_name']) ? $hook->shipping_address['first_name'].' ' : "");
                    $customer_name .= ( isset($hook->shipping_address['last_name']) ? $hook->shipping_address['last_name'] : "");

                    $order->customer_number = ( isset($hook->shipping_address['phone']) && $hook->shipping_address['phone'] != "" ? str_replace("+92","0",str_replace(" ","",$hook->shipping_address['phone'])) : Null);
                    $temp_city = ( isset($hook->shipping_address['city']) && $hook->shipping_address['city'] != ""  ? $hook->shipping_address['city'] : Null);
                    $destintion_city = City::where('name',$temp_city)->first();
                    $order->destination_city = ( isset($hook->shipping_address['city']) && $hook->shipping_address['city'] != ""  ? ( $destintion_city ? $destintion_city->name : $hook->shipping_address['city'] ) : Null);
                    $order->shipping_address = ( isset($hook->shipping_address['address1']) && $hook->shipping_address['address1'] != "" ? $hook->shipping_address['address1'] . " " . $hook->shipping_address['address2'] : ' ').' '.$order->destination_city;
                    $order->postal_code = ( isset($hook->shipping_address['zip']) ? $hook->shipping_address['zip'] : Null );
                    $order->country = ( isset($hook->shipping_address['country_code']) && $hook->shipping_address['country_code'] != "" ? $hook->shipping_address['country_code'] : 'PK' );
                }
                $order->customer_name = $customer_name;
                $order->discount = ( $hook->total_discounts ? $hook->total_discounts : 0 );
                $order->grand_total = $hook->total_price;

                if(!isset($hook->gateway)){

                    if (isset($hook->payment_gateway_names) && isset($hook->payment_gateway_names[0])) {
                        $payment_gateway = $hook->payment_gateway_names[0];
                    } else {
                        $payment_gateway = "NO_PAYMENT_GATEWAY";
                    }
                    
                } else {
                    $payment_gateway = $hook->gateway;
                }

                $order->cod_payment = ($payment_gateway == 'Cash on Delivery (COD)'? 1 : 0) ;
                $order->shipping_fee = ( isset($hook->shipping_lines[0]['price']) && $hook->shipping_lines[0]['price'] != "" ? $hook->shipping_lines[0]['price'] : 0 );
                
                $order->status =  config('enum.order_status')['PENDING'];

                // $order->placement_date = Carbon::parse($hook->created_at)->toDateTimeString() ;
                $order->placement_date = Carbon::parse($hook->created_at)->setTimezone(config('app.timezone'))->toDateTimeString() ;


                $payment_method_id = SellerPaymentMethod::where('seller_id',$seller_id)->where('machine_name',$payment_gateway)->first();
                if($payment_method_id){
                    $order->seller_payment_method_id = $payment_method_id->id;
                    $order->cod_payment = ($payment_method_id->payment_on_delivery == 1 ? 1 : 0);                
                }

                foreach($hook->shipping_lines as $line)
                {
                    $method_id = SellerShipmentMethod::where('seller_id',$seller_id)->where('machine_name',$line['title'])->first();
                    if($method_id){
                        $order->seller_shipment_method_id = $method_id->id;
                       
                    }
               
                    
                }

                //This is to cater FLH-1546
                if(AddOn::fbr($seller_id) && $order->country == "PK"){
                    $fbr_pos_fee_conf = Setting::where('seller_id', $seller_id)->where('key',config('enum.fbr')['FBR_POS_FEE_CONF']);
                    if($fbr_pos_fee_conf->exists() && $fbr_pos_fee_conf->value('value') == config('enum.fbr')['FBR_POS_FEE_OPTION_1']){
                        if($order->shipping_fee >= 1){
                            $order->shipping_fee = $order->shipping_fee - 1;
                            $order->fbr_tax = 1;
                        }
                    }
                }

                $order->customer_number = str_replace(' ','',$order->customer_number);
                $order->is_created = 0;

                $voucher_message = "";
                if(isset($hook->discount_applications)){
                    \Log::info($hook->discount_applications);
                    if (count($hook->discount_applications) > 0) {
                        foreach ($hook->discount_applications as $value) {
                            if($value['type'] == "discount_code"){
                                $order->voucher = $value['code'];
                                $voucher_message =  ", ".$value['code']." as discount voucher code";
                                //this is to identify if the voucher code was order wise discount or order wise 
                                //this was requested during gtech integration for pos insert
                                if($value['target_selection'] == "all"){
                                    $order->is_voucher_order_wise = 1;
                                }

                                if($value['target_selection'] == "entitled"){
                                    $order->is_voucher_order_wise = 2; // 2 mean voucher is product voucher
                                }
                            }
                        }
                    }
                }

                $isOrderSaved=$order->save();

                $concatinated_customer_details = $order->customer_number."|".$order->customer_email;
                foreach($hook->line_items as $item)
                {
                    $orderItem = new OrderItem();
                    $orderItem->order_id = $order->id;
                    $orderItem->marketplace_id = $item['id'];
                    //This to cater if product created on shopify is also on Unity - realised when working on FFC
                    $item_sku = ($item['sku'] ? $item['sku'] : '');
                    // $product = Product::where('seller_id',$seller_id)->where('SKU', $item_sku)->select('id')->first();
                    // if($product){
                    //     $orderItem->product_id = $product->id;
                    // }else{
                    //     $orderItem->product_id = $item['product_id'];
                    // }
                    $orderItem->product_id =  '';
                    $orderItem->product_name = $item['name'];
                    $orderItem->SKU = $item['sku'];
                    $orderItem->description = $item['name'];
                    $orderItem->description2;
                    $orderItem->description3;
                    $orderItem->seller_unique_code = $item['product_id'];
                    $orderItem->material_of_product;

                    $original_price = 0;
                    if (isset($item['properties']) && $item['properties']) {
                        
                        foreach ($item['properties'] as $value) {

                            if (isset($value['name']) && $value['name'] == 'Compare Price') {

                                $original_price = $value['value'];
                                break;
                            }
                        }
                    }

                    $orderItem->cost = ( $original_price ? $original_price : $item['price'] );
                    $orderItem->actual_price = ( $original_price ? $original_price : $item['price'] );


                    /// For Discount
                    $discount = 0;
                    if (count($item['discount_allocations']) > 0) {
                        foreach ($item['discount_allocations'] as $value) {
                            $discount += $value['amount'];
                        }
                    }

                    if ($original_price > $item['price']) {
                        $orderItem->item_discount = ($original_price - $item['price']) * ($item['quantity']);
                        $discount += $orderItem->item_discount;
                    }
                    $orderItem->discount = $discount;


                    /// For Tax
                    $tax = 0;
                    if (count($item['tax_lines']) > 0) {
                        foreach ($item['tax_lines'] as $value) {
                            $tax += $value['price'];
                        }
                        $orderItem->tax = $tax;
                        $tax = $tax / $item['quantity'];
                    }


                    /// For Unit Price
                    if($hook->taxes_included) {
                        $orderItem->unit_price = $orderItem->cost-$tax;
                    } else {
                        $orderItem->unit_price = $orderItem->cost;
                    }
                    
                    
                    $orderItem->quantity = $item['quantity'];
                    $orderItem->sub_total = ($orderItem->unit_price * $item['quantity']) - $discount;
                    $orderItem->weight = ( $item['grams'] ? $item['grams']/1000 : $item['grams'] );
                    $orderItem->status = config('enum.item_status')['PENDING'];
                    $orderItem->reason = "";
                    $orderItem->tax_rate = ( isset($item['tax_lines'][0]['rate']) ? $item['tax_lines'][0]['rate'] : 0 );
                    $orderItem->save();

                    $shopify_product_ids .= ($shopify_product_ids ? ',' : '').$item['product_id'];
                    $order_item_variant_ids[] = ['variant_id' => $item['variant_id'], 'order_item_id' => $orderItem->id ];
                    $order_skus[] = $orderItem->SKU;
                }
                OrderComments::add($order->id, 'Order Dropped Process', '#'.$order->marketplace_reference_id.' Order Dropped from website source <b>'.$request->header('X-Shopify-Shop-Domain').' (Shopify)</b> with <b>'.( $payment_gateway ).'</b> as its payment gateway'.$voucher_message.' and <b>'.$hook->total_outstanding.'</b> is its outstanding amount' , 'Success', '1');

                sort($order_skus);
                $concatinated_customer_details .= "|".implode('|', $order_skus);
                $concatinated_customer_details_md5_hash = md5($concatinated_customer_details);
                
                $order->order_hash = $concatinated_customer_details_md5_hash;
                $order->is_created = 1;
                $order->save();


                /// Adding Note into Comments
                if (isset($hook->note) && $hook->note) {
                    OrderComments::add($order->id, 'Order Customer Note', $hook->note, 'Success', '1');
                }

                /// Adding additional note into comments
                $note_attributes = "";
                if (isset($hook->note_attributes) && $hook->note_attributes) {

                    foreach ($hook->note_attributes as $value) {
                        $note_attributes .= $value['value']."<br>";
                    }

                    OrderComments::add($order->id, 'Order Additional Customer Note', $note_attributes, 'Success', '1');
                }

                //check for order duplication
                $order_duplicate = OmniEngineHoldReason::checkForOrderDuplication($order);



                /// GET ORDER ITEM BARCODE
                $shopify = new ShopifyApp();
                $order_item_barcode_creation_result = $shopify->getOrderItemBarcode($order->seller_id, $shopify_product_ids, $order_item_variant_ids);

                if ($order_item_barcode_creation_result['error']) {

                    // if (in_array($order_item_barcode_creation_result['message'], ['Shopify access token not found'])) {

                        event(new ShopifyOrderItemBarcodeEvent($order->seller_id, $order->id, $shopify_product_ids, $order_item_variant_ids));
                        Log::critical($order_item_barcode_creation_result['message'].' | Seller ID : '.$order->seller_id.' | Shopify Product ID : '. $shopify_product_ids);                    
                    // } 
                }

                $add_on_auto_confirm = \App\Models\AddOn::where('seller_id',$order->seller_id)->where('key',config('enum.add_ons')['AUTOORDERCONFIRMATION'])->where('value','1')->first();
                if($add_on_auto_confirm && !$order_duplicate)
                    CustomerOrderConfirmation::autoConfirmProcess($order);
                
                AutoShippingMethodBook::addTag($order);
                
                AutoShipped::auto_shipped_created_order($order->id);
                $order->passthrough();

                //Omni Location Assignment Item wise
                if(AddOn::omniLocationAssignment($order->seller_id)){
                    $olDeterminationTrig = Setting::where('seller_id', $order->seller_id)->where('key', config('enum.settings')['OMNILOCATIONDETERMINATIONTRIGGER'])->first();
                    if (isset($olDeterminationTrig) && $olDeterminationTrig->value == 'order_creation') {
                        $order->assignLocationToOrderItems();
                    }
                }


                //Single Auto Location Auto Assingment
                if(!AddOn::omniLocationAssignment($order->seller_id)){
                    $assign_orders_to_ffc_setting = Setting::where('seller_id', $order->seller_id)->where('key', config('enum.settings')['ASSIGN-ORDER-TO-LOCATION'])->first();
                    if (isset($assign_orders_to_ffc_setting)) {
                        if ($assign_orders_to_ffc_setting->value == 'order_creation') {
                            $order->assignSingleLocation();
                        }
                    }
                }

                \Log::info("Zeen | ".$order->id." | Order Created");

                event(new AddressValidationLikelihoodEvent($order));
                if($order->cod_payment == 0){
                    event(new ShopifyGetPaymentIdEvent($order));
                }
                return "Order Created";
            } else  {
                \Log::info("Zeen | Duplicate Order");
                return "Duplicate Order";
            }
            
        }
        elseif(\Session::get('topic') == 'cancel'){
            return ShopifyApp::webhookCancellationOrder($seller_id, $hook, $marketplace_reference_id, $order_check);
        }
        elseif(\Session::get('topic') == 'update'){
            return ShopifyApp::zeenWebhookUpdateOrder($seller_id, $hook, $marketplace_reference_id, $order_check);
        }
    } catch (\Exception $e){
        Log::critical("Zeen | ".\Session::get('topic')." | ".$e->getMessage());
        Log::critical($e->getTraceAsString());
        $activity_id = activity()
                        ->causedBy(\Session::get('seller_id'))
                        ->withProperties(['response' => $e->getMessage(), 'dump' => json_encode($request->all())])
                        ->log('Zeen');
        Mail::to('<EMAIL>')->send(new IntegrationError($activity_id, 'Zeen '.\Session::get('topic')));

        if (str_contains($e->getMessage(), 'Incorrect string value')) {
            $code = 200;
        } else {
            $code = 500;
        }
        
        return response()->json(['error' => 1, 'message' => $e->getMessage()], $code);
    }
})->middleware('spapiauth');



Route::post('zeen-main/order', function (Request $request) {
    try {
        $hook = $request;

        $shopify_product_ids = '';    
        $order_item_variant_ids = [];  
        
        $marketplace_reference_id = ltrim($hook->name, '#');
        $seller_id = \Session::get('seller_id');
        $order_check = Order::onWriteConnection()->where('marketplace_reference_id', $marketplace_reference_id)->where('seller_id', $seller_id)->count();
        $order_check_temp = Order::where('marketplace_reference_id', $marketplace_reference_id)->where('seller_id', $seller_id)->count();
        \Log::info("Zeen Main | Reference_id : ".$marketplace_reference_id." | Count : ".$order_check." | Temp Count : ".$order_check_temp." | Seller ID : ".$seller_id);
        
        if(\Session::get('topic') == 'create'){
            if($order_check == 0)  {
                $order = new Order();
                $order->source_id = 1;
                $order->marketplace_id = $hook->id;
                $order->seller_location_id = 1; 
                $order->marketplace_reference_id = $marketplace_reference_id;
                $order->seller_id = $seller_id;
                $order->created_date = date('Y-m-d H:i:s');

                $order->customer_reference_id = ( isset($hook->customer['id']) && $hook->customer['id'] != "" ? $hook->customer['id'] : Null);

                $order->customer_email = ( isset($hook->customer['email']) && $hook->customer['email'] != "" ? $hook->customer['email'] : Null);
                $order->customer_number = ( substr( $order->customer_number, 0, 2 ) == "92" ? "0".substr( $order->customer_number, 2) : $order->customer_number );
                
                $customer_name = '';
                
                if(!isset($hook->shipping_address) && isset($hook->billing_address)){
                    $customer_name .= $hook->billing_address['first_name'].' ';
                    $customer_name .= $hook->billing_address['last_name'];

                    $order->customer_number = ( isset($hook->billing_address['phone']) && $hook->billing_address['phone'] != ""  ? str_replace("+92","0",str_replace(" ","",$hook->billing_address['phone'])) : Null);
                    $destintion_city = City::where('name',$hook->billing_address['city'])->first();
                    $order->destination_city = (  isset($hook->billing_address['city']) && $hook->billing_address['city'] != "" ? ( $destintion_city ? $destintion_city->name : $hook->billing_address['city'] ) : Null);
                    $order->shipping_address = (  isset($hook->billing_address['address1']) && $hook->billing_address['address1'] != "" ? $hook->billing_address['address1'] . " " . $hook->billing_address['address2'] : ' ').' '.$order->destination_city;
                    $order->postal_code = ( isset($hook->billing_address['zip']) ? $hook->billing_address['zip'] : Null );
                    $order->country = ( isset($hook->billing_address['country_code']) && $hook->billing_address['country_code'] != "" ? $hook->billing_address['country_code'] : 'PK' );
                }else {

                    $customer_name .= ( isset($hook->shipping_address['first_name']) ? $hook->shipping_address['first_name'].' ' : "");
                    $customer_name .= ( isset($hook->shipping_address['last_name']) ? $hook->shipping_address['last_name'] : "");

                    $order->customer_number = ( isset($hook->shipping_address['phone']) && $hook->shipping_address['phone'] != "" ? str_replace("+92","0",str_replace(" ","",$hook->shipping_address['phone'])) : Null);
                    $temp_city = ( isset($hook->shipping_address['city']) && $hook->shipping_address['city'] != ""  ? $hook->shipping_address['city'] : Null);
                    $destintion_city = City::where('name',$temp_city)->first();
                    $order->destination_city = ( isset($hook->shipping_address['city']) && $hook->shipping_address['city'] != ""  ? ( $destintion_city ? $destintion_city->name : $hook->shipping_address['city'] ) : Null);
                    $order->shipping_address = ( isset($hook->shipping_address['address1']) && $hook->shipping_address['address1'] != "" ? $hook->shipping_address['address1'] . " " . $hook->shipping_address['address2'] : ' ').' '.$order->destination_city;
                    $order->postal_code = ( isset($hook->shipping_address['zip']) ? $hook->shipping_address['zip'] : Null );
                    $order->country = ( isset($hook->shipping_address['country_code']) && $hook->shipping_address['country_code'] != "" ? $hook->shipping_address['country_code'] : 'PK' );
                }
                $order->customer_name = $customer_name;
                $order->discount = ( $hook->total_discounts ? $hook->total_discounts : 0 );
                $order->grand_total = $hook->total_price;

                if(!isset($hook->gateway)){

                    if (isset($hook->payment_gateway_names) && isset($hook->payment_gateway_names[0])) {
                        $payment_gateway = $hook->payment_gateway_names[0];
                    } else {
                        $payment_gateway = "NO_PAYMENT_GATEWAY";
                    }
                    
                } else {
                    $payment_gateway = $hook->gateway;
                }

                $order->cod_payment = ($payment_gateway == 'Cash on Delivery (COD)'? 1 : 0) ;
                $order->shipping_fee = ( isset($hook->shipping_lines[0]['price']) && $hook->shipping_lines[0]['price'] != "" ? $hook->shipping_lines[0]['price'] : 0 );
                
                $order->status =  config('enum.order_status')['PENDING'];

                // $order->placement_date = Carbon::parse($hook->created_at)->toDateTimeString() ;
                $order->placement_date = Carbon::parse($hook->created_at)->setTimezone(config('app.timezone'))->toDateTimeString() ;


                $payment_method_id = SellerPaymentMethod::where('seller_id',$seller_id)->where('machine_name',$payment_gateway)->first();
                if($payment_method_id){
                    $order->seller_payment_method_id = $payment_method_id->id;
                    $order->cod_payment = ($payment_method_id->payment_on_delivery == 1 ? 1 : 0);                
                }

                foreach($hook->shipping_lines as $line)
                {
                    $method_id = SellerShipmentMethod::where('seller_id',$seller_id)->where('machine_name',$line['title'])->first();
                    if($method_id){
                        $order->seller_shipment_method_id = $method_id->id;
                       
                    }
               
                    
                }

                //This is to cater FLH-1546
                if(AddOn::fbr($seller_id) && $order->country == "PK"){
                    $fbr_pos_fee_conf = Setting::where('seller_id', $seller_id)->where('key',config('enum.fbr')['FBR_POS_FEE_CONF']);
                    if($fbr_pos_fee_conf->exists() && $fbr_pos_fee_conf->value('value') == config('enum.fbr')['FBR_POS_FEE_OPTION_1']){
                        if($order->shipping_fee >= 1){
                            $order->shipping_fee = $order->shipping_fee - 1;
                            $order->fbr_tax = 1;
                        }
                    }
                }

                $order->customer_number = str_replace(' ','',$order->customer_number);
                $order->is_created = 0;

                $voucher_message = "";
                if(isset($hook->discount_applications)){
                    \Log::info($hook->discount_applications);
                    if (count($hook->discount_applications) > 0) {
                        foreach ($hook->discount_applications as $value) {
                            if($value['type'] == "discount_code"){
                                $order->voucher = $value['code'];
                                $voucher_message =  ", ".$value['code']." as discount voucher code";
                                //this is to identify if the voucher code was order wise discount or order wise 
                                //this was requested during gtech integration for pos insert
                                if($value['target_selection'] == "all"){
                                    $order->is_voucher_order_wise = 1;
                                }

                                if($value['target_selection'] == "entitled"){
                                    $order->is_voucher_order_wise = 2; // 2 mean voucher is product voucher
                                }
                            }
                        }
                    }
                }

                $isOrderSaved=$order->save();
                $concatinated_customer_details = $order->customer_number."|".$order->customer_email;
                foreach($hook->line_items as $item)
                {
                    $orderItem = new OrderItem();
                    $orderItem->order_id = $order->id;
                    $orderItem->marketplace_id = $item['id'];
                    //This to cater if product created on shopify is also on Unity - realised when working on FFC
                    $item_sku = ($item['sku'] ? $item['sku'] : '');
                    // $product = Product::where('seller_id',$seller_id)->where('SKU', $item_sku)->select('id')->first();
                    // if($product){
                    //     $orderItem->product_id = $product->id;
                    // }else{
                    //     $orderItem->product_id = $item['product_id'];
                    // }
                    $orderItem->product_id =  '';
                    $orderItem->product_name = $item['name'];
                    $orderItem->SKU = $item['sku'];
                    $orderItem->description = $item['name'];
                    $orderItem->description2;
                    $orderItem->description3;
                    $orderItem->seller_unique_code = $item['product_id'];
                    $orderItem->material_of_product;

                    $original_price = 0;
                    if (isset($item['properties']) && $item['properties']) {
                        
                        foreach ($item['properties'] as $value) {

                            if (isset($value['name']) && $value['name'] == 'Compare Price') {

                                $original_price = $value['value'];
                                break;
                            }
                        }
                    }

                    $orderItem->cost = ( $original_price ? $original_price : $item['price'] );
                    $orderItem->actual_price = ( $original_price ? $original_price : $item['price'] );


                    /// For Discount
                    $discount = 0;
                    if (count($item['discount_allocations']) > 0) {
                        foreach ($item['discount_allocations'] as $value) {
                            $discount += $value['amount'];
                        }
                    }

                    if ($original_price > $item['price']) {
                        $orderItem->item_discount = ($original_price - $item['price']) * ($item['quantity']);
                        $discount += $orderItem->item_discount;
                    }
                    $orderItem->discount = $discount;


                    /// For Tax
                    $tax = 0;
                    if (count($item['tax_lines']) > 0) {
                        foreach ($item['tax_lines'] as $value) {
                            $tax += $value['price'];
                        }
                        $orderItem->tax = $tax;
                        $tax = $tax / $item['quantity'];
                    }


                    /// For Unit Price
                    if($hook->taxes_included) {
                        $orderItem->unit_price = $orderItem->cost-$tax;
                    } else {
                        $orderItem->unit_price = $orderItem->cost;
                    }
                    
                    
                    $orderItem->quantity = $item['quantity'];
                    $orderItem->sub_total = ($orderItem->unit_price * $item['quantity']) - $discount;
                    $orderItem->weight = ( $item['grams'] ? $item['grams']/1000 : $item['grams'] );
                    $orderItem->status = config('enum.item_status')['PENDING'];
                    $orderItem->reason = "";
                    $orderItem->tax_rate = ( isset($item['tax_lines'][0]['rate']) ? $item['tax_lines'][0]['rate'] : 0 );
                    $orderItem->save();

                    $shopify_product_ids .= ($shopify_product_ids ? ',' : '').$item['product_id'];
                    $order_item_variant_ids[] = ['variant_id' => $item['variant_id'], 'order_item_id' => $orderItem->id ];
                    $order_skus[] = $orderItem->SKU;
                }
                
                OrderComments::add($order->id, 'Order Dropped Process', '#'.$order->marketplace_reference_id.' Order Dropped from website source <b>'.$request->header('X-Shopify-Shop-Domain').' (Shopify)</b> with <b>'.( $payment_gateway ).'</b> as its payment gateway'.$voucher_message.' and <b>'.$hook->total_outstanding.'</b> is its outstanding amount' , 'Success', '1');
        
                sort($order_skus);
                $concatinated_customer_details .= "|".implode('|', $order_skus);
                $concatinated_customer_details_md5_hash = md5($concatinated_customer_details);
                
                $order->order_hash = $concatinated_customer_details_md5_hash;
                $order->is_created = 1;
                $order->save();


                /// Adding Note into Comments
                if (isset($hook->note) && $hook->note) {
                    OrderComments::add($order->id, 'Order Customer Note', $hook->note, 'Success', '1');
                }

                /// Adding additional note into comments
                $note_attributes = "";
                if (isset($hook->note_attributes) && $hook->note_attributes) {

                    foreach ($hook->note_attributes as $value) {
                        $note_attributes .= $value['value']."<br>";
                    }

                    OrderComments::add($order->id, 'Order Additional Customer Note', $note_attributes, 'Success', '1');
                }
        
                //check for order duplication
                $order_duplicate = OmniEngineHoldReason::checkForOrderDuplication($order);



                /// GET ORDER ITEM BARCODE
                $shopify = new ShopifyApp();
                $order_item_barcode_creation_result = $shopify->getOrderItemBarcode($order->seller_id, $shopify_product_ids, $order_item_variant_ids);

                if ($order_item_barcode_creation_result['error']) {

                    // if (in_array($order_item_barcode_creation_result['message'], ['Shopify access token not found'])) {

                        event(new ShopifyOrderItemBarcodeEvent($order->seller_id, $order->id, $shopify_product_ids, $order_item_variant_ids));
                        Log::critical($order_item_barcode_creation_result['message'].' | Seller ID : '.$order->seller_id.' | Shopify Product ID : '. $shopify_product_ids);                    
                    // } 
                }

                $add_on_auto_confirm = \App\Models\AddOn::where('seller_id',$order->seller_id)->where('key',config('enum.add_ons')['AUTOORDERCONFIRMATION'])->where('value','1')->first();
                if($add_on_auto_confirm && !$order_duplicate)
                    CustomerOrderConfirmation::autoConfirmProcess($order);
                
                AutoShippingMethodBook::addTag($order);
                
                AutoShipped::auto_shipped_created_order($order->id);
                $order->passthrough();

                //Omni Location Assignment Item wise
                if(AddOn::omniLocationAssignment($order->seller_id)){
                    $olDeterminationTrig = Setting::where('seller_id', $order->seller_id)->where('key', config('enum.settings')['OMNILOCATIONDETERMINATIONTRIGGER'])->first();
                    if (isset($olDeterminationTrig) && $olDeterminationTrig->value == 'order_creation') {
                        $order->assignLocationToOrderItems();
                    }
                }


                //Single Auto Location Auto Assingment
                if(!AddOn::omniLocationAssignment($order->seller_id)){
                    $assign_orders_to_ffc_setting = Setting::where('seller_id', $order->seller_id)->where('key', config('enum.settings')['ASSIGN-ORDER-TO-LOCATION'])->first();
                    if (isset($assign_orders_to_ffc_setting)) {
                        if ($assign_orders_to_ffc_setting->value == 'order_creation') {
                            $order->assignSingleLocation();
                        }
                    }
                }

                \Log::info("Zeen Main | ".$order->id." | Order Created");

                event(new AddressValidationLikelihoodEvent($order));
                if($order->cod_payment == 0){
                    event(new ShopifyGetPaymentIdEvent($order));
                }
                return "Order Created";
            } else  {
                \Log::info("Zeen Main | Duplicate Order");
                return "Duplicate Order";
            }
            
        }
        elseif(\Session::get('topic') == 'cancel'){
            return ShopifyApp::webhookCancellationOrder($seller_id, $hook, $marketplace_reference_id, $order_check);
        }
        elseif(\Session::get('topic') == 'update'){
            return ShopifyApp::zeenWebhookUpdateOrder($seller_id, $hook, $marketplace_reference_id, $order_check);
        }
    } catch (\Exception $e){
        Log::critical("Zeen Main | ".\Session::get('topic')." | ".$e->getMessage());
        Log::critical($e->getTraceAsString());
        $activity_id = activity()
                        ->causedBy(\Session::get('seller_id'))
                        ->withProperties(['response' => $e->getMessage(), 'dump' => json_encode($request->all())])
                        ->log('Zeen Main');
        Mail::to('<EMAIL>')->send(new IntegrationError($activity_id, 'Zeen Main '.\Session::get('topic')));

        if (str_contains($e->getMessage(), 'Incorrect string value')) {
            $code = 200;
        } else {
            $code = 500;
        }
        
        return response()->json(['error' => 1, 'message' => $e->getMessage()], $code);
    }
})->middleware('spapiauth');


Route::post('jafferjees-shopify/order', function (Request $request) {
    try {
        $hook = $request;

        $shopify_product_ids = '';    
        $order_item_variant_ids = [];  
        
        $marketplace_reference_id = ltrim($hook->name, '#');
        $seller_id = \Session::get('seller_id');
        $order_check = Order::onWriteConnection()->where('marketplace_reference_id', $marketplace_reference_id)->where('seller_id', $seller_id)->count();
        $order_check_temp = Order::where('marketplace_reference_id', $marketplace_reference_id)->where('seller_id', $seller_id)->count();
        \Log::info("Jafferjees | Reference_id : ".$marketplace_reference_id." | Count : ".$order_check." | Temp Count : ".$order_check_temp." | Seller ID : ".$seller_id);
        
        if(\Session::get('topic') == 'create'){
            if($order_check == 0)  {
                $order = new Order();
                $order->source_id = 1;
                $order->marketplace_id = $hook->id;
                $order->seller_location_id = 1; 
                $order->marketplace_reference_id = $marketplace_reference_id;
                $order->seller_id = $seller_id;
                $order->created_date = date('Y-m-d H:i:s');

                $order->customer_reference_id = ( isset($hook->customer['id']) && $hook->customer['id'] != "" ? $hook->customer['id'] : Null);

                $order->customer_email = ( isset($hook->customer['email']) && $hook->customer['email'] != "" ? $hook->customer['email'] : Null);
                $order->customer_number = ( substr( $order->customer_number, 0, 2 ) == "92" ? "0".substr( $order->customer_number, 2) : $order->customer_number );
                
                $customer_name = '';
                
                if(!isset($hook->shipping_address) && isset($hook->billing_address)){
                    $customer_name .= $hook->billing_address['first_name'].' ';
                    $customer_name .= $hook->billing_address['last_name'];

                    $order->customer_number = ( isset($hook->billing_address['phone']) && $hook->billing_address['phone'] != ""  ? str_replace("+92","0",str_replace(" ","",$hook->billing_address['phone'])) : Null);
                    $destintion_city = City::where('name',$hook->billing_address['city'])->first();
                    $order->destination_city = (  isset($hook->billing_address['city']) && $hook->billing_address['city'] != "" ? ( $destintion_city ? $destintion_city->name : $hook->billing_address['city'] ) : Null);
                    $order->shipping_address = (  isset($hook->billing_address['address1']) && $hook->billing_address['address1'] != "" ? $hook->billing_address['address1'] . " " . $hook->billing_address['address2'] : ' ').' '.$order->destination_city;
                    $order->postal_code = ( isset($hook->billing_address['zip']) ? $hook->billing_address['zip'] : Null );
                    $order->country = ( isset($hook->billing_address['country_code']) && $hook->billing_address['country_code'] != "" ? $hook->billing_address['country_code'] : 'PK' );
                }else {

                    $customer_name .= ( isset($hook->shipping_address['first_name']) ? $hook->shipping_address['first_name'].' ' : "");
                    $customer_name .= ( isset($hook->shipping_address['last_name']) ? $hook->shipping_address['last_name'] : "");

                    $order->customer_number = ( isset($hook->shipping_address['phone']) && $hook->shipping_address['phone'] != "" ? str_replace("+92","0",str_replace(" ","",$hook->shipping_address['phone'])) : Null);
                    $temp_city = ( isset($hook->shipping_address['city']) && $hook->shipping_address['city'] != ""  ? $hook->shipping_address['city'] : Null);
                    $destintion_city = City::where('name',$temp_city)->first();
                    $order->destination_city = ( isset($hook->shipping_address['city']) && $hook->shipping_address['city'] != ""  ? ( $destintion_city ? $destintion_city->name : $hook->shipping_address['city'] ) : Null);
                    $order->shipping_address = ( isset($hook->shipping_address['address1']) && $hook->shipping_address['address1'] != "" ? $hook->shipping_address['address1'] . " " . $hook->shipping_address['address2'] : ' ').' '.$order->destination_city;
                    $order->postal_code = ( isset($hook->shipping_address['zip']) ? $hook->shipping_address['zip'] : Null );
                    $order->country = ( isset($hook->shipping_address['country_code']) && $hook->shipping_address['country_code'] != "" ? $hook->shipping_address['country_code'] : 'PK' );
                }
                $order->customer_name = $customer_name;
                $order->discount = ( $hook->total_discounts ? $hook->total_discounts : 0 );
                $order->grand_total = $hook->total_price;

                if(!isset($hook->gateway)){

                    if (isset($hook->payment_gateway_names) && isset($hook->payment_gateway_names[0])) {
                        $payment_gateway = $hook->payment_gateway_names[0];
                    } else {
                        $payment_gateway = "NO_PAYMENT_GATEWAY";
                    }
                    
                } else {
                    $payment_gateway = $hook->gateway;
                }

                $order->cod_payment = ($payment_gateway == 'Cash on Delivery (COD)'? 1 : 0) ;
                $order->shipping_fee = ( isset($hook->shipping_lines[0]['price']) && $hook->shipping_lines[0]['price'] != "" ? $hook->shipping_lines[0]['price'] : 0 );
                
                $order->status =  config('enum.order_status')['PENDING'];

                // $order->placement_date = Carbon::parse($hook->created_at)->toDateTimeString() ;
                $order->placement_date = Carbon::parse($hook->created_at)->setTimezone(config('app.timezone'))->toDateTimeString() ;


                $payment_method_id = SellerPaymentMethod::where('seller_id',$seller_id)->where('machine_name',$payment_gateway)->first();
                if($payment_method_id){
                    $order->seller_payment_method_id = $payment_method_id->id;
                    $order->cod_payment = ($payment_method_id->payment_on_delivery == 1 ? 1 : 0);                
                }

                foreach($hook->shipping_lines as $line)
                {
                    $method_id = SellerShipmentMethod::where('seller_id',$seller_id)->where('machine_name',$line['title'])->first();
                    if($method_id){
                        $order->seller_shipment_method_id = $method_id->id;
                       
                    }
               
                    
                }

                //This is to cater FLH-1546
                if(AddOn::fbr($seller_id) && $order->country == "PK"){
                    $fbr_pos_fee_conf = Setting::where('seller_id', $seller_id)->where('key',config('enum.fbr')['FBR_POS_FEE_CONF']);
                    if($fbr_pos_fee_conf->exists() && $fbr_pos_fee_conf->value('value') == config('enum.fbr')['FBR_POS_FEE_OPTION_1']){
                        if($order->shipping_fee >= 1){
                            $order->shipping_fee = $order->shipping_fee - 1;
                            $order->fbr_tax = 1;
                        }
                    }
                }

                $order->customer_number = str_replace(' ','',$order->customer_number);
                $order->is_created = 0;

                $voucher_message = "";
                if(isset($hook->discount_applications)){
                    \Log::info($hook->discount_applications);
                    if (count($hook->discount_applications) > 0) {
                        foreach ($hook->discount_applications as $value) {
                            if($value['type'] == "discount_code"){
                                $order->voucher = $value['code'];
                                $voucher_message =  ", ".$value['code']." as discount voucher code";
                                //this is to identify if the voucher code was order wise discount or order wise 
                                //this was requested during gtech integration for pos insert
                                if($value['target_selection'] == "all"){
                                    $order->is_voucher_order_wise = 1;
                                }

                                if($value['target_selection'] == "entitled"){
                                    $order->is_voucher_order_wise = 2; // 2 mean voucher is product voucher
                                }
                            }
                        }
                    }
                }

                $note_attributes = "";
                \Log::info("Before note attributes");
                if (isset($hook->note_attributes) && $hook->note_attributes) {
                    \Log::info($hook->note_attributes);
                    foreach ($hook->note_attributes as $value) {

                        if (isset($value['name']) && $value['name'] == 'gift-note') {
                            $note_attributes =  " with additional note : <b>".$value['value']."</b>";
                            break;
                        }
                    }
                }


                $isOrderSaved=$order->save();
                $concatinated_customer_details = $order->customer_number."|".$order->customer_email;
                foreach($hook->line_items as $item)
                {
                    $orderItem = new OrderItem();
                    $orderItem->order_id = $order->id;
                    $orderItem->marketplace_id = $item['id'];
                    //This to cater if product created on shopify is also on Unity - realised when working on FFC
                    $item_sku = ($item['sku'] ? $item['sku'] : '');
                    $product = Product::where('seller_id',$seller_id)->where('SKU', $item_sku)->select('id')->first();
                    if($product){
                        $orderItem->product_id = $product->id;
                    }else{
                        $orderItem->product_id = $item['product_id'];
                    }
                    $orderItem->product_name = $item['name'];
                    $orderItem->SKU = $item['sku'];
                   
                    $orderItem->description2;
                    $orderItem->description3;
                    $orderItem->seller_unique_code = $item['product_id'];
                    $orderItem->material_of_product;

                    $original_price = $item['price'];
                    $personalization = "";

                    if (isset($item['properties']) && $item['properties']) {
                        
                        foreach ($item['properties'] as $value) {

                            if (isset($value['name']) && $value['name'] == 'Name') {
                               $personalization.=  $value['name']." : ".$value['value']." <br> ";
                            }
                            if (isset($value['name']) && $value['name'] == 'Font-Style') {
                                $personalization.=  $value['name']." : ".$value['value']." <br> ";
                             }
                             if (isset($value['name']) && $value['name'] == 'Font-Effect') {
                                $personalization.=  $value['name']." : ".$value['value']." <br> ";
                             }
                             if (isset($value['name']) && $value['name'] == 'Font-placement') {
                                $personalization.=  $value['name']." : ".$value['value'];
                             }
                        }
                    }

                    $orderItem->description = ( $personalization != "" ? json_encode($personalization) : $item['name'] );

                    $orderItem->cost = ( $original_price ? $original_price : $item['price'] );
                    $orderItem->actual_price = ( $original_price ? $original_price : $item['price'] );


                    /// For Discount
                    $discount = 0;
                    if (count($item['discount_allocations']) > 0) {
                        foreach ($item['discount_allocations'] as $value) {
                            $discount += $value['amount'];
                        }
                    }

                    if ($original_price > $item['price']) {
                        $orderItem->item_discount = ($original_price - $item['price']) * ($item['quantity']);
                        $discount += $orderItem->item_discount;
                    }
                    $orderItem->discount = $discount;


                    /// For Tax
                    $tax = 0;
                    if (count($item['tax_lines']) > 0) {
                        foreach ($item['tax_lines'] as $value) {
                            $tax += $value['price'];
                        }
                        $orderItem->tax = $tax;
                        $tax = $tax / $item['quantity'];
                    }


                    /// For Unit Price
                    if($hook->taxes_included) {
                        $orderItem->unit_price = $orderItem->cost-$tax;
                    } else {
                        $orderItem->unit_price = $orderItem->cost;
                    }
                    
                    
                    $orderItem->quantity = $item['quantity'];
                    $orderItem->sub_total = ($orderItem->unit_price * $item['quantity']) - $discount;
                    $orderItem->weight = ( $item['grams'] ? $item['grams']/1000 : $item['grams'] );
                    $orderItem->status = config('enum.item_status')['PENDING'];
                    $orderItem->reason = "";
                    $orderItem->tax_rate = ( isset($item['tax_lines'][0]['rate']) ? $item['tax_lines'][0]['rate'] : 0 );
                    $orderItem->save();

                    $shopify_product_ids .= ($shopify_product_ids ? ',' : '').$item['product_id'];
                    $order_item_variant_ids[] = ['variant_id' => $item['variant_id'], 'order_item_id' => $orderItem->id ];
                    $order_skus[] = $orderItem->SKU;
                }
                
                OrderComments::add($order->id, 'Order Dropped Process', '#'.$order->marketplace_reference_id.' Order Dropped from website source <b>'.$request->header('X-Shopify-Shop-Domain').' (Shopify)</b> with <b>'.( $payment_gateway ).'</b> as its payment gateway'.$voucher_message.' and <b>'.$hook->total_outstanding.'</b> is its outstanding amount'.$note_attributes , 'Success', '1');
        
                sort($order_skus);
                $concatinated_customer_details .= "|".implode('|', $order_skus);
                $concatinated_customer_details_md5_hash = md5($concatinated_customer_details);
                
                $order->order_hash = $concatinated_customer_details_md5_hash;
                $order->is_created = 1;
                $order->save();
        

                /// Adding Note into Comments
                if (isset($hook->note) && $hook->note) {
                    OrderComments::add($order->id, 'Order Customer Note', $hook->note, 'Success', '1');
                }

                /// Adding additional note into comments
                $note_attributes = "";
                if (isset($hook->note_attributes) && $hook->note_attributes) {

                    foreach ($hook->note_attributes as $value) {
                        $note_attributes .= $value['value']."<br>";
                    }

                    OrderComments::add($order->id, 'Order Additional Customer Note', $note_attributes, 'Success', '1');
                }

                //check for order duplication
                $order_duplicate = OmniEngineHoldReason::checkForOrderDuplication($order);


                $fetch_barcodes_from_storefront = Setting::where('seller_id', $order->seller_id)->where('key', config('enum.settings')['FETCH-BARCODES-STOREFRONT'])->first();
                if (isset($fetch_barcodes_from_storefront) && $fetch_barcodes_from_storefront->value == '1') {
                    /// GET ORDER ITEM BARCODE
                    $shopify = new ShopifyApp();
                    $order_item_barcode_creation_result = $shopify->getOrderItemBarcode($order->seller_id, $shopify_product_ids, $order_item_variant_ids);

                    if ($order_item_barcode_creation_result['error']) {

                        // if (in_array($order_item_barcode_creation_result['message'], ['Shopify access token not found'])) {

                            event(new ShopifyOrderItemBarcodeEvent($order->seller_id, $order->id, $shopify_product_ids, $order_item_variant_ids));
                            Log::critical($order_item_barcode_creation_result['message'].' | Seller ID : '.$order->seller_id.' | Shopify Product ID : '. $shopify_product_ids);                    
                        // } 
                    }
                }

                $add_on_auto_confirm = \App\Models\AddOn::where('seller_id',$order->seller_id)->where('key',config('enum.add_ons')['AUTOORDERCONFIRMATION'])->where('value','1')->first();
                if($add_on_auto_confirm && !$order_duplicate)
                    CustomerOrderConfirmation::autoConfirmProcess($order);
                
                AutoShippingMethodBook::addTag($order);
                
                AutoShipped::auto_shipped_created_order($order->id);
                $order->passthrough();

                //Omni Location Assignment Item wise
                if(AddOn::omniLocationAssignment($order->seller_id)){
                    $olDeterminationTrig = Setting::where('seller_id', $order->seller_id)->where('key', config('enum.settings')['OMNILOCATIONDETERMINATIONTRIGGER'])->first();
                    if (isset($olDeterminationTrig) && $olDeterminationTrig->value == 'order_creation') {
                        $order->assignLocationToOrderItems();
                    }
                }


                //Single Auto Location Auto Assingment
                if(!AddOn::omniLocationAssignment($order->seller_id)){
                    $assign_orders_to_ffc_setting = Setting::where('seller_id', $order->seller_id)->where('key', config('enum.settings')['ASSIGN-ORDER-TO-LOCATION'])->first();
                    if (isset($assign_orders_to_ffc_setting)) {
                        if ($assign_orders_to_ffc_setting->value == 'order_creation') {
                            $order->assignSingleLocation();
                        }
                    }
                }

                \Log::info("Jafferjees shopify | ".$order->id." | Order Created");

                event(new AddressValidationLikelihoodEvent($order));
                if($order->cod_payment == 0){
                    event(new ShopifyGetPaymentIdEvent($order));
                }
                return "Order Created";
            } else  {
                \Log::info("Jafferjees shopify | Duplicate Order");
                return "Duplicate Order";
            }
            
        }
        elseif(\Session::get('topic') == 'cancel'){
            return ShopifyApp::webhookCancellationOrder($seller_id, $hook, $marketplace_reference_id, $order_check);
        }
        elseif(\Session::get('topic') == 'update'){
            return ShopifyApp::JafferjessWebhookUpdateOrder($seller_id, $hook, $marketplace_reference_id, $order_check);
        }
    } catch (\Exception $e){
        Log::critical("Zeen Main | ".\Session::get('topic')." | ".$e->getMessage());
        Log::critical($e->getTraceAsString());
        $activity_id = activity()
                        ->causedBy(\Session::get('seller_id'))
                        ->withProperties(['response' => $e->getMessage(), 'dump' => json_encode($request->all())])
                        ->log('Zeen Main');
        Mail::to('<EMAIL>')->send(new IntegrationError($activity_id, 'Zeen Main '.\Session::get('topic')));

        if (str_contains($e->getMessage(), 'Incorrect string value')) {
            $code = 200;
        } else {
            $code = 500;
        }
        
        return response()->json(['error' => 1, 'message' => $e->getMessage()], $code);
    }
})->middleware('spapiauth');

Route::post('mg2/order', function (Request $request) {
    $hook = $request->getContent();
    //print_r($hook);die;
    //$hook='{"items":[{"item_id":"678","order_id":"222","parent_item_id":null,"quote_item_id":"2558","store_id":"1","created_at":"2018-07-27 09:14:57","updated_at":"2018-07-27 09:15:22","product_id":"410","product_type":"configurable","product_options":"a:7:{s:15:\"info_buyRequest\";a:7:{s:4:\"uenc\";s:84:\"aHR0cDovL2J1a2hhcmEudGVjaG5vbGVjdHVhbHMuY29tL2NoZWxzZWEtdGVlLTcyMC5odG1sP19fX1NJRD1V\";s:7:\"product\";s:3:\"410\";s:8:\"form_key\";s:16:\"0342XMIz8C9J6lzh\";s:15:\"related_product\";s:0:\"\";s:15:\"super_attribute\";a:2:{i:92;s:2:\"20\";i:180;s:2:\"78\";}s:7:\"options\";a:2:{i:3;s:13:\"Monogram Logo\";i:2;s:1:\"1\";}s:3:\"qty\";s:1:\"1\";}s:7:\"options\";a:2:{i:0;a:7:{s:5:\"label\";s:8:\"monogram\";s:5:\"value\";s:13:\"Monogram Logo\";s:11:\"print_value\";s:13:\"Monogram Logo\";s:9:\"option_id\";s:1:\"3\";s:11:\"option_type\";s:4:\"area\";s:12:\"option_value\";s:13:\"Monogram Logo\";s:11:\"custom_view\";b:0;}i:1;a:7:{s:5:\"label\";s:19:\"Test Custom Options\";s:5:\"value\";s:7:\"model 1\";s:11:\"print_value\";s:7:\"model 1\";s:9:\"option_id\";s:1:\"2\";s:11:\"option_type\";s:9:\"drop_down\";s:12:\"option_value\";s:1:\"1\";s:11:\"custom_view\";b:0;}}s:15:\"attributes_info\";a:2:{i:0;a:2:{s:5:\"label\";s:5:\"Color\";s:5:\"value\";s:5:\"Black\";}i:1;a:2:{s:5:\"label\";s:4:\"Size\";s:5:\"value\";s:1:\"L\";}}s:11:\"simple_name\";s:11:\"Chelsea Tee\";s:10:\"simple_sku\";s:6:\"mtk005\";s:20:\"product_calculations\";i:1;s:13:\"shipment_type\";i:0;}","weight":"1.0000","is_virtual":"0","sku":"mtk005","name":"Chelsea Tee","description":null,"applied_rule_ids":null,"additional_data":null,"free_shipping":"0","is_qty_decimal":"0","no_discount":"0","qty_backordered":null,"qty_canceled":"0.0000","qty_invoiced":1,"qty_ordered":"1.0000","qty_refunded":"0.0000","qty_shipped":"0.0000","base_cost":null,"price":"154.0000","base_price":"154.0000","original_price":"154.0000","base_original_price":"154.0000","tax_percent":"0.0000","tax_amount":"0.0000","base_tax_amount":"0.0000","tax_invoiced":0,"base_tax_invoiced":0,"discount_percent":"0.0000","discount_amount":"0.0000","base_discount_amount":"0.0000","discount_invoiced":0,"base_discount_invoiced":0,"amount_refunded":"0.0000","base_amount_refunded":"0.0000","row_total":"154.0000","base_row_total":"154.0000","row_invoiced":154,"base_row_invoiced":154,"row_weight":"1.0000","base_tax_before_discount":null,"tax_before_discount":null,"ext_order_item_id":null,"locked_do_invoice":null,"locked_do_ship":null,"price_incl_tax":"154.0000","base_price_incl_tax":"154.0000","row_total_incl_tax":"154.0000","base_row_total_incl_tax":"154.0000","hidden_tax_amount":"0.0000","base_hidden_tax_amount":"0.0000","hidden_tax_invoiced":0,"base_hidden_tax_invoiced":0,"hidden_tax_refunded":null,"base_hidden_tax_refunded":null,"is_nominal":"0","tax_canceled":null,"hidden_tax_canceled":null,"tax_refunded":null,"base_tax_refunded":null,"discount_refunded":null,"base_discount_refunded":null,"gift_message_id":null,"gift_message_available":"1","base_weee_tax_applied_amount":"0.0000","base_weee_tax_applied_row_amnt":"0.0000","base_weee_tax_applied_row_amount":"0.0000","weee_tax_applied_amount":"0.0000","weee_tax_applied_row_amount":"0.0000","weee_tax_applied":"a:0:{}","weee_tax_disposition":"0.0000","weee_tax_row_disposition":"0.0000","base_weee_tax_disposition":"0.0000","base_weee_tax_row_disposition":"0.0000","event_id":null,"giftregistry_item_id":null,"gw_id":null,"gw_base_price":null,"gw_price":null,"gw_base_tax_amount":null,"gw_tax_amount":null,"gw_base_price_invoiced":null,"gw_price_invoiced":null,"gw_base_tax_amount_invoiced":null,"gw_tax_amount_invoiced":null,"gw_base_price_refunded":null,"gw_price_refunded":null,"gw_base_tax_amount_refunded":null,"gw_tax_amount_refunded":null,"qty_returned":"0.0000","has_children":true}],"OrderData":{"entity_id":"222","state":"processing","status":"processing","coupon_code":null,"protect_code":"be7a44","shipping_description":"Flat Rate - Fixed","is_virtual":"0","store_id":"1","customer_id":null,"base_discount_amount":"0.0000","base_discount_canceled":null,"base_discount_invoiced":0,"base_discount_refunded":null,"base_grand_total":"159.0000","base_shipping_amount":"5.0000","base_shipping_canceled":null,"base_shipping_invoiced":5,"base_shipping_refunded":null,"base_shipping_tax_amount":"0.0000","base_shipping_tax_refunded":null,"base_subtotal":"154.0000","base_subtotal_canceled":null,"base_subtotal_invoiced":154,"base_subtotal_refunded":null,"base_tax_amount":"0.0000","base_tax_canceled":null,"base_tax_invoiced":0,"base_tax_refunded":null,"base_to_global_rate":"1.0000","base_to_order_rate":"1.0000","base_total_canceled":null,"base_total_invoiced":159,"base_total_invoiced_cost":0,"base_total_offline_refunded":null,"base_total_online_refunded":null,"base_total_paid":159,"base_total_qty_ordered":null,"base_total_refunded":null,"discount_amount":"0.0000","discount_canceled":null,"discount_invoiced":0,"discount_refunded":null,"grand_total":"159.0000","shipping_amount":"5.0000","shipping_canceled":null,"shipping_invoiced":5,"shipping_refunded":null,"shipping_tax_amount":"0.0000","shipping_tax_refunded":null,"store_to_base_rate":"1.0000","store_to_order_rate":"1.0000","subtotal":"154.0000","subtotal_canceled":null,"subtotal_invoiced":154,"subtotal_refunded":null,"tax_amount":"0.0000","tax_canceled":null,"tax_invoiced":0,"tax_refunded":null,"total_canceled":null,"total_invoiced":159,"total_offline_refunded":null,"total_online_refunded":null,"total_paid":159,"total_qty_ordered":"1.0000","total_refunded":null,"can_ship_partially":null,"can_ship_partially_item":null,"customer_is_guest":"1","customer_note_notify":false,"billing_address_id":"436","customer_group_id":"0","edit_increment":null,"email_sent":"1","forced_shipment_with_invoice":null,"payment_auth_expiration":null,"quote_address_id":null,"quote_id":"708","shipping_address_id":"437","adjustment_negative":null,"adjustment_positive":null,"base_adjustment_negative":null,"base_adjustment_positive":null,"base_shipping_discount_amount":"0.0000","base_subtotal_incl_tax":"154.0000","base_total_due":null,"payment_authorization_amount":null,"shipping_discount_amount":"0.0000","subtotal_incl_tax":"154.0000","total_due":null,"weight":"1.0000","customer_dob":null,"increment_id":"*********","applied_rule_ids":null,"base_currency_code":"USD","customer_email":"<EMAIL>","customer_firstname":"Mohammed","customer_lastname":"Sami","customer_middlename":"A","customer_prefix":null,"customer_suffix":null,"customer_taxvat":null,"discount_description":null,"ext_customer_id":null,"ext_order_id":null,"global_currency_code":"USD","hold_before_state":null,"hold_before_status":null,"order_currency_code":"USD","original_increment_id":null,"relation_child_id":null,"relation_child_real_id":null,"relation_parent_id":null,"relation_parent_real_id":null,"remote_ip":"**************","shipping_method":"flatrate_flatrate","store_currency_code":"USD","store_name":"Main Website\nMadison Island\nEnglish","x_forwarded_for":null,"customer_note":null,"created_at":"2018-07-27 09:14:57","updated_at":"2018-07-27 09:15:22","total_item_count":"1","customer_gender":null,"hidden_tax_amount":"0.0000","base_hidden_tax_amount":"0.0000","shipping_hidden_tax_amount":"0.0000","base_shipping_hidden_tax_amnt":"0.0000","hidden_tax_invoiced":0,"base_hidden_tax_invoiced":0,"hidden_tax_refunded":null,"base_hidden_tax_refunded":null,"shipping_incl_tax":"5.0000","base_shipping_incl_tax":"5.0000","coupon_rule_name":null,"paypal_ipn_customer_notified":"0","gift_message_id":null,"base_customer_balance_amount":null,"customer_balance_amount":null,"base_customer_balance_invoiced":null,"customer_balance_invoiced":null,"base_customer_balance_refunded":null,"customer_balance_refunded":null,"bs_customer_bal_total_refunded":null,"customer_bal_total_refunded":null,"gift_cards":null,"base_gift_cards_amount":null,"gift_cards_amount":null,"base_gift_cards_invoiced":null,"gift_cards_invoiced":null,"base_gift_cards_refunded":null,"gift_cards_refunded":null,"gw_id":null,"gw_allow_gift_receipt":null,"gw_add_card":null,"gw_base_price":null,"gw_price":null,"gw_items_base_price":null,"gw_items_price":null,"gw_card_base_price":null,"gw_card_price":null,"gw_base_tax_amount":null,"gw_tax_amount":null,"gw_items_base_tax_amount":null,"gw_items_tax_amount":null,"gw_card_base_tax_amount":null,"gw_card_tax_amount":null,"gw_base_price_invoiced":null,"gw_price_invoiced":null,"gw_items_base_price_invoiced":null,"gw_items_price_invoiced":null,"gw_card_base_price_invoiced":null,"gw_card_price_invoiced":null,"gw_base_tax_amount_invoiced":null,"gw_tax_amount_invoiced":null,"gw_items_base_tax_invoiced":null,"gw_items_tax_invoiced":null,"gw_card_base_tax_invoiced":null,"gw_card_tax_invoiced":null,"gw_base_price_refunded":null,"gw_price_refunded":null,"gw_items_base_price_refunded":null,"gw_items_price_refunded":null,"gw_card_base_price_refunded":null,"gw_card_price_refunded":null,"gw_base_tax_amount_refunded":null,"gw_tax_amount_refunded":null,"gw_items_base_tax_refunded":null,"gw_items_tax_refunded":null,"gw_card_base_tax_refunded":null,"gw_card_tax_refunded":null,"reward_points_balance":null,"base_reward_currency_amount":null,"reward_currency_amount":null,"base_rwrd_crrncy_amt_invoiced":null,"rwrd_currency_amount_invoiced":null,"base_rwrd_crrncy_amnt_refnded":null,"rwrd_crrncy_amnt_refunded":null,"reward_points_balance_refund":null,"reward_points_balance_refunded":null,"reward_salesrule_points":null,"payment_authorization_expiration":null,"forced_do_shipment_with_invoice":null,"base_shipping_hidden_tax_amount":"0.0000","shipping_tax_invoiced":0,"base_shipping_tax_invoiced":0,"is_in_process":true},"OrderShipping":{"entity_id":"437","parent_id":"222","customer_address_id":null,"quote_address_id":null,"region_id":null,"customer_id":null,"fax":null,"region":null,"postcode":"75300","lastname":"Sami","street":"A-160 Block 3, Gulshan-e-Iqbal\nA-160 Block 3, Gulshan-e-Iqbal","city":"Karachi, Sindh","email":"<EMAIL>","telephone":"+923458225212","country_id":"PK","firstname":"Mohammed","address_type":"shipping","prefix":null,"middlename":"A","suffix":null,"company":"TCS ECOM","vat_id":null,"vat_is_valid":null,"vat_request_id":null,"vat_request_date":null,"vat_request_success":null,"giftregistry_item_id":null},"OrderPayment":"Cash On Delivery"}';
    //return $request;
    $hook = json_decode($hook);
    //var_dump($hook->items );die;
    /*   foreach($hook->items as $item)
       {
           $pOptions = unserialize($item->product_options);
           // Loops through each element. If element again is array, function is recalled. If not, result is echoed.
           $ss="";
           traverseArray($pOptions, $ss);
           echo $ss;

       }
       die;*/
    $seller_id = \Session::get('seller_id');
    $order_check = Order::onWriteConnection()->where('marketplace_reference_id', $hook->OrderData->increment_id)->where('seller_id', $seller_id);
    if ($order_check->count() > 0) {
        \Log::info('Magento 2 | Duplicate Order');

        return 'Duplicate Order';
    }
    try {
        $order = new Order();
        $order->source_id = 1;
        $order->marketplace_id;
        $order->seller_location_id = 1;
        $order->marketplace_reference_id = ($hook->OrderData->increment_id ? $hook->OrderData->increment_id : 'Not Present');
        $order->seller_id = \Session::get('seller_id');
        $order->created_date = date('Y-m-d H:i:s');
        $order->customer_name = ($hook->OrderShipping->firstname ? $hook->OrderShipping->firstname : 'Not Present').' '.($hook->OrderShipping->middlename ? $hook->OrderShipping->middlename : ' ').' '.($hook->OrderShipping->lastname ? $hook->OrderShipping->lastname : ' ');
        $order->customer_email = ($hook->OrderShipping->email ? $hook->OrderShipping->email : 'Not Present');

        $order->customer_number = ($hook->OrderShipping->telephone ? str_replace('+92', '0', $hook->OrderShipping->telephone) : 'Not Present');
        $order->customer_number = (substr($order->customer_number, 0, 2) == '92' ? '0'.substr($order->customer_number, 2) : $order->customer_number);

        $hook->OrderShipping->city = str_replace("'", '', $hook->OrderShipping->city);
        $hook->OrderShipping->street = str_replace("'", '', $hook->OrderShipping->street);

        $destintion_city = City::where('name', $hook->OrderShipping->city)->first();
        $order->destination_city = ($hook->OrderShipping->city ? ($destintion_city ? $destintion_city->name : $hook->OrderShipping->city) : 'Not Present');
        $order->shipping_address = ($hook->OrderShipping->street ? str_replace("'", '', $hook->OrderShipping->street) : ' ').' '.$order->destination_city;
        $order->discount = ($hook->OrderData->base_discount_amount ? $hook->OrderData->base_discount_amount : 0);
        $order->grand_total = ($hook->OrderData->grand_total ? $hook->OrderData->grand_total : 0);
        $order->total_invoiced = ($hook->OrderData->total_invoiced ? $hook->OrderData->total_invoiced : 0);
        $order->cod_payment = ($hook->OrderPayment == 'cashondelivery' ? 1 : 0);
        $order->shipping_fee = ($hook->OrderData->base_shipping_amount ? $hook->OrderData->base_shipping_amount : 0);
        $order->status =  config('enum.order_status')['PENDING'];

        // $order->placement_date = $hook->OrderData->created_at ;
        $order->placement_date = Carbon::parse($hook->OrderData->created_at,'utc')->setTimezone(config('app.timezone'))->toDateTimeString() ;

        $payment_method_id = SellerPaymentMethod::where('seller_id',\Session::get('seller_id'))->where('machine_name',$hook->OrderPayment)->first();
        if($payment_method_id){
            $order->seller_payment_method_id = $payment_method_id->id;
            $order->cod_payment = ($payment_method_id->payment_on_delivery == 1 ? 1 : 0); 
        }
        
        $method_id = SellerShipmentMethod::where('seller_id',\Session::get('seller_id'))->where('machine_name',$hook->OrderData->shipping_description)->first();
        if($method_id){
            $order->seller_shipment_method_id = $method_id->id;
            
        }

        $order->currency = ($hook->OrderData->base_currency_code ? $hook->OrderData->base_currency_code : 'PKR');
        $order->customer_number = str_replace(' ','',$order->customer_number);
        $isOrderSaved = $order->save();
        $concatinated_customer_details = $order->customer_number."|".$order->customer_email;

        //dd($request->items);
        foreach ($hook->items as $item) {
            if ($item->parent_item_id != null) {
                continue;
            }
            $orderItem = new OrderItem();
            $orderItem->order_id = $order->id;
            //This to cater if product created on shopify is also on Unity - realised when working on FFC
            $item_sku = ($item->sku ? $item->sku : '');
            $product = Product::where('seller_id',$seller_id)->where('SKU', $item_sku)->select('id')->first();
            if($product){
                $orderItem->product_id = $product->id;
            }else{
                $orderItem->product_id = ($item->product_id ? $item->product_id : 'Not Present');
            }

            $orderItem->product_name = ($item->name ? $item->name : 'Not Present');
            $orderItem->SKU = ($item->sku ? $item->sku : 'Not Present');
            //var_dump($item->product_options);die;
            $pOptions_text = '';
            //$pOptions = unserialize($item->product_options);

            if (isset($item->product_options->options)) {
                foreach ($item->product_options->options as $pOption) {
                    $pOptions_text .= $pOption->label.' = '.$pOption->value;
                }
            }

            if (isset($item->product_options->attributes_info)) {
                $pOptions_text .= ' ';

                foreach ($item->product_options->attributes_info as $pAttribute) {
                    $pOptions_text .= $pAttribute->label.' = '.$pAttribute->value;
                }
            }

            //dd($pOptions_text);

            //traverseArray($pOptions, $ss);
            $orderItem->description = $pOptions_text;

            $orderItem->description2;
            $orderItem->description3;
            $orderItem->seller_unique_code = ($item->product_id ? $item->product_id : 'Not Present');
            $orderItem->material_of_product;
            $orderItem->cost = $item->base_cost == null ? 0 : $item->base_cost;

            /// For Discount
            $discount = $item->original_price - $item->price;
            if ($discount) {
                $discount = $discount * $item->qty_ordered;
            }
            $orderItem->discount = $discount + ($item->base_discount_amount ? $item->base_discount_amount : 0);

            $orderItem->unit_price = ($item->original_price ? $item->original_price : 0);
            $orderItem->quantity = ($item->qty_ordered ? $item->qty_ordered : 0);
            $orderItem->sub_total = ($orderItem->unit_price * $item->qty_ordered) - $orderItem->discount;
            $orderItem->weight = ($item->weight ? $item->weight : 0);
            $orderItem->status = config('enum.item_status')['PENDING'];
            $orderItem->reason = "";

            $orderItem->tax = ($item->tax_amount ? $item->tax_amount : 0) + (isset($item->discount_tax_compensation_amount) ? $item->discount_tax_compensation_amount : 0);
            $orderItem->tax_rate = ($item->tax_percent ? $item->tax_percent : 0);
            $orderItem->tax_rate = ($item->tax_percent / 100);

            $orderItem->save();
            $order_skus[] = $orderItem->SKU;
        }
        
        OrderComments::add($order->id, 'Order Dropped Process', '#'.$order->marketplace_reference_id.' Order Dropped from website source <b>'.$request->header('X-Origin-Url').' (Magento 2)</b>' , 'Success', '1');

        sort($order_skus);
        $concatinated_customer_details .= "|".implode('|', $order_skus);
        $concatinated_customer_details_md5_hash = md5($concatinated_customer_details);
        
        $order->order_hash = $concatinated_customer_details_md5_hash;
        $order->is_created = 1;
        $order->save();

        //check for order duplication
        $order_duplicate = OmniEngineHoldReason::checkForOrderDuplication($order);

        
        // if(Setting::where('seller_id', $seller_id)->where('key',config('enum.add_ons')['AUTOROBOCALL'])->value('value')){
        //     $client = new \GuzzleHttp\Client(['verify' => false]);
        //     $response = $client->post(env('APP_URL').'/api/robocall', [
        //         'http_errors' => FALSE,
        //         'form_params' => [
        //             'ids' => [$order->id]
        //         ]
        //     ]);
        // }
        // if(AddOn::where('seller_id', $order->seller_id)->where('key',config('enum.add_ons')['CUSTOMERORDERCONFIRMATION'])->where('value',1)->exists() && Setting::where('seller_id', $order->seller_id)->where('key',config('enum.settings')['CUSTOMERORDERCONFIRMATION'])->where('value',1)->exists()){
        //     CustomerOrderConfirmation::getUrl($order->id,$order->customer_number);
        // }
        $add_on_auto_confirm = \App\Models\AddOn::where('seller_id',$order->seller_id)->where('key',config('enum.add_ons')['AUTOORDERCONFIRMATION'])->where('value','1')->first();
        if($add_on_auto_confirm && !$order_duplicate)
            CustomerOrderConfirmation::autoConfirmProcess($order);

        AutoShippingMethodBook::addTag($order);

        AutoShipped::auto_shipped_created_order($order->id);
        $order->passthrough();

        //Omni Location Assignment Item wise
        if(AddOn::omniLocationAssignment($order->seller_id)){
            $olDeterminationTrig = Setting::where('seller_id', $order->seller_id)->where('key', config('enum.settings')['OMNILOCATIONDETERMINATIONTRIGGER'])->first();
            if (isset($olDeterminationTrig) && $olDeterminationTrig->value == 'order_creation') {
                $order->assignLocationToOrderItems();
            }
        }


        //Single Auto Location Auto Assingment
        if(!AddOn::omniLocationAssignment($order->seller_id)){
            $assign_orders_to_ffc_setting = Setting::where('seller_id', $order->seller_id)->where('key', config('enum.settings')['ASSIGN-ORDER-TO-LOCATION'])->first();
            if (isset($assign_orders_to_ffc_setting)) {
                if ($assign_orders_to_ffc_setting->value == 'order_creation') {
                    $order->assignSingleLocation();
                }
            }
        }

        \Log::info('Magento 2 | '.$order->id.' | Order Created');

        event(new AddressValidationLikelihoodEvent($order));

        return 'good';
    } catch (Exception $e) {
        \Log::info('Magento 2 | '.$e->getMessage());
        echo 'Message: '.$e->getMessage();
        $activity_id = activity()
                        ->causedBy(\Session::get('seller_id'))
                        ->withProperties(['response' => $e->getMessage(), 'dump' => json_encode($request->all())])
                        ->log('Magento');
        Mail::to('<EMAIL>')->send(new IntegrationError($activity_id, 'Magento'));
    }
})->middleware('mg19apiauth');

Route::post('jdot/order', function (Request $request) {
    $hook = $request->getContent();
    \Log::info($hook);
    //print_r($hook);die;
    //$hook='{"items":[{"item_id":"678","order_id":"222","parent_item_id":null,"quote_item_id":"2558","store_id":"1","created_at":"2018-07-27 09:14:57","updated_at":"2018-07-27 09:15:22","product_id":"410","product_type":"configurable","product_options":"a:7:{s:15:\"info_buyRequest\";a:7:{s:4:\"uenc\";s:84:\"aHR0cDovL2J1a2hhcmEudGVjaG5vbGVjdHVhbHMuY29tL2NoZWxzZWEtdGVlLTcyMC5odG1sP19fX1NJRD1V\";s:7:\"product\";s:3:\"410\";s:8:\"form_key\";s:16:\"0342XMIz8C9J6lzh\";s:15:\"related_product\";s:0:\"\";s:15:\"super_attribute\";a:2:{i:92;s:2:\"20\";i:180;s:2:\"78\";}s:7:\"options\";a:2:{i:3;s:13:\"Monogram Logo\";i:2;s:1:\"1\";}s:3:\"qty\";s:1:\"1\";}s:7:\"options\";a:2:{i:0;a:7:{s:5:\"label\";s:8:\"monogram\";s:5:\"value\";s:13:\"Monogram Logo\";s:11:\"print_value\";s:13:\"Monogram Logo\";s:9:\"option_id\";s:1:\"3\";s:11:\"option_type\";s:4:\"area\";s:12:\"option_value\";s:13:\"Monogram Logo\";s:11:\"custom_view\";b:0;}i:1;a:7:{s:5:\"label\";s:19:\"Test Custom Options\";s:5:\"value\";s:7:\"model 1\";s:11:\"print_value\";s:7:\"model 1\";s:9:\"option_id\";s:1:\"2\";s:11:\"option_type\";s:9:\"drop_down\";s:12:\"option_value\";s:1:\"1\";s:11:\"custom_view\";b:0;}}s:15:\"attributes_info\";a:2:{i:0;a:2:{s:5:\"label\";s:5:\"Color\";s:5:\"value\";s:5:\"Black\";}i:1;a:2:{s:5:\"label\";s:4:\"Size\";s:5:\"value\";s:1:\"L\";}}s:11:\"simple_name\";s:11:\"Chelsea Tee\";s:10:\"simple_sku\";s:6:\"mtk005\";s:20:\"product_calculations\";i:1;s:13:\"shipment_type\";i:0;}","weight":"1.0000","is_virtual":"0","sku":"mtk005","name":"Chelsea Tee","description":null,"applied_rule_ids":null,"additional_data":null,"free_shipping":"0","is_qty_decimal":"0","no_discount":"0","qty_backordered":null,"qty_canceled":"0.0000","qty_invoiced":1,"qty_ordered":"1.0000","qty_refunded":"0.0000","qty_shipped":"0.0000","base_cost":null,"price":"154.0000","base_price":"154.0000","original_price":"154.0000","base_original_price":"154.0000","tax_percent":"0.0000","tax_amount":"0.0000","base_tax_amount":"0.0000","tax_invoiced":0,"base_tax_invoiced":0,"discount_percent":"0.0000","discount_amount":"0.0000","base_discount_amount":"0.0000","discount_invoiced":0,"base_discount_invoiced":0,"amount_refunded":"0.0000","base_amount_refunded":"0.0000","row_total":"154.0000","base_row_total":"154.0000","row_invoiced":154,"base_row_invoiced":154,"row_weight":"1.0000","base_tax_before_discount":null,"tax_before_discount":null,"ext_order_item_id":null,"locked_do_invoice":null,"locked_do_ship":null,"price_incl_tax":"154.0000","base_price_incl_tax":"154.0000","row_total_incl_tax":"154.0000","base_row_total_incl_tax":"154.0000","hidden_tax_amount":"0.0000","base_hidden_tax_amount":"0.0000","hidden_tax_invoiced":0,"base_hidden_tax_invoiced":0,"hidden_tax_refunded":null,"base_hidden_tax_refunded":null,"is_nominal":"0","tax_canceled":null,"hidden_tax_canceled":null,"tax_refunded":null,"base_tax_refunded":null,"discount_refunded":null,"base_discount_refunded":null,"gift_message_id":null,"gift_message_available":"1","base_weee_tax_applied_amount":"0.0000","base_weee_tax_applied_row_amnt":"0.0000","base_weee_tax_applied_row_amount":"0.0000","weee_tax_applied_amount":"0.0000","weee_tax_applied_row_amount":"0.0000","weee_tax_applied":"a:0:{}","weee_tax_disposition":"0.0000","weee_tax_row_disposition":"0.0000","base_weee_tax_disposition":"0.0000","base_weee_tax_row_disposition":"0.0000","event_id":null,"giftregistry_item_id":null,"gw_id":null,"gw_base_price":null,"gw_price":null,"gw_base_tax_amount":null,"gw_tax_amount":null,"gw_base_price_invoiced":null,"gw_price_invoiced":null,"gw_base_tax_amount_invoiced":null,"gw_tax_amount_invoiced":null,"gw_base_price_refunded":null,"gw_price_refunded":null,"gw_base_tax_amount_refunded":null,"gw_tax_amount_refunded":null,"qty_returned":"0.0000","has_children":true}],"OrderData":{"entity_id":"222","state":"processing","status":"processing","coupon_code":null,"protect_code":"be7a44","shipping_description":"Flat Rate - Fixed","is_virtual":"0","store_id":"1","customer_id":null,"base_discount_amount":"0.0000","base_discount_canceled":null,"base_discount_invoiced":0,"base_discount_refunded":null,"base_grand_total":"159.0000","base_shipping_amount":"5.0000","base_shipping_canceled":null,"base_shipping_invoiced":5,"base_shipping_refunded":null,"base_shipping_tax_amount":"0.0000","base_shipping_tax_refunded":null,"base_subtotal":"154.0000","base_subtotal_canceled":null,"base_subtotal_invoiced":154,"base_subtotal_refunded":null,"base_tax_amount":"0.0000","base_tax_canceled":null,"base_tax_invoiced":0,"base_tax_refunded":null,"base_to_global_rate":"1.0000","base_to_order_rate":"1.0000","base_total_canceled":null,"base_total_invoiced":159,"base_total_invoiced_cost":0,"base_total_offline_refunded":null,"base_total_online_refunded":null,"base_total_paid":159,"base_total_qty_ordered":null,"base_total_refunded":null,"discount_amount":"0.0000","discount_canceled":null,"discount_invoiced":0,"discount_refunded":null,"grand_total":"159.0000","shipping_amount":"5.0000","shipping_canceled":null,"shipping_invoiced":5,"shipping_refunded":null,"shipping_tax_amount":"0.0000","shipping_tax_refunded":null,"store_to_base_rate":"1.0000","store_to_order_rate":"1.0000","subtotal":"154.0000","subtotal_canceled":null,"subtotal_invoiced":154,"subtotal_refunded":null,"tax_amount":"0.0000","tax_canceled":null,"tax_invoiced":0,"tax_refunded":null,"total_canceled":null,"total_invoiced":159,"total_offline_refunded":null,"total_online_refunded":null,"total_paid":159,"total_qty_ordered":"1.0000","total_refunded":null,"can_ship_partially":null,"can_ship_partially_item":null,"customer_is_guest":"1","customer_note_notify":false,"billing_address_id":"436","customer_group_id":"0","edit_increment":null,"email_sent":"1","forced_shipment_with_invoice":null,"payment_auth_expiration":null,"quote_address_id":null,"quote_id":"708","shipping_address_id":"437","adjustment_negative":null,"adjustment_positive":null,"base_adjustment_negative":null,"base_adjustment_positive":null,"base_shipping_discount_amount":"0.0000","base_subtotal_incl_tax":"154.0000","base_total_due":null,"payment_authorization_amount":null,"shipping_discount_amount":"0.0000","subtotal_incl_tax":"154.0000","total_due":null,"weight":"1.0000","customer_dob":null,"increment_id":"*********","applied_rule_ids":null,"base_currency_code":"USD","customer_email":"<EMAIL>","customer_firstname":"Mohammed","customer_lastname":"Sami","customer_middlename":"A","customer_prefix":null,"customer_suffix":null,"customer_taxvat":null,"discount_description":null,"ext_customer_id":null,"ext_order_id":null,"global_currency_code":"USD","hold_before_state":null,"hold_before_status":null,"order_currency_code":"USD","original_increment_id":null,"relation_child_id":null,"relation_child_real_id":null,"relation_parent_id":null,"relation_parent_real_id":null,"remote_ip":"**************","shipping_method":"flatrate_flatrate","store_currency_code":"USD","store_name":"Main Website\nMadison Island\nEnglish","x_forwarded_for":null,"customer_note":null,"created_at":"2018-07-27 09:14:57","updated_at":"2018-07-27 09:15:22","total_item_count":"1","customer_gender":null,"hidden_tax_amount":"0.0000","base_hidden_tax_amount":"0.0000","shipping_hidden_tax_amount":"0.0000","base_shipping_hidden_tax_amnt":"0.0000","hidden_tax_invoiced":0,"base_hidden_tax_invoiced":0,"hidden_tax_refunded":null,"base_hidden_tax_refunded":null,"shipping_incl_tax":"5.0000","base_shipping_incl_tax":"5.0000","coupon_rule_name":null,"paypal_ipn_customer_notified":"0","gift_message_id":null,"base_customer_balance_amount":null,"customer_balance_amount":null,"base_customer_balance_invoiced":null,"customer_balance_invoiced":null,"base_customer_balance_refunded":null,"customer_balance_refunded":null,"bs_customer_bal_total_refunded":null,"customer_bal_total_refunded":null,"gift_cards":null,"base_gift_cards_amount":null,"gift_cards_amount":null,"base_gift_cards_invoiced":null,"gift_cards_invoiced":null,"base_gift_cards_refunded":null,"gift_cards_refunded":null,"gw_id":null,"gw_allow_gift_receipt":null,"gw_add_card":null,"gw_base_price":null,"gw_price":null,"gw_items_base_price":null,"gw_items_price":null,"gw_card_base_price":null,"gw_card_price":null,"gw_base_tax_amount":null,"gw_tax_amount":null,"gw_items_base_tax_amount":null,"gw_items_tax_amount":null,"gw_card_base_tax_amount":null,"gw_card_tax_amount":null,"gw_base_price_invoiced":null,"gw_price_invoiced":null,"gw_items_base_price_invoiced":null,"gw_items_price_invoiced":null,"gw_card_base_price_invoiced":null,"gw_card_price_invoiced":null,"gw_base_tax_amount_invoiced":null,"gw_tax_amount_invoiced":null,"gw_items_base_tax_invoiced":null,"gw_items_tax_invoiced":null,"gw_card_base_tax_invoiced":null,"gw_card_tax_invoiced":null,"gw_base_price_refunded":null,"gw_price_refunded":null,"gw_items_base_price_refunded":null,"gw_items_price_refunded":null,"gw_card_base_price_refunded":null,"gw_card_price_refunded":null,"gw_base_tax_amount_refunded":null,"gw_tax_amount_refunded":null,"gw_items_base_tax_refunded":null,"gw_items_tax_refunded":null,"gw_card_base_tax_refunded":null,"gw_card_tax_refunded":null,"reward_points_balance":null,"base_reward_currency_amount":null,"reward_currency_amount":null,"base_rwrd_crrncy_amt_invoiced":null,"rwrd_currency_amount_invoiced":null,"base_rwrd_crrncy_amnt_refnded":null,"rwrd_crrncy_amnt_refunded":null,"reward_points_balance_refund":null,"reward_points_balance_refunded":null,"reward_salesrule_points":null,"payment_authorization_expiration":null,"forced_do_shipment_with_invoice":null,"base_shipping_hidden_tax_amount":"0.0000","shipping_tax_invoiced":0,"base_shipping_tax_invoiced":0,"is_in_process":true},"OrderShipping":{"entity_id":"437","parent_id":"222","customer_address_id":null,"quote_address_id":null,"region_id":null,"customer_id":null,"fax":null,"region":null,"postcode":"75300","lastname":"Sami","street":"A-160 Block 3, Gulshan-e-Iqbal\nA-160 Block 3, Gulshan-e-Iqbal","city":"Karachi, Sindh","email":"<EMAIL>","telephone":"+923458225212","country_id":"PK","firstname":"Mohammed","address_type":"shipping","prefix":null,"middlename":"A","suffix":null,"company":"TCS ECOM","vat_id":null,"vat_is_valid":null,"vat_request_id":null,"vat_request_date":null,"vat_request_success":null,"giftregistry_item_id":null},"OrderPayment":"Cash On Delivery"}';
    //return $request;s
    $hook = json_decode($hook);
    //var_dump($hook->items );die;
    /*   foreach($hook->items as $item)
       {
           $pOptions = unserialize($item->product_options);
           // Loops through each element. If element again is array, function is recalled. If not, result is echoed.
           $ss="";
           traverseArray($pOptions, $ss);
           echo $ss;

       }
       die;*/
    $seller_id = \Session::get('seller_id');
    $new_order_can_be_created_when_order_edit = false;
    $duplicate_check = true;
    if($hook->OrderData->status == "order_edit"){
        $duplicate_check = false;
        $current_order = Order::where('marketplace_reference_id', $hook->OrderData->increment_id)->where('seller_id', $seller_id)->where('status',config('enum.order_status')['PENDING'])->first();
        $messge_to_cs_team = "";
        if($current_order){

            // $fulfilment_order = FulfillmentOrder::where('order_id',$current_order->id)->where('status','!=',config('enum.fulfillment_order_status')['REJECT'])->first();
            // if(!$fulfilment_order){
                $current_order->marketplace_reference_id = $current_order->marketplace_reference_id.'-EDT';
                $current_order->marketplace_id = null;
                $current_order->entity_id = null;
                $current_order->save();

                $current_order->cancelItemsAndRejectFulfillment($current_order->items->pluck('id'), 'Order Edit', 'Magento Order Edit Process');


                $new_order_can_be_created_when_order_edit = true;
            // }else{
            //     $messge_to_cs_team = "Fulfillment Order needs to be rejected from fulfillment location";
            // }
        }else{
            $messge_to_cs_team = "Order Status is not in Pending";
        }

        if($new_order_can_be_created_when_order_edit == false){
            //send email
            $subject = "JDOT <".$hook->OrderData->increment_id."> - <Order Edit> - Sync Failed";
            $jdot = ['<EMAIL>'];
            $unity = '<EMAIL>';
            Mail::raw($messge_to_cs_team, function ($m)  use($jdot,$unity,$subject) {
                $m->to($jdot)
                    ->bcc($unity)
                    ->subject($subject);
            });

            return "Order cannot be edited because order is not in pending state or fulfilment order was created";
        }

    }

    if($duplicate_check == true){
        $order_check = Order::onWriteConnection()->where('marketplace_reference_id', $hook->OrderData->increment_id)->where('seller_id', $seller_id);
        if ($order_check->count() > 0) {
            \Log::info('Jdot | Duplicate Order');

            return 'Duplicate Order';
        }
    }

    $placement_date = Carbon::parse($hook->OrderData->created_at,'utc')->setTimezone(config('app.timezone'))->toDateTimeString();
    $comparison_date = Carbon::parse('2024-09-01');

    // Check if placement_date is greater than September 1, 2024
    if (!Carbon::parse($placement_date)->greaterThan($comparison_date)) {
        \Log::info('Placement date show be greater than September 1, 2024');
        return 'Placement date show be greater than September 1, 2024';
    } 


    try {
        $order = new Order();
        $order->source_id = 1;
        $order->marketplace_id = ($hook->OrderData->entity_id ? $hook->OrderData->entity_id : null);
        $order->entity_id = ($hook->OrderData->entity_id ? $hook->OrderData->entity_id : null);
        $order->seller_location_id = 1;
        $order->marketplace_reference_id = ($hook->OrderData->increment_id ? $hook->OrderData->increment_id : 'Not Present');
        $order->seller_id = \Session::get('seller_id');
        $order->created_date = date('Y-m-d H:i:s');
        $order->customer_name = ($hook->OrderShipping->firstname ? $hook->OrderShipping->firstname : 'Not Present').' '.($hook->OrderShipping->middlename ? $hook->OrderShipping->middlename : ' ').' '.($hook->OrderShipping->lastname ? $hook->OrderShipping->lastname : ' ');
        $order->customer_email = ($hook->OrderShipping->email ? $hook->OrderShipping->email : 'Not Present');

        $order->customer_number = ($hook->OrderShipping->telephone ? str_replace('+92', '0', $hook->OrderShipping->telephone) : 'Not Present');
        $order->customer_number = (substr($order->customer_number, 0, 2) == '92' ? '0'.substr($order->customer_number, 2) : $order->customer_number);

        $hook->OrderShipping->city = str_replace("'", '', $hook->OrderShipping->city);
        $hook->OrderShipping->street = str_replace("'", '', $hook->OrderShipping->street);

        $destintion_city = City::where('name', $hook->OrderShipping->city)->first();
        $order->destination_city = ($hook->OrderShipping->city ? ($destintion_city ? $destintion_city->name : $hook->OrderShipping->city) : 'Not Present');
        $order->shipping_address = ($hook->OrderShipping->street ? str_replace("'", '', $hook->OrderShipping->street) : ' ').' '.$order->destination_city;
        $order->discount = ($hook->OrderData->base_discount_amount ? $hook->OrderData->base_discount_amount : 0);
        $order->grand_total = ($hook->OrderData->base_grand_total ? $hook->OrderData->base_grand_total : 0);
        $order->total_invoiced = (isset($hook->OrderData->total_invoiced) ? $hook->OrderData->total_invoiced : 0);
        $order->cod_payment = ($hook->OrderPayment == 'cashondelivery' ? 1 : 0);
        $order->shipping_fee = ($hook->OrderData->base_shipping_amount ? $hook->OrderData->base_shipping_amount : 0);
        $order->status =  config('enum.order_status')['PENDING'];

        // $order->placement_date = $hook->OrderData->created_at ;
        $order->placement_date = Carbon::parse($hook->OrderData->created_at,'utc')->setTimezone(config('app.timezone'))->toDateTimeString() ;
        $order->country = ( $hook->OrderShipping->country_id ? $hook->OrderShipping->country_id : 'PK' );

        $payment_method_id = SellerPaymentMethod::where('seller_id',\Session::get('seller_id'))->where('machine_name',$hook->OrderPayment)->first();
        if($payment_method_id){
            $order->seller_payment_method_id = $payment_method_id->id;
            $order->cod_payment = ($payment_method_id->payment_on_delivery == 1 ? 1 : 0); 
        }
        
        $method_id = SellerShipmentMethod::where('seller_id',\Session::get('seller_id'))->where('machine_name',$hook->OrderData->shipping_description)->first();
        if($method_id){
            $order->seller_shipment_method_id = $method_id->id;
            
        }

        $order->currency = ($hook->OrderData->base_currency_code ? $hook->OrderData->base_currency_code : 'PKR');
        $order->customer_number = str_replace(' ','',$order->customer_number);

        $order->voucher =  (isset($hook->OrderData->coupon_code) ? $hook->OrderData->coupon_code : null);
        $order->is_voucher_order_wise = (isset($hook->OrderData->coupon_code) ? 1 : 0);
        $order->merchant_tags = ( isset($hook->OrderData->coupon_code) ? $hook->OrderData->coupon_code : null);

        $isOrderSaved = $order->save();
        $concatinated_customer_details = $order->customer_number."|".$order->customer_email;

        //dd($request->items);
        foreach ($hook->items as $item) {
            if ( isset($item->parent_item_id) && $item->parent_item_id != null) {
                if($order_item = OrderItem::where('order_id',$order->id)->where('marketplace_id',$item->parent_item_id)->first())
                {   
                    $order_item->marketplace_variant_id = $item->item_id;
                    $order_item->save();
                }
                continue;
            }
            $orderItem = new OrderItem();
            $orderItem->order_id = $order->id;
            //This to cater if product created on shopify is also on Unity - realised when working on FFC
            $item_sku = ($item->sku ? $item->sku : '');
            $orderItem->SKU = ($item->sku ? $item->sku : 'Not Present');
            $product = Product::where('seller_id',$seller_id)->where('barcode', $item_sku)->select('id','barcode','SKU','category')->first();
            if($product){
                $orderItem->product_id = $product->id;
                $orderItem->barcode = $product->barcode;
                $orderItem->SKU = $product->SKU;
                $orderItem->category = $product->category;
            }else{
                $getMagentoCatalog = new GetMagentoCatalog;
                $api_response = $getMagentoCatalog->basedOnSku($item_sku) ;
                if($api_response['error'] == 1){
                    $orderItem->product_id =  '';
                    $orderItem->barcode = $item_sku;
                    $orderItem->SKU = '';

                    $subject = "JDOT <".$hook->OrderData->increment_id."> - <".$item_sku."> - Catalogue API Error";
                    $jdot = ['<EMAIL>'];
                    $unity = '<EMAIL>';
                    Mail::raw($api_response['api_response'], function ($m)  use($jdot,$unity,$subject) {
                        $m->to($jdot)
                            ->bcc($unity)
                            ->subject($subject);
                    });
                    
                }else{
                    $product = $api_response['unity_product'];
                    $orderItem->product_id = $product->id;
                    $orderItem->barcode = $product->barcode;
                    $orderItem->SKU = $product->SKU;
                    $orderItem->category = $product->category;
                }
            }

            $orderItem->product_name = ($item->name ? $item->name : 'Not Present');
            //var_dump($item->product_options);die;
            $pOptions_text = '';
            //$pOptions = unserialize($item->product_options);

            if (isset($item->product_options->options)) {
                foreach ($item->product_options->options as $pOption) {
                    $pOptions_text .= $pOption->label.' = '.$pOption->value;
                }
            }

            if (isset($item->product_options->attributes_info)) {
                $pOptions_text .= ' ';

                foreach ($item->product_options->attributes_info as $pAttribute) {
                    $pOptions_text .= $pAttribute->label.' = '.$pAttribute->value;
                }
            }

            //dd($pOptions_text);

            //traverseArray($pOptions, $ss);
            $orderItem->description = $pOptions_text;

            $orderItem->description2;
            $orderItem->description3;
            $orderItem->seller_unique_code = ($item->product_id ? $item->product_id : 'Not Present');
            $orderItem->material_of_product;
            // $orderItem->cost = $item->base_cost == null ? 0 : $item->base_cost;
            $orderItem->cost = ($item->base_original_price ? $item->base_original_price : 0);
            $orderItem->actual_price = ($item->base_price ? $item->base_price : 0);

            $price = ($item->base_price ? $item->base_price : 0);
            /// For Discount
            $discount = 0;
            if($price > $item->base_price_incl_tax){
                $discount = $price  - $item->base_price_incl_tax;
            }
            if ($discount) {
                $discount = $discount * $item->qty_ordered;
            }
            $orderItem->discount = $discount + ($item->base_discount_amount ? $item->base_discount_amount : 0);

            $orderItem->tax = ($item->base_tax_amount ? $item->base_tax_amount : 0) + (isset($item->base_discount_tax_compensation_amount) ? $item->base_discount_tax_compensation_amount : 0);
            $orderItem->tax_rate = ($item->tax_percent ? $item->tax_percent : 0);
            $orderItem->tax_rate = ($item->tax_percent / 100);

            $orderItem->unit_price = $price - $orderItem->tax;
            $orderItem->quantity = ($item->qty_ordered ? $item->qty_ordered : 0);
            $orderItem->sub_total = ($orderItem->unit_price * $item->qty_ordered) - $orderItem->discount;
            $orderItem->weight = ($item->weight ? $item->weight : 0);
            $orderItem->status = config('enum.item_status')['PENDING'];
            $orderItem->marketplace_id = ($item->item_id ? $item->item_id : null);
            $orderItem->reason = "";

            $orderItem->save();

            $order_skus[] = $orderItem->barcode;
        }
        
        OrderComments::add($order->id, 'Order Dropped Process', '#'.$order->marketplace_reference_id.' Order Dropped from website source <b>'.$request->header('X-Origin-Url').' (Magento 2)</b>' , 'Success', '1');

        sort($order_skus);
        $concatinated_customer_details .= "|".implode('|', $order_skus);
        $concatinated_customer_details_md5_hash = md5($concatinated_customer_details);
        
        $order->order_hash = $concatinated_customer_details_md5_hash;
        $order->is_created = 1;
        $order->save();

        //check for order duplication
        $order_duplicate = OmniEngineHoldReason::checkForOrderDuplication($order);
        
        // if(Setting::where('seller_id', $seller_id)->where('key',config('enum.add_ons')['AUTOROBOCALL'])->value('value')){
        //     $client = new \GuzzleHttp\Client(['verify' => false]);
        //     $response = $client->post(env('APP_URL').'/api/robocall', [
        //         'http_errors' => FALSE,
        //         'form_params' => [
        //             'ids' => [$order->id]
        //         ]
        //     ]);
        // }
        // if(AddOn::where('seller_id', $order->seller_id)->where('key',config('enum.add_ons')['CUSTOMERORDERCONFIRMATION'])->where('value',1)->exists() && Setting::where('seller_id', $order->seller_id)->where('key',config('enum.settings')['CUSTOMERORDERCONFIRMATION'])->where('value',1)->exists()){
        //     CustomerOrderConfirmation::getUrl($order->id,$order->customer_number);
        // }
        $add_on_auto_confirm = \App\Models\AddOn::where('seller_id',$order->seller_id)->where('key',config('enum.add_ons')['AUTOORDERCONFIRMATION'])->where('value','1')->first();


        AutoShippingMethodBook::addTag($order);

        AutoShipped::auto_shipped_created_order($order->id);
        $order->passthrough();

        //Omni Location Assignment Item wise
        if(AddOn::omniLocationAssignment($order->seller_id)){
            $olDeterminationTrig = Setting::where('seller_id', $order->seller_id)->where('key', config('enum.settings')['OMNILOCATIONDETERMINATIONTRIGGER'])->first();
            if (isset($olDeterminationTrig) && $olDeterminationTrig->value == 'order_creation') {
                $order->assignLocationToOrderItems();
            }
        }



        //Single Auto Location Auto Assingment
        if(!AddOn::omniLocationAssignment($order->seller_id)){
            $assign_orders_to_ffc_setting = Setting::where('seller_id', $order->seller_id)->where('key', config('enum.settings')['ASSIGN-ORDER-TO-LOCATION'])->first();
            if (isset($assign_orders_to_ffc_setting)) {
                if ($assign_orders_to_ffc_setting->value == 'order_creation') {
                    $order->assignSingleLocation();
                }
            }
        }

        \Log::info('Jdot | '.$order->id.' | Order Created');

        event(new AddressValidationLikelihoodEvent($order));

        if($new_order_can_be_created_when_order_edit == true){

            OrderComments::add($order->id, 'Auto Order Confirmation Process', 'Method applied: <b>Magento Order Edit</b>', 'Success', 1 );
            RecordOrderConfirmation::add($order->seller_id,$order->id,'Magento Order Edit',1);
            
            $tag = Tag::whereSellerId($order->seller_id)->whereValue('Confirmed')->first();
    
            if (isset($tag['id'])) {
                $ordertag = new OrderTag();
                $ordertag->updateOrCreate(['order_id' => $order->id], ['tag_id' => $tag['id']]);
                RecordOrderConfirmation::add($order->seller_id,$order->id,'Magento Order Edit',2);
                OrderComments::add($order->id, 'Order Tag Assignment Process', '<span class="label '.$tag['color'].'">'.$tag['value'].'</span> Tag has been assigned', 'Success', 1);
                OrderTag::tag_check($order->id, $tag);
            }
        }else{
            if($add_on_auto_confirm && !$order_duplicate)
                CustomerOrderConfirmation::autoConfirmProcess($order);
        }

        return 'good';
    } catch (Exception $e) {
        \Log::info('Jdot | '.$e->getMessage());
        echo 'Message: '.$e->getMessage();
        $activity_id = activity()
                        ->causedBy(\Session::get('seller_id'))
                        ->withProperties(['response' => $e->getMessage(), 'dump' => json_encode($request->all())])
                        ->log('Magento');
        Mail::to('<EMAIL>')->send(new IntegrationError($activity_id, 'Magento'));
    }
})->middleware('jdotapiauth');

Route::post('jdot/order/cancel', function (Request $request) {
    $hook = $request->getContent();
    \Log::info("Order Cancelation request received from Magentof or jdot");
    \Log::info($hook);
    $hook = json_decode($hook);
    $seller_id = \Session::get('seller_id');
    $order = Order::onWriteConnection()->where('marketplace_reference_id', $hook->orderId)->where('seller_id', $seller_id)->where('status', config('enum.order_status')['PENDING'])->first();
    
    if ($order) {
        $order->cancelItemsAndRejectFulfillment($order->items->pluck('id'), 'Cancelation through Magento', 'Magento Order Cancelation Process');
        //Customer Intimation Trigger
        CustomerIntimationService::execute($order->seller_id, [
            'event' => 'order_cancellation',
            'marketplace_reference_id' => $order->marketplace_reference_id,
            'order_id' => $order->id,
            'customer_email' => $order->customer_email,
            'customer_name' => $order->customer_name,
            'customer_number' => $order->customer_number
        ]);
    }else{
        return 'Order # '.$hook->orderId.' not Found or not in Pending state!';
    }

})->middleware('jdotapiauth');


Route::post('khaadi/order', function (Request $request) {
    $hook = $request->getContent();
    $hook = json_decode($hook);
    $seller_id = 120;

    $pakistan_stores = explode('_', env('KHAADI_PAK'));
    $international_stores = explode('_', env('KHAADI_INT'));

    if (in_array($hook->OrderData->store_id, $pakistan_stores)) {
        $seller_id = 119;
    } elseif (in_array($hook->OrderData->store_id, $international_stores)) {
        $seller_id = 120;
    }

    $order_check = Order::onWriteConnection()->where('marketplace_reference_id', $hook->OrderData->increment_id)->where('seller_id', $seller_id)->count();
    $order_check_temp = Order::where('marketplace_reference_id', $hook->OrderData->increment_id)->where('seller_id', $seller_id)->count();
    if($order_check > 0) {
        \Log::info("Khaadi | Duplicate Order");
        return "Duplicate Order"; 
    }
    \Log::info("Khaadi | Reference_id : ".$hook->OrderData->increment_id." | Count : ".$order_check." | Temp Count : ".$order_check_temp." | Seller ID : ".$seller_id);
    try{
        $order = new Order();
        $order->source_id = 1;
        $order->marketplace_id;
        $order->seller_location_id = 1;
        $order->entity_id = ($hook->OrderData->entity_id ? $hook->OrderData->entity_id : null);
        $order->marketplace_reference_id = ($hook->OrderData->increment_id ? $hook->OrderData->increment_id : 'Not Present');
        $order->seller_id = $seller_id;
        $order->created_date = date('Y-m-d H:i:s');
        $order->customer_name = ( $hook->OrderShipping->firstname ? $hook->OrderShipping->firstname : 'Not Present') . " " . ( $hook->OrderShipping->middlename ? $hook->OrderShipping->middlename : ' ' )  . " " . ( $hook->OrderShipping->lastname ? $hook->OrderShipping->lastname : ' ' );
        $order->customer_email = ( $hook->OrderShipping->email ? $hook->OrderShipping->email : 'Not Present' );
        
        $order->customer_number = ( $hook->OrderShipping->telephone ? str_replace(" ","",$hook->OrderShipping->telephone) : Null );
        $order->customer_number = str_replace(["+92","+0092","±92"],"0",$order->customer_number);
        $order->customer_number = ( substr( $order->customer_number, 0, 2 ) == "92" ? "0".substr( $order->customer_number, 2) : $order->customer_number );
        $order->customer_number = ( substr( $order->customer_number, 0, 4 ) == "0092" ? "0".substr( $order->customer_number, 4) : $order->customer_number );

        $hook->OrderShipping->city = str_replace("'", '', $hook->OrderShipping->city);
        $hook->OrderShipping->street = str_replace("'", '', $hook->OrderShipping->street);

        $destintion_city = City::where('name',$hook->OrderShipping->city)->first();
        $order->destination_city = ( $hook->OrderShipping->city ?  ( $destintion_city ? $destintion_city->name : $hook->OrderShipping->city ) : 'Not Present' );
        $order->shipping_address = ( $hook->OrderShipping->street ? $hook->OrderShipping->street : ' ' ).' '.$order->destination_city.' '.( $hook->OrderShipping->region ? $hook->OrderShipping->region : ' ' );
        $order->discount = ($hook->OrderData->base_discount_amount ? $hook->OrderData->base_discount_amount : 0);
        $order->grand_total = ($hook->OrderData->grand_total ? $hook->OrderData->grand_total : 0);
        $order->total_invoiced = ($hook->OrderData->total_invoiced ? $hook->OrderData->total_invoiced : 0);
        $order->cod_payment = ($hook->OrderPayment == 'cashondelivery' || $hook->OrderPayment == 'cardondelivery' ? 1 : 0);
        if($seller_id == 119){
            $order->shipping_fee = ($hook->OrderData->base_shipping_amount ? $hook->OrderData->base_shipping_amount : 0);
        }else{
            $shipping_fee = ($hook->OrderData->base_shipping_amount ? $hook->OrderData->base_shipping_amount : 0);
            $duty_fee = ( isset($hook->OrderData->fee)  && $hook->OrderData->fee != "" ? $hook->OrderData->fee : 0 );
            $order->shipping_fee = $duty_fee + $shipping_fee;
        }
        $order->status =  config('enum.order_status')['PENDING'];

        // $order->placement_date = $hook->OrderData->created_at ;
        $order->placement_date = Carbon::parse($hook->OrderData->created_at,'utc')->setTimezone(config('app.timezone'))->toDateTimeString() ;

        $order->currency = ($hook->OrderData->base_currency_code ? $hook->OrderData->base_currency_code : 'PKR');
        $order->postal_code = ( $hook->OrderShipping->postcode ? $hook->OrderShipping->postcode : Null );
        $order->region = ( $hook->OrderShipping->region ? $hook->OrderShipping->region : Null );
        $order->country = ( $hook->OrderShipping->country_id ? $hook->OrderShipping->country_id : 'PK' );
        $order->special_instruction = ( isset($hook->OrderData->additional_info) ? $hook->OrderData->additional_info : NULL );
        
        $order_check_temp = Order::where('seller_id', $seller_id)->where('marketplace_reference_id', $hook->OrderData->increment_id)->count();
        \Log::info("Khaadi | Reference_id : ".$hook->OrderData->increment_id." | Again Temp Count : ".$order_check_temp." | Seller ID : ".$seller_id);
        
        $payment_method_id = SellerPaymentMethod::where('seller_id',$seller_id)->where('machine_name',$hook->OrderPayment)->first();
        if($payment_method_id){
            $order->seller_payment_method_id = $payment_method_id->id;   
            // $order->cod_payment = $payment_method_id->payment_on_delivery;
        }
        // else{
        //     $order->cod_payment = ($hook->OrderPayment == 'cashondelivery' ? 1 : 0);
        // }

        $method_id = SellerShipmentMethod::where('seller_id',$seller_id)->where('machine_name',$hook->OrderData->shipping_description)->first();
        if($method_id){
            $order->seller_shipment_method_id = $method_id->id;
            
        }

        //lat long and fbr tax addition
        if($seller_id == 119){
            $order->fbr_tax = ( isset($hook->OrderData->fee)  && $hook->OrderData->fee != "" ? $hook->OrderData->fee : 0 );
        }
        $order->customer_lat = ( isset($hook->OrderData->maplatitude)  && $hook->OrderData->maplatitude != "" ? $hook->OrderData->maplatitude : 0 );
        $order->customer_long = ( isset($hook->OrderData->maplongitude)  && $hook->OrderData->maplongitude != "" ? $hook->OrderData->maplongitude : 0 );
        
        $isOrderSaved=$order->save();
        $concatinated_customer_details = $order->customer_number."|".$order->customer_email;

        //dd($request->items);
        foreach ($hook->items as $item) {
            if ($item->parent_item_id != null) {
                continue;
            }
            $orderItem = new OrderItem();
            $orderItem->order_id = $order->id;
            $orderItem->product_id = ($item->product_id ? $item->product_id : 'Not Present');

            $orderItem->product_name = ($item->name ? $item->name : 'Not Present');
            $orderItem->SKU = ($item->sku ? $item->sku : 'Not Present');
            //var_dump($item->product_options);die;
            $pOptions_text = '';
            //$pOptions = unserialize($item->product_options);

            if (isset($item->product_options->options)) {
                foreach ($item->product_options->options as $pOption) {
                    $pOptions_text .= $pOption->label.' = '.$pOption->value;
                }
            }

            if (isset($item->product_options->attributes_info)) {
                $pOptions_text .= ' ';

                foreach ($item->product_options->attributes_info as $pAttribute) {
                    $pOptions_text .= $pAttribute->label.' = '.$pAttribute->value;
                }
            }

            //dd($pOptions_text);

            //traverseArray($pOptions, $ss);
            $orderItem->description = $pOptions_text;

            $orderItem->description2;
            $orderItem->description3;
            $orderItem->seller_unique_code = ($item->product_id ? $item->product_id : 'Not Present');
            $orderItem->material_of_product;
            $orderItem->cost = $item->base_cost == null ? 0 : $item->base_cost;

            /// For Discount
            $discount = $item->original_price - $item->price;
            if ($discount) {
                $discount = $discount * $item->qty_ordered;
            }
            $orderItem->discount = $discount + ($item->base_discount_amount ? $item->base_discount_amount : 0);

            $orderItem->unit_price = ($item->original_price ? $item->original_price : 0);
            $orderItem->quantity = ($item->qty_ordered ? $item->qty_ordered : 0);
            $orderItem->sub_total = ($orderItem->unit_price * $item->qty_ordered) - $orderItem->discount;
            $orderItem->weight = ($item->weight ? $item->weight : 0);
            $orderItem->status = config('enum.item_status')['PENDING'];
            $orderItem->reason = "";

            $orderItem->tax = ($item->tax_amount ? $item->tax_amount : 0) + (isset($item->discount_tax_compensation_amount) ? $item->discount_tax_compensation_amount : 0);
            $orderItem->tax_rate = ($item->tax_percent ? $item->tax_percent : 0);
            $orderItem->tax_rate = ($item->tax_percent / 100);
            
            $orderItem->save();
            $order_skus[] = $orderItem->SKU;
        }
        
        OrderComments::add($order->id, 'Order Dropped Process', '#'.$order->marketplace_reference_id.' Order Dropped from website source <b>'.$request->header('X-Origin-Url').' ('.$hook->OrderData->store_name.') (Magento 2)</b><br>Status: <b>'.$hook->OrderData->status.'</b>' , 'Success', '1');

        sort($order_skus);
        $concatinated_customer_details .= "|".implode('|', $order_skus);
        $concatinated_customer_details_md5_hash = md5($concatinated_customer_details);
        
        $order->order_hash = $concatinated_customer_details_md5_hash;
        $order->is_created = 1;
        $order->save();

        //check for order duplication
        $order_duplicate = OmniEngineHoldReason::checkForOrderDuplication($order);
        
        /////////////////////////// Chapter 2 Hack /////////////////////////////
        if($hook->OrderData->store_id == 23 && $seller_id == 119){

            $order->seller_location_id = env('KHAADI_CHAPTER2_LOCATION_ID', 56);
            $order->seller_location_assigned_date = Carbon::now()->toDateTimeString();
            $order->save();

            OrderComments::add($order->id, 'Order Location Assignment Process', 'location has been assigned at the time of order creation', 'Success', 1);
            $order->auto_shipped_location_assign_order();
        }

        if ($hook->OrderData->status == env('KHAADI_TEMP_STATUS')) {
            AutoShipped::auto_shipped_created_order_TEMP($order->id);
        } else {
            AutoShipped::auto_shipped_created_order($order->id);
        }
        $add_on_auto_confirm = \App\Models\AddOn::where('seller_id',$order->seller_id)->where('key',config('enum.add_ons')['AUTOORDERCONFIRMATION'])->where('value','1')->first();
        if($add_on_auto_confirm && !$order_duplicate)
            CustomerOrderConfirmation::autoConfirmProcess($order);
        
        AutoShippingMethodBook::addTag($order);

        $order->passthrough();
        \Log::info('Khaadi | '.$order->id.' | Order Created');
        \Log::info('Khaddi Magento Order Log | '.$order->id.' |||| '.json_encode($request->all()));

        event(new AddressValidationLikelihoodEvent($order));


        return 'good';
    } catch (Exception $e) {
        \Log::info('Khaadi | '.$e->getMessage());
        echo 'Message: '.$e->getMessage();
        $activity_id = activity()
                        ->causedBy($seller_id)
                        ->withProperties(['response' => $e->getMessage(), 'dump' => json_encode($request->all())])
                        ->log('Khaadi Magento');
        Mail::to('<EMAIL>')->send(new IntegrationError($activity_id, 'Khaadi'));
    }
})->middleware('khaadiapiauth');

Route::post('hobo/order', function(Request $request) {
    $hook=$request->getContent();
    $hook=json_decode($hook);
    
    try{
        \Log::info('Hobo endpoint reference id | '.$hook->OrderData->increment_id);
        $order_check = Order::onWriteConnection()->where('marketplace_reference_id', $hook->OrderData->increment_id)->where('seller_id', \Session::get('seller_id'))->count();
        $order_check_temp = Order::where('marketplace_reference_id', $hook->OrderData->increment_id)->where('seller_id', \Session::get('seller_id'))->count();
        \Log::info("Hobo | Reference_id : ".$hook->OrderData->increment_id." | Count : ".$order_check." | Temp Count : ".$order_check_temp." | Seller ID : ".\Session::get('seller_id'));
        
        if($order_check > 0) {
            \Log::info("Hobo | Duplicate Order");
            return "Duplicate Order"; 
        } else {
            $order = new Order();
            $order->source_id = 1;
            $order->marketplace_id;
            $order->seller_location_id = 1; 
            $order->marketplace_reference_id = ( $hook->OrderData->increment_id ? $hook->OrderData->increment_id : 'Not Present' );
            $order->seller_id = \Session::get('seller_id');
            $order->created_date = date('Y-m-d H:i:s');
            $order->customer_name = ( $hook->OrderShipping->firstname ? $hook->OrderShipping->firstname : 'Not Present') . " " . ( $hook->OrderShipping->middlename ? $hook->OrderShipping->middlename : ' ' )  . " " . ( $hook->OrderShipping->lastname ? $hook->OrderShipping->lastname : ' ' );
            $order->customer_email = ( $hook->OrderData->customer_email ? $hook->OrderData->customer_email : 'Not Present' );
            $order->customer_number = ( $hook->OrderShipping->telephone ? str_replace("+92","0",$hook->OrderShipping->telephone) : 'Not Present' ) ;
            $order->customer_number = ( substr( $order->customer_number, 0, 2 ) == "92" ? "0".substr( $order->customer_number, 2) : $order->customer_number );
            
            $destintion_city = City::where('name',$hook->OrderShipping->city)->first();
            $order->destination_city = ( $hook->OrderShipping->city ?  ( $destintion_city ? $destintion_city->name : $hook->OrderShipping->city ) : 'Not Present' );
            $order->shipping_address = ( $hook->OrderShipping->street ? $hook->OrderShipping->street : ' ' ).' '.$order->destination_city;
            $order->discount = ($hook->OrderData->base_discount_amount ? $hook->OrderData->base_discount_amount : 0);
            $order->grand_total = ($hook->OrderData->grand_total ? $hook->OrderData->grand_total : 0);
            $order->total_invoiced = ($hook->OrderData->base_grand_total ? $hook->OrderData->base_grand_total : 0);
            $order->cod_payment = ($hook->OrderPayment == 'Cash On Delivery'? 1 : 0) ;
            $order->shipping_fee = ($hook->OrderData->base_shipping_amount ? $hook->OrderData->base_shipping_amount : 0);
            $order->status =  config('enum.order_status')['PENDING'];

            // $order->placement_date = $hook->OrderData->created_at ;
            $order->placement_date = Carbon::parse($hook->OrderData->created_at,'utc')->setTimezone(config('app.timezone'))->toDateTimeString() ;

            $order->currency = ($hook->OrderData->base_currency_code ? $hook->OrderData->base_currency_code : 'PKR');

            $payment_method_id = SellerPaymentMethod::where('seller_id',\Session::get('seller_id'))->where('machine_name',$hook->OrderPayment)->first();
            if($payment_method_id){
                $order->seller_payment_method_id = $payment_method_id->id;
                
            }

            $method_id = SellerShipmentMethod::where('seller_id',\Session::get('seller_id'))->where('machine_name',$hook->OrderData->shipping_description)->first();
            if($method_id){
                $order->seller_shipment_method_id = $method_id->id;
                
            }

            $isOrderSaved=$order->save();
            $concatinated_customer_details = $order->customer_number."|".$order->customer_email;

            //dd($request->items);
            foreach($hook->items as $item)
            {
                $orderItem = new OrderItem();
                $orderItem->order_id = $order->id;
                $orderItem->product_id = ($item->product_id ? $item->product_id : 'Not Present');


                $orderItem->product_name = ($item->name ? $item->name : 'Not Present');
                $orderItem->SKU = ($item->sku ? $item->sku : 'Not Present');

                $pOptions_text = '';
                $pOptions = unserialize($item->product_options);

                if(array_key_exists('options', $pOptions))    {
                    foreach ($pOptions['options'] as $pOption) {
                        $pOptions_text .= $pOption['label'].' = '.$pOption['value'];
                    }
                }

                if(array_key_exists('attributes_info', $pOptions))    {
                    $pOptions_text .= ' ';

                    foreach ($pOptions['attributes_info'] as $pAttribute) {
                        $pOptions_text .= $pAttribute['label'].' = '.$pAttribute['value'];
                    }
                }
                
                //dd($pOptions_text);

                //traverseArray($pOptions, $ss);
                $orderItem->description = $pOptions_text;            

                $orderItem->description2;
                $orderItem->description3;
                $orderItem->seller_unique_code = ($item->product_id ? $item->product_id : 'Not Present');
                $orderItem->material_of_product;
                $orderItem->cost = $item->base_cost==null? 0 : $item->base_cost;
                

                /// For Discount
                $discount = $item->original_price - $item->price;
                if ($discount) {
                    $discount = $discount * $item->qty_ordered;
                }
                $orderItem->discount = $discount + ($item->base_discount_amount ? $item->base_discount_amount : 0);

                $orderItem->unit_price = ($item->original_price ? $item->original_price : 0);
                $orderItem->quantity = ($item->qty_ordered ? $item->qty_ordered : 0);
                $orderItem->sub_total = ($orderItem->unit_price * $item->qty_ordered) - $orderItem->discount;
                $orderItem->weight = ($item->weight ? $item->weight : 0);
                $orderItem->status = config('enum.item_status')['PENDING'];

                $orderItem->tax = ($item->base_tax_amount ? $item->base_tax_amount : 0) + (isset($item->hidden_tax_amount) ? $item->hidden_tax_amount : 0);
                $orderItem->tax_rate = ($item->tax_percent ? $item->tax_percent : 0);
                $orderItem->tax_rate = ($item->tax_percent / 100);

                $orderItem->reason = "";
                $orderItem->save();
                $order_skus[] = $orderItem->SKU;
            }
            
            OrderComments::add($order->id, 'Order Dropped Process', '#'.$order->marketplace_reference_id.' Order Dropped from website source <b>'.$request->header('X-Origin-Url').' (Magento 1.9)</b>' , 'Success', '1');
    
            sort($order_skus);
            $concatinated_customer_details .= "|".implode('|', $order_skus);
            $concatinated_customer_details_md5_hash = md5($concatinated_customer_details);
            
            $order->order_hash = $concatinated_customer_details_md5_hash;
            $order->is_created = 1;
            $order->save();
    
            //check for order duplication
            $order_duplicate = OmniEngineHoldReason::checkForOrderDuplication($order);
            // if(Setting::where('seller_id', \Session::get('seller_id'))->where('key',config('enum.add_ons')['AUTOROBOCALL'])->value('value')){
            //     $client = new \GuzzleHttp\Client(['verify' => false]);
            //     $response = $client->post(env('APP_URL').'/api/robocall', [
            //         'http_errors' => FALSE,
            //         'form_params' => [
            //             'ids' => [$order->id]
			// 		]
            //     ]);
            // }
            
            // if(AddOn::where('seller_id', $order->seller_id)->where('key',config('enum.add_ons')['CUSTOMERORDERCONFIRMATION'])->where('value',1)->exists() && Setting::where('seller_id', $order->seller_id)->where('key',config('enum.settings')['CUSTOMERORDERCONFIRMATION'])->where('value',1)->exists()){
            //     CustomerOrderConfirmation::getUrl($order->id,$order->customer_number);
            // }
            $add_on_auto_confirm = \App\Models\AddOn::where('seller_id',$order->seller_id)->where('key',config('enum.add_ons')['AUTOORDERCONFIRMATION'])->where('value','1')->first();
            if($add_on_auto_confirm && !$order_duplicate)
                CustomerOrderConfirmation::autoConfirmProcess($order);
            
            AutoShippingMethodBook::addTag($order);
            AutoShipped::auto_shipped_created_order($order->id);
            $order->passthrough();

            //Omni Location Assignment Item wise
            if(AddOn::omniLocationAssignment($order->seller_id)){
                $olDeterminationTrig = Setting::where('seller_id', $order->seller_id)->where('key', config('enum.settings')['OMNILOCATIONDETERMINATIONTRIGGER'])->first();
                if (isset($olDeterminationTrig) && $olDeterminationTrig->value == 'order_creation') {
                    $order->assignLocationToOrderItems();
                }
            }


            //Single Auto Location Auto Assingment
            if(!AddOn::omniLocationAssignment($order->seller_id)){
                $assign_orders_to_ffc_setting = Setting::where('seller_id', $order->seller_id)->where('key', config('enum.settings')['ASSIGN-ORDER-TO-LOCATION'])->first();
                if (isset($assign_orders_to_ffc_setting)) {
                    if ($assign_orders_to_ffc_setting->value == 'order_creation') {
                        $order->assignSingleLocation();
                    }
                }
            }

            \Log::info("Hobo | ".$order->id." | Order Created");
            event(new AddressValidationLikelihoodEvent($order));

            return "good";
        }
    } catch(Exception $e) {
        \Log::info("Hobo | ".$e->getMessage());
        \Log::info($e->getTraceAsString());
        echo 'Message: ' .$e->getMessage();
        $activity_id = activity()
                        ->causedBy(\Session::get('seller_id'))
                        ->withProperties(['response' => $e->getMessage(), 'dump' => json_encode($request->all())])
                        ->log('Hobo');
        Mail::to('<EMAIL>')->send(new IntegrationError($activity_id,'Hobo'));
    }
       
})->middleware('hoboapiauth');





Route::post('furor/order', function(Request $request) {
    $hook=$request->getContent();
    $hook=json_decode($hook);
    $seller_id= \Session::get('seller_id');
    $order_check = Order::onWriteConnection()->where('marketplace_reference_id', $hook->OrderData->increment_id)->where('seller_id', $seller_id);
    if($order_check->count() > 0) {
        \Log::info("Furor | Duplicate Order | " . $hook->OrderData->increment_id);
        return response()->json(['error' => 'Not Synced | Furor Duplicate Order | ' . $hook->OrderData->increment_id], 200);
    }
    try{
        $order = new Order();
        $order->source_id = 1;
        $order->marketplace_id;
        $order->seller_location_id = 1; 
        $order->marketplace_reference_id = ( $hook->OrderData->increment_id ? $hook->OrderData->increment_id : 'Not Present' );
        $order->seller_id = \Session::get('seller_id');
        $order->created_date = date('Y-m-d H:i:s');
        $order->customer_name = ( $hook->OrderShipping->firstname ? $hook->OrderShipping->firstname : 'Not Present') . " " . ( $hook->OrderShipping->middlename ? $hook->OrderShipping->middlename : ' ' )  . " " . ( $hook->OrderShipping->lastname ? $hook->OrderShipping->lastname : ' ' );
        $order->customer_email = ( $hook->OrderShipping->email ? $hook->OrderShipping->email : 'Not Present' );
        
        $order->customer_number = ( $hook->OrderShipping->telephone ? str_replace("+92","0",$hook->OrderShipping->telephone) : 'Not Present' ) ;
        $order->customer_number = ( substr( $order->customer_number, 0, 2 ) == "92" ? "0".substr( $order->customer_number, 2) : $order->customer_number );

        $destintion_city = City::where('name',$hook->OrderShipping->city)->first();
        $order->destination_city = ( $hook->OrderShipping->city ?  ( $destintion_city ? $destintion_city->name : $hook->OrderShipping->city ) : 'Not Present' );
        $order->shipping_address = ( $hook->OrderShipping->street ? $hook->OrderShipping->street : ' ' ).' '.$order->destination_city;
        $order->discount = ($hook->OrderData->base_discount_amount ? $hook->OrderData->base_discount_amount : 0);
        $order->grand_total = ($hook->OrderData->grand_total ? $hook->OrderData->grand_total : 0);
        $order->total_invoiced = ($hook->OrderData->total_invoiced ? $hook->OrderData->total_invoiced : 0);
        $order->cod_payment = ($hook->OrderPayment == 'cashondelivery'? 1 : 0) ;
        $order->shipping_fee = ($hook->OrderData->base_shipping_amount ? $hook->OrderData->base_shipping_amount : 0);
        $order->status =  config('enum.order_status')['PENDING'];

        // $order->placement_date = $hook->OrderData->created_at ;
        $order->placement_date = Carbon::parse($hook->OrderData->created_at,'utc')->setTimezone(config('app.timezone'))->toDateTimeString() ;

        $payment_method_id = SellerPaymentMethod::where('seller_id',\Session::get('seller_id'))->where('machine_name',$hook->OrderPayment)->first();
        if($payment_method_id){
            $order->seller_payment_method_id = $payment_method_id->id;
            
        }


        $method_id = SellerShipmentMethod::where('seller_id',\Session::get('seller_id'))->where('machine_name',$hook->OrderData->shipping_description)->first();
        if($method_id){
            $order->seller_shipment_method_id = $method_id->id;
            
        }

        $order->currency = ($hook->OrderData->base_currency_code ? $hook->OrderData->base_currency_code : 'PKR');
        $isOrderSaved=$order->save();
        $concatinated_customer_details = $order->customer_number."|".$order->customer_email;

        //dd($request->items);
        foreach($hook->items as $item)
        {
            if($item->parent_item_id!= null)
                continue;
            $orderItem = new OrderItem();
            $orderItem->order_id = $order->id;
            $orderItem->product_id = ($item->product_id ? $item->product_id : 'Not Present');


            $orderItem->product_name = ($item->name ? $item->name : 'Not Present');
            $orderItem->SKU = ($item->sku ? $item->sku : 'Not Present');
            //var_dump($item->product_options);die;
            $pOptions_text = '';
            //$pOptions = unserialize($item->product_options);

            if(isset($item->product_options->options))    {
                foreach ($item->product_options->options as $pOption) {
                    $pOptions_text .= $pOption->label.' = '.$pOption->value;
                }
            }

            if(isset($item->product_options->attributes_info))    {
                $pOptions_text .= ' ';

                foreach ($item->product_options->attributes_info as $pAttribute) {
                    $pOptions_text .= $pAttribute->label . ' = '. $pAttribute->value;
                }
            }
             
            //dd($pOptions_text);

            //traverseArray($pOptions, $ss);
            $orderItem->description = $pOptions_text;            

            $orderItem->description2;
            $orderItem->description3;
            $orderItem->seller_unique_code = ($item->product_id ? $item->product_id : 'Not Present');
            $orderItem->material_of_product;
            $orderItem->cost = $item->base_cost==null? 0 : $item->base_cost;
            

            /// For Discount
            $discount = $item->original_price - $item->price;
            if ($discount) {
                $discount = $discount * $item->qty_ordered;
            }
            $orderItem->discount = $discount + ($item->base_discount_amount ? $item->base_discount_amount : 0);

            $orderItem->unit_price = ($item->original_price ? $item->original_price : 0);
            $orderItem->quantity = ($item->qty_ordered ? $item->qty_ordered : 0);
            $orderItem->sub_total = ($orderItem->unit_price * $item->qty_ordered) - $orderItem->discount;
            $orderItem->weight = ($item->weight ? $item->weight : 0);
            $orderItem->status = config('enum.item_status')['PENDING'];
            $orderItem->reason = "";

            $orderItem->tax = ($item->tax_amount ? $item->tax_amount : 0) + (isset($item->discount_tax_compensation_amount) ? $item->discount_tax_compensation_amount : 0);
            $orderItem->tax_rate = ($item->tax_percent ? $item->tax_percent : 0);
            $orderItem->tax_rate = ($item->tax_percent / 100);

            $orderItem->save();
            $order_skus[] = $orderItem->SKU;
        }
        
        OrderComments::add($order->id, 'Order Dropped Process', '#'.$order->marketplace_reference_id.' Order Dropped from website source <b>'.$request->header('X-Origin-Url').' (Magento 2)</b>' , 'Success', '1');

        sort($order_skus);
        $concatinated_customer_details .= "|".implode('|', $order_skus);
        $concatinated_customer_details_md5_hash = md5($concatinated_customer_details);
        
        $order->order_hash = $concatinated_customer_details_md5_hash;
        $order->is_created = 1;
        $order->save();

        //check for order duplication
        $order_duplicate = OmniEngineHoldReason::checkForOrderDuplication($order);
        // if(Setting::where('seller_id', $seller_id)->where('key',config('enum.add_ons')['AUTOROBOCALL'])->value('value')){
        //     $client = new \GuzzleHttp\Client(['verify' => false]);
        //     $response = $client->post(env('APP_URL').'/api/robocall', [
        //         'http_errors' => FALSE,
        //         'form_params' => [
        //             'ids' => [$order->id]
        //         ]
        //     ]);
        // }
        // if(AddOn::where('seller_id', $order->seller_id)->where('key',config('enum.add_ons')['CUSTOMERORDERCONFIRMATION'])->where('value',1)->exists() && Setting::where('seller_id', $order->seller_id)->where('key',config('enum.settings')['CUSTOMERORDERCONFIRMATION'])->where('value',1)->exists()){
        //     CustomerOrderConfirmation::getUrl($order->id,$order->customer_number);
        // }

        $add_on_auto_confirm = \App\Models\AddOn::where('seller_id',$order->seller_id)->where('key',config('enum.add_ons')['AUTOORDERCONFIRMATION'])->where('value','1')->first();
        if($add_on_auto_confirm && !$order_duplicate)
            CustomerOrderConfirmation::autoConfirmProcess($order);
        

        AutoShippingMethodBook::addTag($order);

        AutoShipped::auto_shipped_created_order($order->id);
        $order->passthrough();

        //Omni Location Assignment Item wise
        if(AddOn::omniLocationAssignment($order->seller_id)){
            $olDeterminationTrig = Setting::where('seller_id', $order->seller_id)->where('key', config('enum.settings')['OMNILOCATIONDETERMINATIONTRIGGER'])->first();
            if (isset($olDeterminationTrig) && $olDeterminationTrig->value == 'order_creation') {
                $order->assignLocationToOrderItems();
            }
        }


        //Single Auto Location Auto Assingment
        if(!AddOn::omniLocationAssignment($order->seller_id)){
            $assign_orders_to_ffc_setting = Setting::where('seller_id', $order->seller_id)->where('key', config('enum.settings')['ASSIGN-ORDER-TO-LOCATION'])->first();
            if (isset($assign_orders_to_ffc_setting)) {
                if ($assign_orders_to_ffc_setting->value == 'order_creation') {
                    $order->assignSingleLocation();
                }
            }
        }

        \Log::info("Furor | ".$order->id." | Order Created");

        event(new AddressValidationLikelihoodEvent($order));

        return "Order Successfully synced into unity Furor Account";
    }
    catch(Exception $e) {
        \Log::info("Furor | ".$e->getMessage());
        echo 'Message: ' .$e->getMessage();
        $activity_id = activity()
                        ->causedBy(\Session::get('seller_id'))
                        ->withProperties(['response' => $e->getMessage(), 'dump' => json_encode($request->all())])
                        ->log('Furor');
        Mail::to('<EMAIL>')->send(new IntegrationError($activity_id,'Furor'));
      }
       
})->middleware('furorapiauth');







Route::post('edenrobe/order', function(Request $request) {
    $hook=$request->getContent();
    $hook=json_decode($hook);
    $seller_id= \Session::get('seller_id');
    $order_check = Order::onWriteConnection()->where('marketplace_reference_id', $hook->OrderData->increment_id)->where('seller_id', $seller_id);
    if($order_check->count() > 0) {
        \Log::info("Edenrobe | Duplicate Order | " . $hook->OrderData->increment_id);
        return response()->json(['error' => 'Not Synced | Edenrobe Duplicate Order | ' . $hook->OrderData->increment_id], 200);
    }
    try{
        $order = new Order();
        $order->source_id = 1;
        $order->marketplace_id;
        $order->seller_location_id = 1; 
        $order->marketplace_reference_id = ( $hook->OrderData->increment_id ? $hook->OrderData->increment_id : 'Not Present' );
        $order->seller_id = \Session::get('seller_id');
        $order->created_date = date('Y-m-d H:i:s');
        $order->customer_name = ( $hook->OrderShipping->firstname ? $hook->OrderShipping->firstname : 'Not Present') . " " . ( $hook->OrderShipping->middlename ? $hook->OrderShipping->middlename : ' ' )  . " " . ( $hook->OrderShipping->lastname ? $hook->OrderShipping->lastname : ' ' );
        $order->customer_email = ( $hook->OrderShipping->email ? $hook->OrderShipping->email : 'Not Present' );
        
        $order->customer_number = ( $hook->OrderShipping->telephone ? str_replace("+92","0",$hook->OrderShipping->telephone) : 'Not Present' ) ;
        $order->customer_number = ( substr( $order->customer_number, 0, 2 ) == "92" ? "0".substr( $order->customer_number, 2) : $order->customer_number );

        $destintion_city = City::where('name',$hook->OrderShipping->city)->first();
        $order->destination_city = ( $hook->OrderShipping->city ?  ( $destintion_city ? $destintion_city->name : $hook->OrderShipping->city ) : 'Not Present' );
        $order->shipping_address = ( $hook->OrderShipping->street ? $hook->OrderShipping->street : ' ' ).' '.$order->destination_city;
        $order->discount = ($hook->OrderData->base_discount_amount ? $hook->OrderData->base_discount_amount : 0);
        $order->grand_total = ($hook->OrderData->grand_total ? $hook->OrderData->grand_total : 0);
        $order->total_invoiced = ($hook->OrderData->total_invoiced ? $hook->OrderData->total_invoiced : 0);
        $order->cod_payment = ($hook->OrderPayment == 'cashondelivery'? 1 : 0) ;
        $order->shipping_fee = ($hook->OrderData->base_shipping_amount ? $hook->OrderData->base_shipping_amount : 0);
        $order->status =  config('enum.order_status')['PENDING'];

        // $order->placement_date = $hook->OrderData->created_at ;
        $order->placement_date = Carbon::parse($hook->OrderData->created_at,'utc')->setTimezone(config('app.timezone'))->toDateTimeString() ;

        $payment_method_id = SellerPaymentMethod::where('seller_id',\Session::get('seller_id'))->where('machine_name',$hook->OrderPayment)->first();
        if($payment_method_id){
            $order->seller_payment_method_id = $payment_method_id->id;
            
        }

        $method_id = SellerShipmentMethod::where('seller_id',\Session::get('seller_id'))->where('machine_name',$hook->OrderData->shipping_description)->first();
        if($method_id){
            $order->seller_shipment_method_id = $method_id->id;
            
        }

        $order->currency = ($hook->OrderData->base_currency_code ? $hook->OrderData->base_currency_code : 'PKR');
        $isOrderSaved=$order->save();
        $concatinated_customer_details = $order->customer_number."|".$order->customer_email;

        //dd($request->items);
        foreach($hook->items as $item)
        {
            if($item->parent_item_id!= null)
                continue;
            $orderItem = new OrderItem();
            $orderItem->order_id = $order->id;
            $orderItem->product_id = ($item->product_id ? $item->product_id : 'Not Present');


            $orderItem->product_name = ($item->name ? $item->name : 'Not Present');
            $orderItem->SKU = ($item->sku ? $item->sku : 'Not Present');
            //var_dump($item->product_options);die;
            $pOptions_text = '';
            //$pOptions = unserialize($item->product_options);

            if(isset($item->product_options->options))    {
                foreach ($item->product_options->options as $pOption) {
                    $pOptions_text .= $pOption->label.' = '.$pOption->value;
                }
            }

            if(isset($item->product_options->attributes_info))    {
                $pOptions_text .= ' ';

                foreach ($item->product_options->attributes_info as $pAttribute) {
                    $pOptions_text .= $pAttribute->label . ' = '. $pAttribute->value;
                }
            }
             
            //dd($pOptions_text);

            //traverseArray($pOptions, $ss);
            $orderItem->description = $pOptions_text;            

            $orderItem->description2;
            $orderItem->description3;
            $orderItem->seller_unique_code = ($item->product_id ? $item->product_id : 'Not Present');
            $orderItem->material_of_product;
            $orderItem->cost = $item->base_cost==null? 0 : $item->base_cost;
            

            /// For Discount
            $discount = $item->original_price - $item->price;
            if ($discount) {
                $discount = $discount * $item->qty_ordered;
            }
            $orderItem->discount = $discount + ($item->base_discount_amount ? $item->base_discount_amount : 0);

            $orderItem->unit_price = ($item->original_price ? $item->original_price : 0);
            $orderItem->quantity = ($item->qty_ordered ? $item->qty_ordered : 0);
            $orderItem->sub_total = ($orderItem->unit_price * $item->qty_ordered) - $orderItem->discount;
            $orderItem->weight = ($item->weight ? $item->weight : 0);
            $orderItem->status = config('enum.item_status')['PENDING'];
            $orderItem->reason = "";

            $orderItem->tax = ($item->tax_amount ? $item->tax_amount : 0) + (isset($item->discount_tax_compensation_amount) ? $item->discount_tax_compensation_amount : 0);
            $orderItem->tax_rate = ($item->tax_percent ? $item->tax_percent : 0);
            $orderItem->tax_rate = ($item->tax_percent / 100);

            $orderItem->save();
            $order_skus[] = $orderItem->SKU;
        }
        
        OrderComments::add($order->id, 'Order Dropped Process', '#'.$order->marketplace_reference_id.' Order Dropped from website source <b>'.$request->header('X-Origin-Url').' (Magento 2)</b>' , 'Success', '1');

        sort($order_skus);
        $concatinated_customer_details .= "|".implode('|', $order_skus);
        $concatinated_customer_details_md5_hash = md5($concatinated_customer_details);
        
        $order->order_hash = $concatinated_customer_details_md5_hash;
        $order->is_created = 1;
        $order->save();

        //check for order duplication
        $order_duplicate = OmniEngineHoldReason::checkForOrderDuplication($order);
        // if(Setting::where('seller_id', $seller_id)->where('key',config('enum.add_ons')['AUTOROBOCALL'])->value('value')){
        //     $client = new \GuzzleHttp\Client(['verify' => false]);
        //     $response = $client->post(env('APP_URL').'/api/robocall', [
        //         'http_errors' => FALSE,
        //         'form_params' => [
        //             'ids' => [$order->id]
        //         ]
        //     ]);
        // }
        // if(AddOn::where('seller_id', $order->seller_id)->where('key',config('enum.add_ons')['CUSTOMERORDERCONFIRMATION'])->where('value',1)->exists() && Setting::where('seller_id', $order->seller_id)->where('key',config('enum.settings')['CUSTOMERORDERCONFIRMATION'])->where('value',1)->exists()){
        //     CustomerOrderConfirmation::getUrl($order->id,$order->customer_number);
        // }
        $add_on_auto_confirm = \App\Models\AddOn::where('seller_id',$order->seller_id)->where('key',config('enum.add_ons')['AUTOORDERCONFIRMATION'])->where('value','1')->first();
        if($add_on_auto_confirm && !$order_duplicate)
            CustomerOrderConfirmation::autoConfirmProcess($order);
        
        AutoShippingMethodBook::addTag($order);

        AutoShipped::auto_shipped_created_order($order->id);
        $order->passthrough();

        //Omni Location Assignment Item wise
        if(AddOn::omniLocationAssignment($order->seller_id)){
            $olDeterminationTrig = Setting::where('seller_id', $order->seller_id)->where('key', config('enum.settings')['OMNILOCATIONDETERMINATIONTRIGGER'])->first();
            if (isset($olDeterminationTrig) && $olDeterminationTrig->value == 'order_creation') {
                $order->assignLocationToOrderItems();
            }
        }


        //Single Auto Location Auto Assingment
        if(!AddOn::omniLocationAssignment($order->seller_id)){
            $assign_orders_to_ffc_setting = Setting::where('seller_id', $order->seller_id)->where('key', config('enum.settings')['ASSIGN-ORDER-TO-LOCATION'])->first();
            if (isset($assign_orders_to_ffc_setting)) {
                if ($assign_orders_to_ffc_setting->value == 'order_creation') {
                    $order->assignSingleLocation();
                }
            }
        }

        \Log::info("Edenrobe | ".$order->id." | Order Created");
        event(new AddressValidationLikelihoodEvent($order));

        return "Order Successfully synced into unity Edenrobe Account";
    }
    catch(Exception $e) {
        \Log::info("Edenrobe | ".$e->getMessage());
        echo 'Message: ' .$e->getMessage();
        $activity_id = activity()
                        ->causedBy(\Session::get('seller_id'))
                        ->withProperties(['response' => $e->getMessage(), 'dump' => json_encode($request->all())])
                        ->log('Edenrobe');
        Mail::to('<EMAIL>')->send(new IntegrationError($activity_id,'Furor'));
      }
       
})->middleware('edenrobepiauth');






Route::post('acegalleria/order', function(Request $request) {
    $hook=$request->getContent();
    $hook=json_decode($hook);
    $order_check = Order::onWriteConnection()->where('marketplace_reference_id', $hook->OrderData->increment_id)->where('seller_id', \Session::get('seller_id'));
    if($order_check->count() > 0) {
        \Log::info("Ace Galleria | Duplicate Order | " . $hook->OrderData->increment_id);
        return response()->json(['error' => 'Not Synced | Ace Galleria Duplicate Order | ' . $hook->OrderData->increment_id], 200);
    }
    try{
        $order = new Order();
        $order->source_id = 1;
        $order->marketplace_id;
        $order->seller_location_id = 1; 
        $order->marketplace_reference_id = ( $hook->OrderData->increment_id ? $hook->OrderData->increment_id : 'Not Present' );
        $order->seller_id = \Session::get('seller_id');
        $order->created_date = date('Y-m-d H:i:s');
        $order->customer_name = ( $hook->OrderShipping->firstname ? $hook->OrderShipping->firstname : 'Not Present') . " " . ( $hook->OrderShipping->middlename ? $hook->OrderShipping->middlename : ' ' )  . " " . ( $hook->OrderShipping->lastname ? $hook->OrderShipping->lastname : ' ' );
        $order->customer_email = ( $hook->OrderData->customer_email ? $hook->OrderData->customer_email : 'Not Present' );
        $order->customer_number = ( $hook->OrderShipping->telephone ? str_replace("+92","0",$hook->OrderShipping->telephone) : 'Not Present' ) ;
        $order->customer_number = ( substr( $order->customer_number, 0, 2 ) == "92" ? "0".substr( $order->customer_number, 2) : $order->customer_number );
        
        $destintion_city = City::where('name',$hook->OrderShipping->city)->first();
        $order->destination_city = ( $hook->OrderShipping->city ?  ( $destintion_city ? $destintion_city->name : $hook->OrderShipping->city ) : 'Not Present' );
        $order->shipping_address = ( $hook->OrderShipping->street ? $hook->OrderShipping->street : ' ' ).' '.$order->destination_city;
        $order->discount = ($hook->OrderData->base_discount_amount ? $hook->OrderData->base_discount_amount : 0);
        $order->grand_total = ($hook->OrderData->grand_total ? $hook->OrderData->grand_total : 0);
        $order->total_invoiced = ($hook->OrderData->base_grand_total ? $hook->OrderData->base_grand_total : 0);
        $order->cod_payment = ($hook->OrderPayment == 'Cash On Delivery'? 1 : 0) ;
        $order->shipping_fee = ($hook->OrderData->base_shipping_amount ? $hook->OrderData->base_shipping_amount : 0);
        $order->status =  config('enum.order_status')['PENDING'];

        // $order->placement_date = $hook->OrderData->created_at ;
        $order->placement_date = Carbon::parse($hook->OrderData->created_at,'utc')->setTimezone(config('app.timezone'))->toDateTimeString() ;

        $payment_method_id = SellerPaymentMethod::where('seller_id',\Session::get('seller_id'))->where('machine_name',$hook->OrderPayment)->first();
        if($payment_method_id){
            $order->seller_payment_method_id = $payment_method_id->id;
            
        }

        $method_id = SellerShipmentMethod::where('seller_id',\Session::get('seller_id'))->where('machine_name',$hook->OrderData->shipping_description)->first();
        if($method_id){
            $order->seller_shipment_method_id = $method_id->id;
            
        }

        $order->currency = ($hook->OrderData->base_currency_code ? $hook->OrderData->base_currency_code : 'PKR');
        $isOrderSaved=$order->save();
        $concatinated_customer_details = $order->customer_number."|".$order->customer_email;

        //dd($request->items);
        foreach($hook->items as $item)
        {
            $orderItem = new OrderItem();
            $orderItem->order_id = $order->id;
            $orderItem->product_id = ($item->product_id ? $item->product_id : 'Not Present');


            $orderItem->product_name = ($item->name ? $item->name : 'Not Present');
            $orderItem->SKU = ($item->sku ? $item->sku : 'Not Present');

            $pOptions_text = '';
            $pOptions = unserialize($item->product_options);

            if(array_key_exists('options', $pOptions))    {
                foreach ($pOptions['options'] as $pOption) {
                    $pOptions_text .= $pOption['label'].' = '.$pOption['value'];
                }
            }

            if(array_key_exists('attributes_info', $pOptions))    {
                $pOptions_text .= ' ';

                foreach ($pOptions['attributes_info'] as $pAttribute) {
                    $pOptions_text .= $pAttribute['label'].' = '.$pAttribute['value'];
                }
            }
             
            //dd($pOptions_text);

            //traverseArray($pOptions, $ss);
            $orderItem->description = $pOptions_text;            

            $orderItem->description2;
            $orderItem->description3;
            $orderItem->seller_unique_code = ($item->product_id ? $item->product_id : 'Not Present');
            $orderItem->material_of_product;
            $orderItem->cost = $item->base_cost==null? 0 : $item->base_cost;
            

            
            /// For Discount
            $discount = $item->original_price - $item->price;
            if ($discount) {
                $discount = $discount * $item->qty_ordered;
            }
            $orderItem->discount = $discount + ($item->base_discount_amount ? $item->base_discount_amount : 0);

            $orderItem->unit_price = ($item->original_price ? $item->original_price : 0);
            $orderItem->quantity = ($item->qty_ordered ? $item->qty_ordered : 0);
            $orderItem->sub_total = ($orderItem->unit_price * $item->qty_ordered) - $orderItem->discount;
            $orderItem->weight = ($item->weight ? $item->weight : 0);
            $orderItem->status = config('enum.item_status')['PENDING'];
            $orderItem->reason = "";

            $orderItem->tax = ($item->tax_amount ? $item->tax_amount : 0) + (isset($item->hidden_tax_amount) ? $item->hidden_tax_amount : 0);
            $orderItem->tax_rate = ($item->tax_percent ? $item->tax_percent : 0);
            $orderItem->tax_rate = ($item->tax_percent / 100);

            $orderItem->save();
            $order_skus[] = $orderItem->SKU;
        }
        
        OrderComments::add($order->id, 'Order Dropped Process', '#'.$order->marketplace_reference_id.' Order Dropped from website source <b>'.$request->header('X-Origin-Url').' (Magento 1.9)</b>' , 'Success', '1');

        sort($order_skus);
        $concatinated_customer_details .= "|".implode('|', $order_skus);
        $concatinated_customer_details_md5_hash = md5($concatinated_customer_details);
        
        $order->order_hash = $concatinated_customer_details_md5_hash;
        $order->is_created = 1;
        $order->save();

        //check for order duplication
        $order_duplicate = OmniEngineHoldReason::checkForOrderDuplication($order);
        // if(Setting::where('seller_id', \Session::get('seller_id'))->where('key',config('enum.add_ons')['AUTOROBOCALL'])->value('value')){
        //     $client = new \GuzzleHttp\Client(['verify' => false]);
        //     $response = $client->post(env('APP_URL').'/api/robocall', [
        //         'http_errors' => FALSE,
        //         'form_params' => [
        //             'ids' => [$order->id]
        //         ]
        //     ]);
        // }
        // if(AddOn::where('seller_id', $order->seller_id)->where('key',config('enum.add_ons')['CUSTOMERORDERCONFIRMATION'])->where('value',1)->exists() && Setting::where('seller_id', $order->seller_id)->where('key',config('enum.settings')['CUSTOMERORDERCONFIRMATION'])->where('value',1)->exists()){
        //     CustomerOrderConfirmation::getUrl($order->id,$order->customer_number);
        // }
        $add_on_auto_confirm = \App\Models\AddOn::where('seller_id',$order->seller_id)->where('key',config('enum.add_ons')['AUTOORDERCONFIRMATION'])->where('value','1')->first();
        if($add_on_auto_confirm && !$order_duplicate)
            CustomerOrderConfirmation::autoConfirmProcess($order);
        
        AutoShippingMethodBook::addTag($order);

        AutoShipped::auto_shipped_created_order($order->id);
        $order->passthrough();

        //Omni Location Assignment Item wise
        if(AddOn::omniLocationAssignment($order->seller_id)){
            $olDeterminationTrig = Setting::where('seller_id', $order->seller_id)->where('key', config('enum.settings')['OMNILOCATIONDETERMINATIONTRIGGER'])->first();
            if (isset($olDeterminationTrig) && $olDeterminationTrig->value == 'order_creation') {
                $order->assignLocationToOrderItems();
            }
        }

        //Single Auto Location Auto Assingment
        if(!AddOn::omniLocationAssignment($order->seller_id)){
            $assign_orders_to_ffc_setting = Setting::where('seller_id', $order->seller_id)->where('key', config('enum.settings')['ASSIGN-ORDER-TO-LOCATION'])->first();
            if (isset($assign_orders_to_ffc_setting)) {
                if ($assign_orders_to_ffc_setting->value == 'order_creation') {
                    $order->assignSingleLocation();
                }
            }
        }

        \Log::info("Ace Galleria | ".$order->id." | Order Created");
        return "Order Successfully synced into unity Ace Galleria Account";
        event(new AddressValidationLikelihoodEvent($order));

    }
    catch(Exception $e) {
        \Log::info("Ace Galleria | ".$e->getMessage());
        echo 'Message: ' .$e->getMessage();
        $activity_id = activity()
                        ->causedBy(\Session::get('seller_id'))
                        ->withProperties(['response' => $e->getMessage(), 'dump' => json_encode($request->all())])
                        ->log('Ace Galleria');
        Mail::to('<EMAIL>')->send(new IntegrationError($activity_id,'Ace Galleria'));
      }
       
})->middleware('acegalleriaapiauth');




Route::post('jafferjees/order', function(Request $request) {
    $hook=$request->getContent();
    $hook=json_decode($hook);
    $seller_id= \Session::get('seller_id');


    $pakistan_stores = explode('_', env('JAFFERJEES_PAK'));
    $international_stores = explode('_', env('JAFFERJEES_INT'));

    if (in_array($hook->OrderData->store_id, $pakistan_stores)) {
        $seller_id = 439;
    } elseif (in_array($hook->OrderData->store_id, $international_stores)) {
        $seller_id = 3200;
    }


    $order_check = Order::onWriteConnection()->where('marketplace_reference_id', $hook->OrderData->increment_id)->where('seller_id', $seller_id);
    \Log::info('Seller ID = '.$seller_id.' | Jafferjees order DUMP '.json_encode($request->all()));

    if($order_check->count() > 0) {
        \Log::info("Jaffer Jees | Duplicate Order | " . $hook->OrderData->increment_id);
        return response()->json(['error' => 'Not Synced | Jaffer Jees Duplicate Order | ' . $hook->OrderData->increment_id], 200);
    }
    \Log::info("Jaffer Jees | Reference_id : ".$hook->OrderData->increment_id." | Count : ".$order_check->count()." | Seller ID : ".$seller_id);
    try{
        $order = new Order();
        $order->source_id = 1;
        $order->marketplace_id;
        $order->seller_location_id = 1; 
        $order->marketplace_reference_id = ( $hook->OrderData->increment_id ? $hook->OrderData->increment_id : 'Not Present' );
        $order->seller_id = $seller_id;
        $order->created_date = date('Y-m-d H:i:s');
        $order->customer_name = ( $hook->OrderShipping->firstname ? $hook->OrderShipping->firstname : 'Not Present') . " " . ( $hook->OrderShipping->middlename ? $hook->OrderShipping->middlename : ' ' )  . " " . ( $hook->OrderShipping->lastname ? $hook->OrderShipping->lastname : ' ' );
        $order->customer_email = ( $hook->OrderShipping->email ? $hook->OrderShipping->email : 'Not Present' );
        
        $order->customer_number = ( $hook->OrderShipping->telephone ? str_replace("+92","0",$hook->OrderShipping->telephone) : 'Not Present' ) ;
        $order->customer_number = ( substr( $order->customer_number, 0, 2 ) == "92" ? "0".substr( $order->customer_number, 2) : $order->customer_number );

        $hook->OrderShipping->city = str_replace("'", '', $hook->OrderShipping->city);
        $hook->OrderShipping->street = str_replace("'", '', $hook->OrderShipping->street);

        $destintion_city = City::where('name',$hook->OrderShipping->city)->first();
        $order->destination_city = ( $hook->OrderShipping->city ?  ( $destintion_city ? $destintion_city->name : $hook->OrderShipping->city ) : 'Not Present' );
        $order->shipping_address = ( $hook->OrderShipping->street ? $hook->OrderShipping->street : ' ' ).' '.$order->destination_city;
        $order->discount = ($hook->OrderData->base_discount_amount ? $hook->OrderData->base_discount_amount : 0);
        $order->grand_total = ($hook->OrderData->grand_total ? $hook->OrderData->grand_total : 0);
        $order->total_invoiced = ($hook->OrderData->total_invoiced ? $hook->OrderData->total_invoiced : 0);
        $order->cod_payment = ($hook->OrderPayment == 'cashondelivery'? 1 : 0) ;
        $order->shipping_fee = ($hook->OrderData->shipping_amount ? $hook->OrderData->shipping_amount : 0);
        $order->status =  config('enum.order_status')['PENDING'];

        // $order->placement_date = $hook->OrderData->created_at ;
        $order->placement_date = Carbon::parse($hook->OrderData->created_at,'utc')->setTimezone(config('app.timezone'))->toDateTimeString() ;

        $payment_method_id = SellerPaymentMethod::where('seller_id',\Session::get('seller_id'))->where('machine_name',$hook->OrderPayment)->first();
        if($payment_method_id){
            $order->seller_payment_method_id = $payment_method_id->id;
            
        }


        $method_id = SellerShipmentMethod::where('seller_id',\Session::get('seller_id'))->where('machine_name',$hook->OrderData->shipping_description)->first();
        if($method_id){
            $order->seller_shipment_method_id = $method_id->id;
            
        }

        $order->currency = ($hook->OrderData->order_currency_code ? $hook->OrderData->order_currency_code : 'PKR');
        $order->country = ( $hook->OrderShipping->country_id ? $hook->OrderShipping->country_id : 'PK' );

        $isOrderSaved=$order->save();
        $concatinated_customer_details = $order->customer_number."|".$order->customer_email;

        //dd($request->items);
        foreach($hook->items as $item)
        {
            if($item->parent_item_id!= null)
                continue;
            $orderItem = new OrderItem();
            $orderItem->order_id = $order->id;
            $orderItem->product_id = ($item->product_id ? $item->product_id : 'Not Present');


            $orderItem->product_name = ($item->name ? $item->name : 'Not Present');
            $orderItem->SKU = ($item->sku ? $item->sku : 'Not Present');
            //var_dump($item->product_options);die;
            $pOptions_text = '';
            //$pOptions = unserialize($item->product_options);

            if(isset($item->product_options->options))    {
                foreach ($item->product_options->options as $pOption) {
                    $pOptions_text .= $pOption->label.' = '.$pOption->value;
                }
            }

            if(isset($item->product_options->attributes_info))    {
                $pOptions_text .= ' ';

                foreach ($item->product_options->attributes_info as $pAttribute) {
                    $pOptions_text .= $pAttribute->label . ' = '. $pAttribute->value;
                }
            }
             
            //dd($pOptions_text);

            //traverseArray($pOptions, $ss);
            $orderItem->description = $pOptions_text;            

            $orderItem->description2;
            $orderItem->description3;
            $orderItem->seller_unique_code = ($item->product_id ? $item->product_id : 'Not Present');
            $orderItem->material_of_product;
            $orderItem->cost = $item->base_cost==null? 0 : $item->base_cost;
            

            /// For Discount
            $discount = $item->original_price - $item->price;
            if ($discount) {
                $discount = $discount * $item->qty_ordered;
            }
            $orderItem->discount = $discount + ($item->base_discount_amount ? $item->base_discount_amount : 0);

            $orderItem->unit_price = ($item->original_price ? $item->original_price : 0);
            $orderItem->quantity = ($item->qty_ordered ? $item->qty_ordered : 0);
            $orderItem->sub_total = ($orderItem->unit_price * $item->qty_ordered) - $orderItem->discount;
            $orderItem->weight = ($item->weight ? $item->weight : 0);
            $orderItem->status = config('enum.item_status')['PENDING'];
            $orderItem->reason = "";

            $orderItem->tax = ($item->tax_amount ? $item->tax_amount : 0) + (isset($item->discount_tax_compensation_amount) ? $item->discount_tax_compensation_amount : 0);
            $orderItem->tax_rate = ($item->tax_percent ? $item->tax_percent : 0);
            $orderItem->tax_rate = ($item->tax_percent / 100);

            $orderItem->save();
            $order_skus[] = $orderItem->SKU;
        }
        
        OrderComments::add($order->id, 'Order Dropped Process', '#'.$order->marketplace_reference_id.' Order Dropped from website source <b>'.$request->header('X-Origin-Url').' (Magento 2)</b>' , 'Success', '1');

        sort($order_skus);
        $concatinated_customer_details .= "|".implode('|', $order_skus);
        $concatinated_customer_details_md5_hash = md5($concatinated_customer_details);
        
        $order->order_hash = $concatinated_customer_details_md5_hash;
        $order->is_created = 1;
        $order->save();

        //check for order duplication
        $order_duplicate = OmniEngineHoldReason::checkForOrderDuplication($order);

        $add_on_auto_confirm = \App\Models\AddOn::where('seller_id',$order->seller_id)->where('key',config('enum.add_ons')['AUTOORDERCONFIRMATION'])->where('value','1')->first();
        if($add_on_auto_confirm && !$order_duplicate)
            CustomerOrderConfirmation::autoConfirmProcess($order);
        

        AutoShippingMethodBook::addTag($order);

        AutoShipped::auto_shipped_created_order($order->id);
        $order->passthrough();

        //Omni Location Assignment Item wise
        if(AddOn::omniLocationAssignment($order->seller_id)){
            $olDeterminationTrig = Setting::where('seller_id', $order->seller_id)->where('key', config('enum.settings')['OMNILOCATIONDETERMINATIONTRIGGER'])->first();
            if (isset($olDeterminationTrig) && $olDeterminationTrig->value == 'order_creation') {
                $order->assignLocationToOrderItems();
            }
        }

        //Single Auto Location Auto Assingment
        if(!AddOn::omniLocationAssignment($order->seller_id)){
            $assign_orders_to_ffc_setting = Setting::where('seller_id', $order->seller_id)->where('key', config('enum.settings')['ASSIGN-ORDER-TO-LOCATION'])->first();
            if (isset($assign_orders_to_ffc_setting)) {
                if ($assign_orders_to_ffc_setting->value == 'order_creation') {
                    $order->assignSingleLocation();
                }
            }
        }

        \Log::info("Jaffer Jees | ".$order->id." | Order Created");

        event(new AddressValidationLikelihoodEvent($order));

        return "Order Successfully synced into unity Jaffer Jees Account";
    }
    catch(Exception $e) {
        \Log::info("Jaffer Jees | ".$e->getMessage());
        echo 'Message: ' .$e->getMessage();
        $activity_id = activity()
                        ->causedBy(\Session::get('seller_id'))
                        ->withProperties(['response' => $e->getMessage(), 'dump' => json_encode($request->all())])
                        ->log('Jaffer Jees');
        Mail::to('<EMAIL>')->send(new IntegrationError($activity_id,'Jaffer Jees'));
      }
       
})->middleware('jafferjeesapiauth');



Route::post('mumuso/order', function(Request $request) {
    try {

        $hook=$request;
        $marketplace_reference_id = $hook->id;
        $seller_id = 78;
        $order_check = Order::onWriteConnection()->where('marketplace_reference_id', $marketplace_reference_id)->where('seller_id', $seller_id);
        if($order_check->count() == 0)  {
            $order = new Order();
            $order->source_id = 1;
            $order->marketplace_id;
            $order->seller_location_id = 1; 
            $order->marketplace_reference_id = $hook->id;
            $order->seller_id = $seller_id;
            $order->created_date = date('Y-m-d H:i:s');
            $order->customer_name = ($hook->shipping['first_name'] ? $hook->shipping['first_name'] : 'Not Present' ) . " " . ($hook->shipping['last_name'] ? $hook->shipping['last_name'] : ' ');
            $order->customer_email = ($hook->billing['email'] ? $hook->billing['email'] : 'Not Present');

            //HOTFIX: USING LAST NAME AS CONTACT NUMBER FOR A1SHOPPING
            if($seller_id == 12)
                $order->customer_number = ($hook->billing['last_name'] ? $hook->billing['last_name'] : 'Not present');
            else
                $order->customer_number = str_replace("+92","0",$hook->billing['phone']);

            $order->customer_number = ( substr( $order->customer_number, 0, 2 ) == "92" ? "0".substr( $order->customer_number, 2) : $order->customer_number );
            $destintion_city = City::where('name',$hook->shipping['city'])->first();
            $order->destination_city = ($hook->shipping['city'] ? ( $destintion_city ? $destintion_city->name : $hook->shipping['city'] ) : 'Not present');
            $order->shipping_address = ($hook->shipping['address_1'] ? $hook->shipping['address_1'] : ' ') . " " . ($hook->shipping['address_2'] ? $hook->shipping['address_2'] : ' ').' '.$order->destination_city;
            $order->discount = ($hook->discount_total ? $hook->discount_total+$hook->discount_tax : 0);
            $order->grand_total = ($hook->total ? $hook->total : 0);
            $order->cod_payment = ($hook->payment_method == 'cod'? 1 : 0) ;
            $order->shipping_fee = ($hook->shipping_total ? $hook->shipping_total : 0);
            $order->status =  config('enum.order_status')['PENDING'];

            // $order->placement_date = $hook->date_created ;
            $order->placement_date = Carbon::parse($hook->date_created_gmt,'gmt')->setTimezone(config('app.timezone'))->toDateTimeString();

            $payment_method_id = SellerPaymentMethod::where('seller_id',$seller_id)->where('machine_name',$hook->payment_method)->first();
            if($payment_method_id){
                $order->seller_payment_method_id = $payment_method_id->id;
                
            }


            foreach($hook->shipping_lines as $line)
            {
                $method_id = SellerShipmentMethod::where('seller_id',$seller_id)->where('machine_name',$line['method_title'])->first();
                if($method_id){
                    $order->seller_shipment_method_id = $method_id->id;
                    
                }
            
                
            }

            $isOrderSaved=$order->save();
            $concatinated_customer_details = $order->customer_number."|".$order->customer_email;

            //dd($request->items);
            foreach($hook->line_items as $item)
            {
                $orderItem = new OrderItem();
                $orderItem->order_id = $order->id;
                $orderItem->product_id = ($item['product_id'] ? $item['product_id'] : 'Not Present');


                $orderItem->product_name = ($item['name'] ? $item['name'] : 'Not present');
                $orderItem->SKU = ($item['sku'] ? $item['sku'] : 'Not present');
                // foreach($item['meta_data'] as $meta)
                // {
                //     $orderItem->description .= ($meta['key'] ? $meta['key'] : 'Not present') . " : " . ($meta['value'] ? $meta['value'] : 'Not present') . "<br/>";
                // }
                $orderItem->description;
                $orderItem->description2;
                $orderItem->description3;
                $orderItem->seller_unique_code = ($item['product_id'] ? $item['product_id'] : 'Not Present');
                $orderItem->material_of_product;
                $orderItem->cost = ($item['subtotal'] ? $item['subtotal'] : 0);
                

                /// For Discount
                $discount = $item['subtotal'] - $item['total'];
                if ($item['subtotal_tax']) {
                    $discount = $discount + ($item['subtotal_tax'] - $item['total_tax']);
                }
                $orderItem->discount = $discount;

                $orderItem->unit_price = ($item['subtotal'] ? ($item['quantity'] ? $item['subtotal']/$item['quantity'] : 0 ) : 0);
                $orderItem->quantity = ($item['quantity'] ? $item['quantity'] : 0);
                $orderItem->sub_total = ($item['subtotal'] ? $item['subtotal']-$discount : 0 );

                if(isset($hook->tax_lines[0]['rate_percent'])){
                    $orderItem->tax_rate = ($hook->tax_lines[0]['rate_percent'] ? $hook->tax_lines[0]['rate_percent'] : 0 );
                } else{
                    $orderItem->tax_rate = 0;
                }
                $orderItem->tax_rate = ($orderItem->tax_rate / 100);
                $orderItem->tax = $item['subtotal_tax'];
                $orderItem->weight = 0;
                $orderItem->status = config('enum.item_status')['PENDING'];
                $orderItem->reason = "";
                $orderItem->save();
                $order_skus[] = $orderItem->SKU;
            }
            
            OrderComments::add($order->id, 'Order Dropped Process', '#'.$order->marketplace_reference_id.' Order Dropped from website source <b>'.$request->header('x-wc-webhook-source').' (WooCommerce)</b>' , 'Success', '1');
    
            sort($order_skus);
            $concatinated_customer_details .= "|".implode('|', $order_skus);
            $concatinated_customer_details_md5_hash = md5($concatinated_customer_details);
            
            $order->order_hash = $concatinated_customer_details_md5_hash;
            $order->is_created = 1;
            $order->save();
    
            //check for order duplication
            $order_duplicate = OmniEngineHoldReason::checkForOrderDuplication($order);

            // if(Setting::where('seller_id', $seller_id)->where('key',config('enum.add_ons')['AUTOROBOCALL'])->value('value')){
            //     $client = new \GuzzleHttp\Client(['verify' => false]);
            //     $response = $client->post(env('APP_URL').'/api/robocall', [
            //         'http_errors' => FALSE,
            //         'form_params' => [
            //             'ids' => [$order->id]
			// 		]
            //     ]);
            // }
            
            // if(AddOn::where('seller_id', $order->seller_id)->where('key',config('enum.add_ons')['CUSTOMERORDERCONFIRMATION'])->where('value',1)->exists() && Setting::where('seller_id', $order->seller_id)->where('key',config('enum.settings')['CUSTOMERORDERCONFIRMATION'])->where('value',1)->exists()){
            //     CustomerOrderConfirmation::getUrl($order->id,$order->customer_number);
            // }
            $add_on_auto_confirm = \App\Models\AddOn::where('seller_id',$order->seller_id)->where('key',config('enum.add_ons')['AUTOORDERCONFIRMATION'])->where('value','1')->first();
            if($add_on_auto_confirm && !$order_duplicate)
                CustomerOrderConfirmation::autoConfirmProcess($order);
            
            AutoShipped::auto_shipped_created_order($order->id);

            AutoShippingMethodBook::addTag($order);
            
            $order->passthrough();

            //Omni Location Assignment Item wise
            if(AddOn::omniLocationAssignment($order->seller_id)){
                $olDeterminationTrig = Setting::where('seller_id', $order->seller_id)->where('key', config('enum.settings')['OMNILOCATIONDETERMINATIONTRIGGER'])->first();
                if (isset($olDeterminationTrig) && $olDeterminationTrig->value == 'order_creation') {
                    $order->assignLocationToOrderItems();
                }
            }

            //Single Auto Location Auto Assingment
            if(!AddOn::omniLocationAssignment($order->seller_id)){
                $assign_orders_to_ffc_setting = Setting::where('seller_id', $order->seller_id)->where('key', config('enum.settings')['ASSIGN-ORDER-TO-LOCATION'])->first();
                if (isset($assign_orders_to_ffc_setting)) {
                    if ($assign_orders_to_ffc_setting->value == 'order_creation') {
                        $order->assignSingleLocation();
                    }
                }
            }

            \Log::info("WooCommerce | ".$order->id." | Order Created");

            event(new AddressValidationLikelihoodEvent($order));


            if ($hook->customer_note) {
                OrderComments::add($order->id, 'Order Customer Notes', $hook->customer_note , 'Success', '1');
            }

            return "Order Created";
        }else  {
            \Log::info("WooCommerce | Duplicate Order");
            return "Duplicate Order";
        }
    } catch (\Exception $e){
        \Log::info("WooCommerce | ".$e->getMessage());
        $activity_id = activity()
                        ->causedBy(78)
                        ->withProperties(['response' => $e->getMessage(),'dump' => json_encode($request->all())])
                        ->log('WooCommerce');
        Mail::to('<EMAIL>')->send(new IntegrationError($activity_id,'WooCommerce'));
    }
});


Route::post('shopify/product', function(Request $request) {
    
    \Log::info('Shopify product webhook | seller_id: '.session('sp_location_seller_id').' | product webhook begins!');
    \Log::info('Shopify product webhook | seller_id: '.session('sp_location_seller_id').' | '.$request);

    try {

        CreateOrUpdateShopifyProduct::dispatch($request->all(), session('sp_location_seller_id'))->onQueue('create_or_update_shopify_product');

    } catch(Exception $e) {

        \Log::info('Shopify product webhook | seller_id: '.session('sp_location_seller_id').' | product webhook catch block | '.$e->getMessage());
        activity()
        ->causedBy(\Session::get('sp_location_seller_id'))
        ->withProperties(['response' => $e->getMessage(), 'dump' => json_encode($request->all())])
        ->log('Shopify product Webhook');
    }
    \Log::info('Shopify product webhook | seller_id: '.session('sp_location_seller_id').' | product webhook ended!');

})->middleware('splocationapiauth');

Route::post('shopify/product/delete', function(Request $request) {
    
    \Log::critical('Shopify product delete webhook | seller_id: '.session('sp_location_seller_id').' | product delete webhook begins!');
    \Log::critical('Shopify product delete webhook | seller_id: '.session('sp_location_seller_id').' | '.$request);

    try {

        DeleteShopifyProduct::dispatch($request->id, session('sp_location_seller_id'))->onQueue('delete_shopify_product');

    } catch(Exception $e) {

        \Log::critical('Shopify product delete webhook | seller_id: '.session('sp_location_seller_id').' | product delete webhook catch block | '.$e->getMessage());
        activity()
        ->causedBy(\Session::get('sp_location_seller_id'))
        ->withProperties(['response' => $e->getMessage(), 'dump' => json_encode($request->all())])
        ->log('Shopify product Webhook');
    }
    \Log::info('Shopify product delete webhook | seller_id: '.session('sp_location_seller_id').' | product webhook ended!');

})->middleware('splocationapiauth');


Route::post('shopify/location', function(Request $request) {
    \Log::info('Shopify location webhook | seller_id: '.session('sp_location_seller_id').' | location webhook begins!');
    \Log::info('Shopify location webhook | seller_id: '.session('sp_location_seller_id').' | '.$request);
    try{

        if(session('sp_location_topic') == 'create'){
            $city = City::where('name',$request->city)->value('id');
            if($city){
                $seller_location = new SellerLocation;
                $seller_location->seller_id = session('sp_location_seller_id');
                $seller_location->location_type = 'Retail Store';
                $seller_location->location_name = $request->name;
                $seller_location->storefront_id = $request->id;
                $seller_location->phone = $request->phone;
                $seller_location->address = (isset($request->address2)?$request->address2:'').' '.$request->address1;
                $seller_location->city = (isset($city)?$city:'');
                $seller_location->save();
            }
            else{
                \Log::info('Shopify location webhook | seller_id: '.session('sp_location_seller_id').' | city not exist');
            }
        } elseif(session('sp_location_topic') == 'update'){
            $seller_loc = SellerLocation::where('storefront_id',$request->id)->where('seller_id',session('sp_location_seller_id'))->first();
            if($seller_loc){
                $city = City::where('name',$request->city)->value('id');

                $seller_loc->location_name = $request->name;
                $seller_loc->phone = $request->phone;
                $seller_loc->address = (isset($request->address2)?$request->address2:'').' '.$request->address1;
                $seller_loc->city = (isset($city)?$city:'');
                $seller_loc->save();
            } else{
                \Log::info('Shopify location webhook | seller_id: '.session('sp_location_seller_id').' | location does not exist so cannot be updated');
            }
        } elseif(session('sp_location_topic') == 'delete'){
            $seller_loc = SellerLocation::where('storefront_id',$request->id)->where('seller_id',session('sp_location_seller_id'))->first();
            if($seller_loc){
                $seller_loc->delete();
            } else{
                \Log::info('Shopify location webhook | seller_id: '.session('sp_location_seller_id').' | location does not exist so cannot be deleted');
            }
        }
    } catch(Exception $e){
        \Log::info('Shopify location webhook | seller_id: '.session('sp_location_seller_id').' | location webhook catch block | '.$e->getMessage());
        $activity_id = activity()
        ->causedBy(\Session::get('sp_location_seller_id'))
        ->withProperties(['response' => $e->getMessage(), 'dump' => json_encode($request->all())])
        ->log('Shopify Location Webhook');
    }
    \Log::info('Shopify location webhook | seller_id: '.session('sp_location_seller_id').' | location webhook ended!');

})->middleware('splocationapiauth');
Route::post('invegora/order', function (Request $request) {
    
    try {
        $hook = $request;
        $marketplace_reference_id = $hook->id;
        $seller_id = 503;

        $order_check = Order::onWriteConnection()->where('marketplace_reference_id', $marketplace_reference_id)->where('seller_id', $seller_id)->exists();

        if ($order_check) {
            $order = new Order();
            $order->source_id = 7;
            $order->marketplace_id;
            $order->seller_location_id = 1;
            $order->marketplace_reference_id = $hook->id;
            $order->seller_id = $seller_id;
            $order->created_date = date('Y-m-d H:i:s');
            $order->customer_name = ($hook->shipping['first_name'] ? $hook->shipping['first_name'] : 'Not Present').' '.($hook->shipping['last_name'] ? $hook->shipping['last_name'] : ' ');
            $order->customer_email = ($hook->billing['email'] ? $hook->billing['email'] : 'Not Present');
            $order->customer_number = str_replace('+92', '0', $hook->billing['phone']);
            $order->customer_number = ( substr( $order->customer_number, 0, 2 ) == "92" ? "0".substr( $order->customer_number, 2) : $order->customer_number );
            $destintion_city = City::where('name',$hook->shipping['city'])->first();
            $order->destination_city = ($hook->shipping['city'] ? ( $destintion_city ? $destintion_city->name : $hook->shipping['city'] ) : 'Not present');
            $order->shipping_address = ($hook->shipping['address_1'] ? $hook->shipping['address_1'] : ' ') . " " . ($hook->shipping['address_2'] ? $hook->shipping['address_2'] : ' ').' '.$order->destination_city;
            $order->discount = ($hook->discount_total ? $hook->discount_total+$hook->discount_tax : 0);
            $order->grand_total = ($hook->total ? $hook->total : 0);
            $order->cod_payment = ($hook->payment_method == 'cod' ? 1 : 0);
            $order->shipping_fee = ($hook->shipping_total ? $hook->shipping_total : 0);
            $order->status =  config('enum.order_status')['PENDING'];
            $order->placement_date = Carbon::parse($hook->date_created_gmt,'gmt')->setTimezone(config('app.timezone'))->toDateTimeString();

            $payment_method_id = SellerPaymentMethod::where('seller_id',$seller_id)->where('machine_name',$hook->payment_method)->first();
            if($payment_method_id) {
                $order->seller_payment_method_id = $payment_method_id->id;
            }

            foreach($hook->shipping_lines as $line)
            {
                $method_id = SellerShipmentMethod::where('seller_id',$seller_id)->where('machine_name',$line['method_title'])->first();
                if($method_id) {
                    $order->seller_shipment_method_id = $method_id->id;
                }
            }
            $order->customer_number = str_replace(' ','',$order->customer_number);
            $isOrderSaved = $order->save();
            $concatinated_customer_details = $order->customer_number."|".$order->customer_email;

            foreach ($hook->line_items as $item) {
                $orderItem = new OrderItem();
                $orderItem->order_id = $order->id;
                $orderItem->product_id = ($item['product_id'] ? $item['product_id'] : 'Not Present');

                $orderItem->product_name = ($item['name'] ? $item['name'] : 'Not present');
                $orderItem->SKU = ($item['sku'] ? $item['sku'] : 'Not present');
                $orderItem->description;
                $orderItem->description2;
                $orderItem->description3;
                $orderItem->seller_unique_code = ($item['product_id'] ? $item['product_id'] : 'Not Present');
                $orderItem->material_of_product;
                $orderItem->cost = ($item['subtotal'] ? $item['subtotal'] : 0);

                /// For Discount
                $discount = $item['subtotal'] - $item['total'];
                if ($item['subtotal_tax']) {
                    $discount = $discount + ($item['subtotal_tax'] - $item['total_tax']);
                }
                $orderItem->discount = $discount;

                $orderItem->unit_price = ($item['subtotal'] ? ($item['quantity'] ? $item['subtotal'] / $item['quantity'] : 0) : 0);
                $orderItem->quantity = ($item['quantity'] ? $item['quantity'] : 0);
                $orderItem->sub_total = ($item['subtotal'] ? $item['subtotal']-$discount : 0 );

                if(isset($hook->tax_lines[0]['rate_percent'])) {
                    $orderItem->tax_rate = ($hook->tax_lines[0]['rate_percent'] ? $hook->tax_lines[0]['rate_percent'] : 0 );
                } else {
                    $orderItem->tax_rate = 0;
                }
                $orderItem->tax_rate = ($orderItem->tax_rate / 100);
                $orderItem->tax = $item['subtotal_tax'];
                $orderItem->weight = 0;
                $orderItem->status = config('enum.item_status')['PENDING'];
                $orderItem->reason = '';
                $orderItem->save();
                $order_skus[] = $orderItem->SKU;
            }
            
            OrderComments::add($order->id, 'Order Dropped Process', '#'.$order->marketplace_reference_id.' Order Dropped from website source <b>'.$request->header('x-wc-webhook-source').' (WooCommerce)</b>' , 'Success', '1');
    
            sort($order_skus);
            $concatinated_customer_details .= "|".implode('|', $order_skus);
            $concatinated_customer_details_md5_hash = md5($concatinated_customer_details);
            
            $order->order_hash = $concatinated_customer_details_md5_hash;
            $order->is_created = 1;
            $order->save();
    
            //check for order duplication
            $order_duplicate = OmniEngineHoldReason::checkForOrderDuplication($order);

            
            $add_on_auto_confirm = \App\Models\AddOn::where('seller_id',$order->seller_id)->where('key',config('enum.add_ons')['AUTOORDERCONFIRMATION'])->where('value','1')->first();
            if($add_on_auto_confirm && !$order_duplicate)
                CustomerOrderConfirmation::autoConfirmProcess($order);
            
            AutoShippingMethodBook::addTag($order);

            AutoShipped::auto_shipped_created_order($order->id);
            $order->passthrough();

            //Omni Location Assignment Item wise
            if(AddOn::omniLocationAssignment($order->seller_id)){
                $olDeterminationTrig = Setting::where('seller_id', $order->seller_id)->where('key', config('enum.settings')['OMNILOCATIONDETERMINATIONTRIGGER'])->first();
                if (isset($olDeterminationTrig) && $olDeterminationTrig->value == 'order_creation') {
                    $order->assignLocationToOrderItems();
                }
            }

            //Single Auto Location Auto Assingment
            if(!AddOn::omniLocationAssignment($order->seller_id)){
                $assign_orders_to_ffc_setting = Setting::where('seller_id', $order->seller_id)->where('key', config('enum.settings')['ASSIGN-ORDER-TO-LOCATION'])->first();
                if (isset($assign_orders_to_ffc_setting)) {
                    if ($assign_orders_to_ffc_setting->value == 'order_creation') {
                        $order->assignSingleLocation();
                    }
                }
            }

            Log::info('WooCommerce | Invegora | '.$order->id.' | Order Created');

            if ($hook->customer_note) {
                OrderComments::add($order->id, 'Order Customer Notes', $hook->customer_note, 'Success', '1');
            }

            return 'Order Created';
        } else {
            Log::info('WooCommerce | Invegora | Duplicate Order');

            return 'Duplicate Order';
        }
    } catch (\Exception $e) {
        Log::info('WooCommerce Invegora | '.$e->getMessage());
        $activity_id = activity()
                        ->causedBy(503)
                        ->withProperties(['response' => $e->getMessage(), 'dump' => json_encode($request->all())])
                        ->log('WooCommerce Invegora');
        Mail::to('<EMAIL>')->send(new IntegrationError($activity_id, 'WooCommerce Invegora'));
    }
});



Route::post('molty-foam/order', function (Request $request) {
    try {
        $hook = $request;
        $id = explode('#', $hook->name);

        $shopify_product_ids = '';    
        $order_item_variant_ids = [];  
        
        if (count($id) > 1) {
            $id = $id[1];
        } else {
            $id = $id[0];
        }
        $marketplace_reference_id = $id;
        $seller_id = \Session::get('seller_id');
        $order_check = Order::onWriteConnection()->where('marketplace_reference_id', $marketplace_reference_id)->where('seller_id', $seller_id)->count();
        $order_check_temp = Order::where('marketplace_reference_id', $marketplace_reference_id)->where('seller_id', $seller_id)->count();
        \Log::info("Shopify Molty Foam | Reference_id : ".$marketplace_reference_id." | Count : ".$order_check." | Temp Count : ".$order_check_temp." | Seller ID : ".$seller_id);
        
        if(\Session::get('topic') == 'create'){
            if($order_check == 0)  {
                $order = new Order();
                $order->source_id = 1;
                $order->marketplace_id = $hook->id;
                $order->seller_location_id = 1; 
                $order->marketplace_reference_id = $id;
                $order->seller_id = $seller_id;
                $order->created_date = date('Y-m-d H:i:s');
                $order->customer_email = ( isset($hook->customer['email']) && $hook->customer['email'] != "" ? $hook->customer['email'] : Null);
                $order->customer_number = ( substr( $order->customer_number, 0, 2 ) == "92" ? "0".substr( $order->customer_number, 2) : $order->customer_number );
                if(!isset($hook->shipping_address) && isset($hook->billing_address)){
                    $order->customer_name = ( isset($hook->billing_address['first_name']) && $hook->billing_address['first_name'] != "" ? $hook->billing_address['first_name']. " " . $hook->billing_address['last_name'] : Null) ;
                    //HOTFIX: USING LAST NAME AS CONTACT NUMBER FOR A1SHOPPING
                    if($seller_id == 12)
                        $order->customer_number = ( isset($hook->customer['last_name']) && $hook->customer['last_name'] != ""  ? $hook->customer['last_name'] : Null);
                    else
                        $order->customer_number = ( isset($hook->billing_address['phone']) && $hook->billing_address['phone'] != ""  ? str_replace("+92","0",str_replace(" ","",$hook->billing_address['phone'])) : Null);

                    $destintion_city = City::where('name',$hook->billing_address['city'])->first();
                    $order->destination_city = (  isset($hook->billing_address['city']) && $hook->billing_address['city'] != "" ? ( $destintion_city ? $destintion_city->name : $hook->billing_address['city'] ) : Null);
                    $order->shipping_address = (  isset($hook->billing_address['address1']) && $hook->billing_address['address1'] != "" ? $hook->billing_address['address1'] . " " . $hook->billing_address['address2'] : ' ').' '.$order->destination_city;
                    $order->postal_code = ( isset($hook->billing_address['zip']) ? $hook->billing_address['zip'] : Null );
                    $order->country = ( isset($hook->billing_address['country_code']) && $hook->billing_address['country_code'] != "" ? $hook->billing_address['country_code'] : 'PK' );
                }else{
                    $order->customer_name = ( isset($hook->shipping_address['first_name']) && $hook->shipping_address['first_name'] != "" ? $hook->shipping_address['first_name']. " " . $hook->shipping_address['last_name'] : Null) ;
                    //HOTFIX: USING LAST NAME AS CONTACT NUMBER FOR A1SHOPPING
                    if($seller_id == 12)
                        $order->customer_number = ( isset($hook->customer['last_name']) && $hook->customer['last_name'] != "" ? $hook->customer['last_name'] : Null);
                    else
                        $order->customer_number = ( isset($hook->shipping_address['phone']) && $hook->shipping_address['phone'] != "" ? str_replace("+92","0",str_replace(" ","",$hook->shipping_address['phone'])) : Null);
                    $temp_city = ( isset($hook->shipping_address['city']) && $hook->shipping_address['city'] != ""  ? $hook->shipping_address['city'] : Null);
                    $destintion_city = City::where('name',$temp_city)->first();
                    $order->destination_city = ( isset($hook->shipping_address['city']) && $hook->shipping_address['city'] != ""  ? ( $destintion_city ? $destintion_city->name : $hook->shipping_address['city'] ) : Null);
                    $order->shipping_address = ( isset($hook->shipping_address['address1']) && $hook->shipping_address['address1'] != "" ? $hook->shipping_address['address1'] . " " . $hook->shipping_address['address2'] : ' ').' '.$order->destination_city;
                    $order->postal_code = ( isset($hook->shipping_address['zip']) ? $hook->shipping_address['zip'] : Null );
                    $order->country = ( isset($hook->shipping_address['country_code']) && $hook->shipping_address['country_code'] != "" ? $hook->shipping_address['country_code'] : 'PK' );
                }
                $order->discount = ( $hook->total_discounts ? $hook->total_discounts : 0 );
                $order->grand_total = $hook->total_price;
                $order->cod_payment = ($hook->gateway == 'Cash on Delivery (COD)'? 1 : 0) ;
                $order->shipping_fee = ( isset($hook->shipping_lines[0]['price']) && $hook->shipping_lines[0]['price'] != "" ? $hook->shipping_lines[0]['price'] : 0 );
                
                $order->status =  config('enum.order_status')['PENDING'];

                // $order->placement_date = Carbon::parse($hook->created_at)->toDateTimeString() ;
                $order->placement_date = Carbon::parse($hook->created_at)->setTimezone(config('app.timezone'))->toDateTimeString() ;


                $payment_method_id = SellerPaymentMethod::where('seller_id',$seller_id)->where('machine_name',$hook->gateway)->first();
                if($payment_method_id){
                    $order->seller_payment_method_id = $payment_method_id->id;
                    
                }

                foreach($hook->shipping_lines as $line)
                {
                    $method_id = SellerShipmentMethod::where('seller_id',$seller_id)->where('machine_name',$line['title'])->first();
                    if($method_id){
                        $order->seller_shipment_method_id = $method_id->id;
                       
                    }
               
                    
                }
                $order->customer_number = str_replace(' ','',$order->customer_number);
                $order->is_created = 0;
                $isOrderSaved=$order->save();
                $concatinated_customer_details = $order->customer_number."|".$order->customer_email;

                //dd($request->items);
                foreach($hook->line_items as $item)
                {
                    $orderItem = new OrderItem();
                    $orderItem->order_id = $order->id;
                    $orderItem->marketplace_id = $item['id'];
                    $orderItem->product_id = $item['product_id'];
                    $orderItem->product_name = $item['name'];
                    $orderItem->SKU = $item['sku'];
                    $orderItem->description = $item['name'];
                    $orderItem->description2;
                    $orderItem->description3;
                    $orderItem->seller_unique_code = $item['product_id'];
                    $orderItem->material_of_product;
                    $orderItem->cost = ( $item['price'] ? $item['price'] : 0 );
                    

                    /// For Discount
                    $discount = 0;
                    if (count($item['discount_allocations']) > 0) {
                        foreach ($item['discount_allocations'] as $value) {
                            $discount += $value['amount'];
                        }
                    }
                    $orderItem->discount = $discount;


                    /// For Tax
                    $tax = 0;
                    if (count($item['tax_lines']) > 0) {
                        foreach ($item['tax_lines'] as $value) {
                            $tax += $value['price'];
                        }
                        $orderItem->tax = $tax;
                        $tax = $tax / $item['quantity'];
                    }


                    /// For Unit Price
                    if($hook->taxes_included) {
                        $orderItem->unit_price = $item['price']-$tax;
                    } else {
                        $orderItem->unit_price = $item['price'];
                    }
                    
                    
                    $orderItem->quantity = $item['quantity'];
                    $orderItem->sub_total = ($orderItem->unit_price * $item['quantity']) - $discount;
                    $orderItem->weight = ( $item['grams'] ? $item['grams']/1000 : $item['grams'] );
                    $orderItem->status = config('enum.item_status')['PENDING'];
                    $orderItem->reason = "";
                    $orderItem->tax_rate = ( isset($item['tax_lines'][0]['rate']) ? $item['tax_lines'][0]['rate'] : 0 );
                    $orderItem->save();

                    $shopify_product_ids .= ($shopify_product_ids ? ',' : '').$item['product_id'];
                    $order_item_variant_ids[] = ['variant_id' => $item['variant_id'], 'order_item_id' => $orderItem->id ];
                    $order_skus[] = $orderItem->SKU;
                }
                
                OrderComments::add($order->id, 'Order Dropped Process', '#'.$order->marketplace_reference_id.' Order Dropped from website source <b>'.$request->header('X-Shopify-Shop-Domain').' (Shopify)</b> with <b>'.( $hook->gateway ? $hook->gateway : 'NULL' ).'</b> as its payment gateway and <b>'.$hook->total_outstanding.'</b> is its outstanding amount' , 'Success', '1');
        
                sort($order_skus);
                $concatinated_customer_details .= "|".implode('|', $order_skus);
                $concatinated_customer_details_md5_hash = md5($concatinated_customer_details);
                
                $order->order_hash = $concatinated_customer_details_md5_hash;
                $order->is_created = 1;
                $order->save();


                /// Adding Note into Comments
                if (isset($hook->note) && $hook->note) {
                    OrderComments::add($order->id, 'Order Customer Note', $hook->note, 'Success', '1');
                }

                /// Adding additional note into comments
                $note_attributes = "";
                if (isset($hook->note_attributes) && $hook->note_attributes) {

                    foreach ($hook->note_attributes as $value) {
                        $note_attributes .= $value['value']."<br>";
                    }

                    OrderComments::add($order->id, 'Order Additional Customer Note', $note_attributes, 'Success', '1');
                }
        
                //check for order duplication
                $order_duplicate = OmniEngineHoldReason::checkForOrderDuplication($order);
                

                $add_on_auto_confirm = \App\Models\AddOn::where('seller_id',$order->seller_id)->where('key',config('enum.add_ons')['AUTOORDERCONFIRMATION'])->where('value','1')->first();
                if($add_on_auto_confirm && !$order_duplicate)
                    CustomerOrderConfirmation::autoConfirmProcess($order);
                
                AutoShippingMethodBook::addTag($order);
                
                AutoShipped::auto_shipped_created_order($order->id);
                $order->passthrough();

                //Omni Location Assignment Item wise
                if(AddOn::omniLocationAssignment($order->seller_id)){
                    $olDeterminationTrig = Setting::where('seller_id', $order->seller_id)->where('key', config('enum.settings')['OMNILOCATIONDETERMINATIONTRIGGER'])->first();
                    if (isset($olDeterminationTrig) && $olDeterminationTrig->value == 'order_creation') {
                        $order->assignLocationToOrderItems();
                    }
                }

                //Single Auto Location Auto Assingment
                if(!AddOn::omniLocationAssignment($order->seller_id)){
                    $assign_orders_to_ffc_setting = Setting::where('seller_id', $order->seller_id)->where('key', config('enum.settings')['ASSIGN-ORDER-TO-LOCATION'])->first();
                    if (isset($assign_orders_to_ffc_setting)) {
                        if ($assign_orders_to_ffc_setting->value == 'order_creation') {
                            $order->assignSingleLocation();
                        }
                    }
                }

                \Log::info("Shopify Molty Foam | ".$order->id." | Order Created");

                return "Order Created";
            } else  {
                \Log::info("Shopify Molty Foam | Duplicate Order");
                return "Duplicate Order";
            }
            
        }
        elseif(\Session::get('topic') == 'cancel'){
            return ShopifyApp::webhookCancellationOrder($seller_id, $hook, $marketplace_reference_id, $order_check);
        }
        elseif(\Session::get('topic') == 'update'){
            return ShopifyApp::webhookUpdateOrder($seller_id, $hook, $marketplace_reference_id, $order_check);
        }
    } catch (\Exception $e){
        \Log::info("Shopify Molty Foam | ".$e->getMessage());
        \Log::info($e->getTraceAsString());
        $activity_id = activity()
                        ->causedBy(\Session::get('seller_id'))
                        ->withProperties(['response' => $e->getMessage(), 'dump' => json_encode($request->all())])
                        ->log('Shopify Molty Foam');
        Mail::to('<EMAIL>')->send(new IntegrationError($activity_id, 'Shopify Molty Foam'));

        return $e->getMessage();
    }
})->middleware('mfapiauth');

Route::post('molty-foam/magento/order', function(Request $request) {
    $hook=$request->getContent();
    $hook=json_decode($hook);
    $order_check = Order::onWriteConnection()->where('marketplace_reference_id', $hook->OrderData->increment_id)->where('seller_id', 802);
    if($order_check->count() > 0) {
        \Log::info("Molty Foam Magento 1.9 | Duplicate Order | " . $hook->OrderData->increment_id);
        return response()->json(['error' => 'Not Synced | Molty Foam Magento 1.9 Duplicate Order | ' . $hook->OrderData->increment_id], 200);
    }
    try{
        $order = new Order();
        $order->source_id = 1;
        $order->marketplace_id;
        $order->seller_location_id = 1; 
        $order->marketplace_reference_id = ( $hook->OrderData->increment_id ? $hook->OrderData->increment_id : 'Not Present' );
        $order->seller_id = 802;
        $order->created_date = date('Y-m-d H:i:s');
        $order->customer_name = ( $hook->OrderShipping->firstname ? $hook->OrderShipping->firstname : 'Not Present') . " " . ( $hook->OrderShipping->middlename ? $hook->OrderShipping->middlename : ' ' )  . " " . ( $hook->OrderShipping->lastname ? $hook->OrderShipping->lastname : ' ' );
        $order->customer_email = ( $hook->OrderData->customer_email ? $hook->OrderData->customer_email : 'Not Present' );
        $order->customer_number = ( $hook->OrderShipping->telephone ? str_replace("+92","0",$hook->OrderShipping->telephone) : 'Not Present' ) ;
        $order->customer_number = ( substr( $order->customer_number, 0, 2 ) == "92" ? "0".substr( $order->customer_number, 2) : $order->customer_number );
        
        $destintion_city = City::where('name',$hook->OrderShipping->city)->first();
        $order->destination_city = ( $hook->OrderShipping->city ?  ( $destintion_city ? $destintion_city->name : $hook->OrderShipping->city ) : 'Not Present' );
        $order->shipping_address = ( $hook->OrderShipping->street ? $hook->OrderShipping->street : ' ' ).' '.$order->destination_city;
        $order->discount = ($hook->OrderData->base_discount_amount ? $hook->OrderData->base_discount_amount : 0);
        $order->grand_total = ($hook->OrderData->grand_total ? $hook->OrderData->grand_total : 0);
        $order->total_invoiced = ($hook->OrderData->base_grand_total ? $hook->OrderData->base_grand_total : 0);
        $order->cod_payment = ($hook->OrderPayment == 'Cash On Delivery'? 1 : 0) ;
        $order->shipping_fee = ($hook->OrderData->base_shipping_amount ? $hook->OrderData->base_shipping_amount : 0);
        $order->status =  config('enum.order_status')['PENDING'];

        // $order->placement_date = $hook->OrderData->created_at ;
        $order->placement_date = Carbon::parse($hook->OrderData->created_at,'utc')->setTimezone(config('app.timezone'))->toDateTimeString() ;

        $payment_method_id = SellerPaymentMethod::where('seller_id',802)->where('machine_name',$hook->OrderPayment)->first();
        if($payment_method_id){
            $order->seller_payment_method_id = $payment_method_id->id;
            
        }

        $method_id = SellerShipmentMethod::where('seller_id',802)->where('machine_name',$hook->OrderData->shipping_description)->first();
        if($method_id){
            $order->seller_shipment_method_id = $method_id->id;
            
        }

        $order->currency = ($hook->OrderData->base_currency_code ? $hook->OrderData->base_currency_code : 'PKR');
        $isOrderSaved=$order->save();
        $concatinated_customer_details = $order->customer_number."|".$order->customer_email;

        //dd($request->items);
        foreach($hook->items as $item)
        {
            $orderItem = new OrderItem();
            $orderItem->order_id = $order->id;
            $orderItem->product_id = ($item->product_id ? $item->product_id : 'Not Present');


            $orderItem->product_name = ($item->name ? $item->name : 'Not Present');
            $orderItem->SKU = ($item->sku ? $item->sku : 'Not Present');

            $pOptions_text = '';
            $pOptions = unserialize($item->product_options);

            if(array_key_exists('options', $pOptions))    {
                foreach ($pOptions['options'] as $pOption) {
                    $pOptions_text .= $pOption['label'].' = '.$pOption['value'];
                }
            }

            if(array_key_exists('attributes_info', $pOptions))    {
                $pOptions_text .= ' ';

                foreach ($pOptions['attributes_info'] as $pAttribute) {
                    $pOptions_text .= $pAttribute['label'].' = '.$pAttribute['value'];
                }
            }
             
            //dd($pOptions_text);

            //traverseArray($pOptions, $ss);
            $orderItem->description = $pOptions_text;            

            $orderItem->description2;
            $orderItem->description3;
            $orderItem->seller_unique_code = ($item->product_id ? $item->product_id : 'Not Present');
            $orderItem->material_of_product;
            $orderItem->cost = $item->base_cost==null? 0 : $item->base_cost;
            

            
            /// For Discount
            $discount = $item->original_price - $item->price;
            if ($discount) {
                $discount = $discount * $item->qty_ordered;
            }
            $orderItem->discount = $discount + ($item->base_discount_amount ? $item->base_discount_amount : 0);

            $orderItem->unit_price = ($item->original_price ? $item->original_price : 0);
            $orderItem->quantity = ($item->qty_ordered ? $item->qty_ordered : 0);
            $orderItem->sub_total = ($orderItem->unit_price * $item->qty_ordered) - $orderItem->discount;
            $orderItem->weight = ($item->weight ? $item->weight : 0);
            $orderItem->status = config('enum.item_status')['PENDING'];
            $orderItem->reason = "";

            $orderItem->tax = ($item->tax_amount ? $item->tax_amount : 0) + (isset($item->hidden_tax_amount) ? $item->hidden_tax_amount : 0);
            $orderItem->tax_rate = ($item->tax_percent ? $item->tax_percent : 0);
            $orderItem->tax_rate = ($item->tax_percent / 100);

            $orderItem->save();
            $order_skus[] = $orderItem->SKU;
        }
        
        OrderComments::add($order->id, 'Order Dropped Process', '#'.$order->marketplace_reference_id.' Order Dropped from website source <b>'.$request->header('X-Origin-Url').' (Magento 1.9)</b>' , 'Success', '1');

        sort($order_skus);
        $concatinated_customer_details .= "|".implode('|', $order_skus);
        $concatinated_customer_details_md5_hash = md5($concatinated_customer_details);
        
        $order->order_hash = $concatinated_customer_details_md5_hash;
        $order->is_created = 1;
        $order->save();

        //check for order duplication
        $order_duplicate = OmniEngineHoldReason::checkForOrderDuplication($order);
        
        $add_on_auto_confirm = \App\Models\AddOn::where('seller_id',$order->seller_id)->where('key',config('enum.add_ons')['AUTOORDERCONFIRMATION'])->where('value','1')->first();
        if($add_on_auto_confirm && !$order_duplicate)
            CustomerOrderConfirmation::autoConfirmProcess($order);
        
        AutoShippingMethodBook::addTag($order);

        AutoShipped::auto_shipped_created_order($order->id);
        $order->passthrough();

        //Omni Location Assignment Item wise
        if(AddOn::omniLocationAssignment($order->seller_id)){
            $olDeterminationTrig = Setting::where('seller_id', $order->seller_id)->where('key', config('enum.settings')['OMNILOCATIONDETERMINATIONTRIGGER'])->first();
            if (isset($olDeterminationTrig) && $olDeterminationTrig->value == 'order_creation') {
                $order->assignLocationToOrderItems();
            }
        }

        //Single Auto Location Auto Assingment
        if(!AddOn::omniLocationAssignment($order->seller_id)){
            $assign_orders_to_ffc_setting = Setting::where('seller_id', $order->seller_id)->where('key', config('enum.settings')['ASSIGN-ORDER-TO-LOCATION'])->first();
            if (isset($assign_orders_to_ffc_setting)) {
                if ($assign_orders_to_ffc_setting->value == 'order_creation') {
                    $order->assignSingleLocation();
                }
            }
        }

        \Log::info("Molty Foam Magento 1.9 | ".$order->id." | Order Created");
        return "Order Successfully synced into unity Molty Foam Magento 1.9 Account";
    }
    catch(Exception $e) {
        \Log::info("Molty Foam Magento 1.9 | ".$e->getMessage());
        echo 'Message: ' .$e->getMessage();
        $activity_id = activity()
                        ->causedBy(802)
                        ->withProperties(['response' => $e->getMessage(), 'dump' => json_encode($request->all())])
                        ->log('Molty Foam Magento 1.9');
        Mail::to('<EMAIL>')->send(new IntegrationError($activity_id,'Ace Galleria'));
      }
       
});

Route::post('jafferjees/international/order', function(Request $request) {
        
    \Log::info("Jaferjees International Order Header | ".json_encode($request->header()));
    \Log::info("Jaferjees International Order Request | ".json_encode($request->all()));

    $hook=$request->getContent();
    $hook=json_decode($hook);
    $seller_id= \Session::get('seller_id');
    $order_check = Order::onWriteConnection()->where('marketplace_reference_id', $hook->order_id)->where('seller_id', $seller_id);
    \Log::info('Seller ID = '.\Session::get('seller_id').' | Jafferjees order DUMP '.json_encode($request->all()));

    if($order_check->count() > 0) {
        \Log::info("Jaffer Jees | Duplicate Order | " . $hook->order_id);
        return response()->json(['error' => 'Not Synced | Jaffer Jees Duplicate Order | ' . $hook->order_id], 200);
    }
    \Log::info("Jaffer Jees | Reference_id : ".$hook->order_id." | Count : ".$order_check->count()." | Seller ID : ".$seller_id);
    try{
        $order = new Order();
        $order->source_id = 1;
        $order->marketplace_id;
        $order->seller_location_id = 1; 
        $order->marketplace_reference_id = ( $hook->order_id ? $hook->order_id : 'Not Present' );
        $order->seller_id = \Session::get('seller_id');
        $order->created_date = date('Y-m-d H:i:s');
        $order->customer_name = ( $hook->shipping_firstname ? $hook->shipping_firstname : 'Not Present') . " " . ( $hook->shipping_lastname ? $hook->shipping_lastname : ' ' );
        $order->customer_email = ( $hook->email ? $hook->email : 'Not Present' );
        
        $order->customer_number = ( $hook->telephone ? str_replace("+92","0",$hook->telephone ) : 'Not Present' ) ;
        $order->customer_number = ( substr( $order->customer_number, 0, 2 ) == "92" ? "0".substr( $order->customer_number, 2) : $order->customer_number );

        $hook->shipping_city = str_replace("'", '', $hook->shipping_city);
        // $hook->OrderShipping->street = str_replace("'", '', $hook->OrderShipping->street);

        $destintion_city = City::where('name',$hook->shipping_city)->first();
        $order->destination_city = ( $hook->shipping_city ?  $hook->shipping_city  : 'Not Present' );
        $order->shipping_address = ( $hook->shipping_address_1 ? $hook->shipping_address_1 : ' ' ). ' '.( $hook->shipping_address_2 ? $hook->shipping_address_2 : '' ).' '.$order->destination_city;
        $order->discount = ($hook->discount ? $hook->discount : 0);
        $order->grand_total = ($hook->total ? $hook->total : 0);
        $order->total_invoiced = 0;
        $order->cod_payment = ($hook->payment_method == 'Cash On Delivery (For UAE only)' ? 1 : 0) ;
        $order->shipping_fee = ($hook->shipping_charges ? $hook->shipping_charges : 0);
        $order->status =  config('enum.order_status')['PENDING'];

        // $order->placement_date = $hook->OrderData->created_at ;
        $order->placement_date = Carbon::parse($hook->date_added,'utc')->setTimezone(config('app.timezone'))->toDateTimeString() ;

        $payment_method_id = SellerPaymentMethod::where('seller_id',\Session::get('seller_id'))->where('machine_name',$hook->payment_method)->first();
        if($payment_method_id){
            $order->seller_payment_method_id = $payment_method_id->id;
            
        }


        $method_id = SellerShipmentMethod::where('seller_id',\Session::get('seller_id'))->where('machine_name',$hook->shipping_method)->first();
        if($method_id){
            $order->seller_shipment_method_id = $method_id->id;
            
        }

        $order->currency = ($hook->currency ? $hook->currency : 'PKR');
        $order->country = ( $hook->shipping_iso_code_2 ? $hook->shipping_iso_code_2 : 'PK' );

        $isOrderSaved=$order->save();
        $concatinated_customer_details = $order->customer_number."|".$order->customer_email;

        //dd($request->items);
        foreach($hook->products as $item)
        {
            $orderItem = new OrderItem();
            $orderItem->order_id = $order->id;
            $orderItem->product_id = ($item->product_id ? $item->product_id : 'Not Present');


            $orderItem->product_name = ($item->name ? $item->name : 'Not Present');
            $orderItem->SKU = ($item->name ? $item->name : 'Not Present');
            //var_dump($item->product_options);die;
            $pOptions_text = '';
            //$pOptions = unserialize($item->product_options);

            // if(isset($item->product_options->options))    {
            //     foreach ($item->product_options->options as $pOption) {
            //         $pOptions_text .= $pOption->label.' = '.$pOption->value;
            //     }
            // }

            // if(isset($item->product_options->attributes_info))    {
            //     $pOptions_text .= ' ';

            //     foreach ($item->product_options->attributes_info as $pAttribute) {
            //         $pOptions_text .= $pAttribute->label . ' = '. $pAttribute->value;
            //     }
            // }
             
            //dd($pOptions_text);

            //traverseArray($pOptions, $ss);
            $orderItem->description = "";            

            $orderItem->description2;
            $orderItem->description3;
            $orderItem->seller_unique_code = ($item->product_id ? $item->product_id : 'Not Present');
            $orderItem->material_of_product;
            $orderItem->cost = 0;
            
            /// For Discount
            if(isset($item->discounted_price) && $item->discounted_price > 0){
                $discount = $item->discounted_price;
            }else{
                if($order->discount > 0){
                    $discount = $order->discount / count($hook->products);
                }else{
                    $discount = 0;
                }
            }

            $orderItem->discount = $discount;

            $orderItem->unit_price = ( $item->product_price ? $item->product_price : 0);
            $orderItem->quantity = ($item->quantity ? $item->quantity : 0);
            $orderItem->sub_total =  ($item->total ? $item->total : 0);
            $orderItem->weight =0;
            $orderItem->status = config('enum.item_status')['PENDING'];
            $orderItem->reason = "";
            $tax = ($item->tax ? $item->tax : 0);
            $tax_amount = ($tax ? ($tax / 100) * $item->total  : 0);
            $orderItem->tax =  $tax_amount;
            $orderItem->tax_rate =  $tax;
            $orderItem->save();
            $order_skus[] = $orderItem->SKU;
        }
        
        OrderComments::add($order->id, 'Order Dropped Process', '#'.$order->marketplace_reference_id.' Order Dropped from website source <b>'.$request->header('X-Origin-Url').' (Magento 2)</b>' , 'Success', '1');

        sort($order_skus);
        $concatinated_customer_details .= "|".implode('|', $order_skus);
        $concatinated_customer_details_md5_hash = md5($concatinated_customer_details);
        
        $order->order_hash = $concatinated_customer_details_md5_hash;
        $order->is_created = 1;
        $order->save();

        //check for order duplication
        $order_duplicate = OmniEngineHoldReason::checkForOrderDuplication($order);

        $add_on_auto_confirm = \App\Models\AddOn::where('seller_id',$order->seller_id)->where('key',config('enum.add_ons')['AUTOORDERCONFIRMATION'])->where('value','1')->first();
        if($add_on_auto_confirm && !$order_duplicate)
            CustomerOrderConfirmation::autoConfirmProcess($order);
        

        AutoShippingMethodBook::addTag($order);

        AutoShipped::auto_shipped_created_order($order->id);
        $order->passthrough();
        \Log::info("Jaffer Jees International | ".$order->id." | Order Created");
        return "Order Successfully synced into unity Jaffer Jees International Account";
    }
    catch(Exception $e) {
        \Log::info("Jaffer Jees | ".$e->getMessage());
        echo 'Message: ' .$e->getMessage();
        $activity_id = activity()
                        ->causedBy(\Session::get('seller_id'))
                        ->withProperties(['response' => $e->getMessage(), 'dump' => json_encode($request->all())])
                        ->log('Jaffer Jees');
        Mail::to('<EMAIL>')->send(new IntegrationError($activity_id,'Jaffer Jees International'));
    }

})->middleware('jafferjeesintapiauth');


Route::get('cxrobo/{sellerid}/{orderid}/{input}', function($sellerid, $orderid, $input) {
    
    $order = Order::where('marketplace_reference_id', $orderid)->where("seller_id", $sellerid)->first();

    if($order) {

        $message = 'Undefined input '.$input;

        if ($input == 1) {
            $tag = Tag::whereSellerId($sellerid)->whereValue('Confirmed')->select('id', 'color', 'value')->first();
            $message = 'Confirmed';
        } elseif ($input == 3) {
            $tag = Tag::whereSellerId($sellerid)->whereValue('Cancelled')->select('id', 'color', 'value')->first();
            $message = 'Cancelled';
        } elseif ($input == 2) {
            $tag = Tag::whereSellerId($sellerid)->whereValue('Call Back Requested')->select('id', 'color', 'value')->first();
            $message = 'Call Back Requested';
        }

        if (isset($tag['id'])) {
            $ordertag = new App\Models\OrderTag;
            $ordertag->updateOrCreate(['order_id' => $order->id], ['tag_id' => $tag['id']]);
            RecordOrderConfirmation::add($order->seller_id,$order->id,'Robocall',2);
            OrderComments::add($order->id, 'Order Tag Assignment Process', '<span class="label '.$tag['color'].'">'.$tag['value'].'</span> Tag has been assigned', 'Success', 1);
            OrderTag::tag_check($order->id, $tag);
        }

        $orderComments = new OrderComments();
        $orderComments->order_id = $order->id;
        $orderComments->key = 'Robocall Process';
        $orderComments->value = $message.' (CX-Connect)';
        $orderComments->status = 'Success';
        $orderComments->run_by = '1';
        $orderComments->save();
    } else {
        \Log::info('Robocall Process Order Not Found | #'.$orderid.' | Seller '.$sellerid);

        return 'Order Not Found';
    }

    return 'ok';
})->middleware('cxauth');

Route::get('robocall/eocean/{sellerid}/{orderid}/{input}', function ($sellerid, $orderid, $input) {
    \Log::info('Robocall response received :');
    \Log::info($sellerid."---".$orderid."---".$input);
    $order = Order::where('marketplace_reference_id', $orderid)->where('seller_id', $sellerid)->first();

    if ($order) {
        if(!SellerOrderConfirmation::where('seller_id',$order->seller_id)->where('order_id',$order->id)->where('confirmed',1)->exists()){
            $tagg = Tag::whereSellerId($order->seller_id)->where('value','Confirmed')->where('key','order')->select('id')->first();
            $message = 'Undefined input '.$input;

            if ($input == 1) {
                $tag = Tag::whereSellerId($sellerid)->whereValue('Confirmed')->select('id','color','value')->first();
                $message = 'Confirmed';
            } elseif ($input == 3 || $input == 0) {
                $tag = Tag::whereSellerId($sellerid)->whereValue('Cancelled')->select('id','color','value')->first();
                $message = 'Cancelled';
                //this is to cater auto order cancelation if enabled in settings
                $add_on_auto_order_cancellation = \App\Models\Setting::where('seller_id',$order->seller_id)->where('key',config('enum.settings')['AUTOCUSTOMERCANCELLATION'])->where('value','1')->first();
                if($add_on_auto_order_cancellation){
                    $orderController = new OrderController;
                    $orderController->cancelOrderByCustomer($order);
                }
            } elseif ($input == 2) {
                $tag = Tag::whereSellerId($sellerid)->whereValue('Call Back Requested')->select('id','color','value')->first();
                $message = 'Call Back Requested';
            }

            if (isset($tag['id'])) {
                $ordertag = new App\Models\OrderTag;
                $ordertag->updateOrCreate(['order_id' => $order->id], ['tag_id' => $tag['id']]);
                if($input != 2){
                    RecordOrderConfirmation::add($order->seller_id,$order->id,'Robocall',2);
                }
                OrderComments::add($order->id, 'Order Tag Assignment Process', '<span class="label '.$tag['color'].'">'.$tag['value'].'</span> Tag has been assigned', 'Success', 1);
                OrderTag::tag_check($order->id, $tag);
            }

            $orderComments = new OrderComments();
            $orderComments->order_id = $order->id;
            $orderComments->key = 'Robocall Process';
            $orderComments->value = $message.' (E-Ocean)';
            $orderComments->status = 'Success';
            $orderComments->run_by = '1';
            $orderComments->save();
            
            //Customer Intimation Trigger
            if ($input == 3) {
                CustomerIntimationService::execute($order->seller_id, [
                    'event' => 'order_cancellation',
                    'marketplace_reference_id' => $order->marketplace_reference_id,
                    'order_id' => $order->id,
                    'customer_email' => $order->customer_email,
                    'customer_name' => $order->customer_name,
                    'customer_number' => $order->customer_number
                ]);
            }

        } else{

        }
    } else {
        return 'Order Not Found';
    }

    return 'ok';
}); //->middleware('cxauth');

Route::post('robocall/its', function(Request $request) {

    \Log::info('Robocall response received :');

    if(!$request->has('order_id') || !$request->has('callinput') || !$request->has('seller_id'))
    {
        return response()->json(['message' => 'Parameters Missing'],400);
    }
    $orderid = $request->order_id;
    $input = $request->callinput;
    $sellerid = $request->seller_id;
    

    \Log::info($sellerid."---".$orderid."---".$input);


    $order = Order::find($orderid);

    if ($order) {
        if(!SellerOrderConfirmation::where('seller_id',$order->seller_id)->where('order_id',$order->id)->where('confirmed',1)->exists()){
            $tagg = Tag::whereSellerId($order->seller_id)->where('value','Confirmed')->where('key','order')->select('id')->first();
            $message = 'Undefined input '.$input;

            if ($input == 1) {
                $tag = Tag::whereSellerId($sellerid)->whereValue('Confirmed')->select('id','color','value')->first();
                $message = 'Confirmed';
            } elseif ($input == 2) {
                $tag = Tag::whereSellerId($sellerid)->whereValue('Cancelled')->select('id','color','value')->first();
                $message = 'Cancelled';
                //this is to cater auto order cancelation if enabled in settings
                $add_on_auto_order_cancellation = \App\Models\Setting::where('seller_id',$order->seller_id)->where('key',config('enum.settings')['AUTOCUSTOMERCANCELLATION'])->where('value','1')->first();
                if($add_on_auto_order_cancellation){
                    $orderController = new OrderController;
                    $orderController->cancelOrderByCustomer($order);
                }
            } 

            if (isset($tag['id'])) {
                $ordertag = new App\Models\OrderTag;
                $ordertag->updateOrCreate(['order_id' => $order->id], ['tag_id' => $tag['id']]);
                RecordOrderConfirmation::add($order->seller_id,$order->id,'Robocall',2);
                OrderComments::add($order->id, 'Order Tag Assignment Process', '<span class="label '.$tag['color'].'">'.$tag['value'].'</span> Tag has been assigned', 'Success', 1);
                OrderTag::tag_check($order->id, $tag);
            }

            $orderComments = new OrderComments();
            $orderComments->order_id = $order->id;
            $orderComments->key = 'Robocall Process';
            $orderComments->value = $message.' (ITS)';
            $orderComments->status = 'Success';
            $orderComments->run_by = '1';
            $orderComments->save();


            //Customer Intimation Trigger
            if ($input == 2) {
                CustomerIntimationService::execute($order->seller_id, [
                    'event' => 'order_cancellation',
                    'marketplace_reference_id' => $order->marketplace_reference_id,
                    'order_id' => $order->id,
                    'customer_email' => $order->customer_email,
                    'customer_name' => $order->customer_name,
                    'customer_number' => $order->customer_number
                ]);
            }

        } else{

        }
    } else {
        return 'Order Not Found';
    }

    return ['error' => 0, 'message' => 'Input Received'];

})->withoutMiddleware('throttle:32000,1');


Route::post('robocall/hthree', function(Request $request) {

    \Log::info('Robocall response received :');

    if(!$request->has('order_id') || !$request->has('dtmf') || !$request->has('seller_id'))
    {
        return response()->json(['message' => 'Parameters Missing'],400);
    }
    $orderid = $request->order_id;
    $input = $request->dtmf;
    $sellerid = $request->seller_id;
    

    \Log::info($sellerid."---".$orderid."---".$input);


    $order = Order::find($orderid);

    if ($order) {
        if(!SellerOrderConfirmation::where('seller_id',$order->seller_id)->where('order_id',$order->id)->where('confirmed',1)->exists()){
            $tagg = Tag::whereSellerId($order->seller_id)->where('value','Confirmed')->where('key','order')->select('id')->first();
            $message = 'Undefined input '.$input;

            if ($input == 1) {
                $tag = Tag::whereSellerId($sellerid)->whereValue('Confirmed')->select('id','color','value')->first();
                $message = 'Confirmed';
            } elseif ($input == 3) {
                $tag = Tag::whereSellerId($sellerid)->whereValue('Cancelled')->select('id','color','value')->first();
                $message = 'Cancelled';
                //this is to cater auto order cancelation if enabled in settings
                $add_on_auto_order_cancellation = \App\Models\Setting::where('seller_id',$order->seller_id)->where('key',config('enum.settings')['AUTOCUSTOMERCANCELLATION'])->where('value','1')->first();
                if($add_on_auto_order_cancellation){
                    $orderController = new OrderController;
                    $orderController->cancelOrderByCustomer($order);
                }
            } elseif ($input == 2) {
                $tag = Tag::whereSellerId($sellerid)->whereValue('Call Back Requested')->select('id','color','value')->first();
                $message = 'Call Back Requested';
            }

            if (isset($tag['id'])) {
                $ordertag = new App\Models\OrderTag;
                $ordertag->updateOrCreate(['order_id' => $order->id], ['tag_id' => $tag['id']]);
                if($input != 2){
                    RecordOrderConfirmation::add($order->seller_id,$order->id,'Robocall',2);
                }                OrderComments::add($order->id, 'Order Tag Assignment Process', '<span class="label '.$tag['color'].'">'.$tag['value'].'</span> Tag has been assigned', 'Success', 1);
                OrderTag::tag_check($order->id, $tag);
            }

            $orderComments = new OrderComments();
            $orderComments->order_id = $order->id;
            $orderComments->key = 'Robocall Process';
            $orderComments->value = $message.' (H3)';
            $orderComments->status = 'Success';
            $orderComments->run_by = '1';
            $orderComments->save();


            //Customer Intimation Trigger
            if ($input == 3) {
                CustomerIntimationService::execute($order->seller_id, [
                    'event' => 'order_cancellation',
                    'marketplace_reference_id' => $order->marketplace_reference_id,
                    'order_id' => $order->id,
                    'customer_email' => $order->customer_email,
                    'customer_name' => $order->customer_name,
                    'customer_number' => $order->customer_number
                ]);
            }

        } else{

        }
    } else {
        return 'Order Not Found';
    }

    return ['error' => 0, 'message' => 'Input Received'];

})->withoutMiddleware('throttle:32000,1');


Route::post('robocall/hthree-merchant', function(Request $request) {

    \Log::info('Robocall Hthree merchant response received :');

    if(!$request->has('order_id') || !$request->has('dtmf') || !$request->has('seller_id'))
    {
        return response()->json(['message' => 'Parameters Missing'],400);
    }
    $orderid = $request->order_id;
    $input = $request->dtmf;
    $sellerid = $request->seller_id;
    

    \Log::info($sellerid."---".$orderid."---".$input);


    $order = Order::find($orderid);

    if ($order) {
        if(!SellerOrderConfirmation::where('seller_id',$order->seller_id)->where('order_id',$order->id)->where('confirmed',1)->exists()){
            $tagg = Tag::whereSellerId($order->seller_id)->where('value','Confirmed')->where('key','order')->select('id')->first();
            $message = 'Undefined input '.$input;

            if ($input == 1) {
                $tag = Tag::whereSellerId($sellerid)->whereValue('Confirmed')->select('id','color','value')->first();
                $message = 'Confirmed';
            } elseif ($input == 2) {
                $tag = Tag::whereSellerId($sellerid)->whereValue('Cancelled')->select('id','color','value')->first();
                $message = 'Cancelled';
                //this is to cater auto order cancelation if enabled in settings
                $add_on_auto_order_cancellation = \App\Models\Setting::where('seller_id',$order->seller_id)->where('key',config('enum.settings')['AUTOCUSTOMERCANCELLATION'])->where('value','1')->first();
                if($add_on_auto_order_cancellation){
                    $orderController = new OrderController;
                    $orderController->cancelOrderByCustomer($order);
                }
            } elseif ($input == 3) {
                $tag = Tag::whereSellerId($sellerid)->whereValue('Call Back Requested')->select('id','color','value')->first();
                $message = 'Call Back Requested';
            }

            if (isset($tag['id'])) {
                $ordertag = new App\Models\OrderTag;
                $ordertag->updateOrCreate(['order_id' => $order->id], ['tag_id' => $tag['id']]);
                if($input != 3){
                    RecordOrderConfirmation::add($order->seller_id,$order->id,'Robocall',2);
                }                
                OrderComments::add($order->id, 'Order Tag Assignment Process', '<span class="label '.$tag['color'].'">'.$tag['value'].'</span> Tag has been assigned', 'Success', 1);
                OrderTag::tag_check($order->id, $tag);
            }

            $orderComments = new OrderComments();
            $orderComments->order_id = $order->id;
            $orderComments->key = 'Robocall Process';
            $orderComments->value = $message.' (H3)';
            $orderComments->status = 'Success';
            $orderComments->run_by = '1';
            $orderComments->save();


            //Customer Intimation Trigger
            if ($input == 2) {
                CustomerIntimationService::execute($order->seller_id, [
                    'event' => 'order_cancellation',
                    'marketplace_reference_id' => $order->marketplace_reference_id,
                    'order_id' => $order->id,
                    'customer_email' => $order->customer_email,
                    'customer_name' => $order->customer_name,
                    'customer_number' => $order->customer_number
                ]);
            }

        } else{

        }
    } else {
        return 'Order Not Found';
    }

    return ['error' => 0, 'message' => 'Input Received'];

})->withoutMiddleware('throttle:32000,1');

Route::post('robocall', function (Request $request) {
    foreach ($request->ids as $id) {
        $order = Order::find($id);
        if($order->status == config('enum.order_status')['PENDING']){
            $seller = Seller::find($order->seller_id);
            $client = new \GuzzleHttp\Client(['verify' => false]);

            $setting = Setting::where('key', 'roboCall')->whereSellerId($order->seller_id);

            if ($setting->exists()) {
                $setting = $setting->first();

                if ($setting->value == config('enum.robo_call')['CXCONNECT']) {
                    $response = $client->post(env('ROBOCALL_URL').'/api/request/outboundcalldata/', [
                        'http_errors' => false,
                        'form_params' => [
                            'phoneno' => $order->customer_number,
                            'orderreferenceid' => $order->marketplace_reference_id,
                            'orderid' => $order->id,
                            'customerno' => $order->customer_number,
                            'clientname' => $seller->company_name,
                            'orderamount' => round($order->grand_total),
                            'sellerid' => $order->seller_id						]
                    ]);

                    $message = 'Call Initiated (CX Connect)';
                } elseif ($setting->value == config('enum.robo_call')['EOCEAN']) {
                    \Log::info("Robocall Logs started");
                    \Log::info('https://ivr.eocean.net/cgi-bin/Unity_Retail/outbound.cgi?seller_id='.$order->seller_id.'&caller_id='.$order->customer_number.'&order_id='.$order->marketplace_reference_id.'&amount='.round($order->grand_total).'&username=Unity&password=Unity@EoRetail');
                    $response = $client->get('https://ivr.eocean.net/cgi-bin/Unity_Retail/outbound.cgi?seller_id='.$order->seller_id.'&caller_id='.$order->customer_number.'&order_id='.urlencode($order->marketplace_reference_id).'&amount='.round($order->grand_total).'&username=Unity&password=Unity@EoRetail');
                    \Log::info(json_encode($response));
                    $statusCode = $response->getStatusCode();
                    \Log::info("Robocall Response code is");
                    \Log::info($statusCode);
                    \Log::info("Robocall Logs completed");

                    $message = 'Call Initiated (E-Ocean)';
                } elseif ($setting->value == config('enum.robo_call')['ITS']) {
                    \Log::info("ITS Robocall Logs started");
                    $camp_id = 400;
                    if($order->seller_id == 410){
                        $camp_id = 404;
                    }
                    \Log::info('https://voicegateway.its.com.pk/api?ApiKey=B3908C3DB2CEEFEC1B84E90AC2963AA2&Recipient='.$order->customer_number.'&CampId='.$camp_id.'&UniqueId='.urlencode($order->marketplace_reference_id));
                    $response = $client->get('https://voicegateway.its.com.pk/api?ApiKey=B3908C3DB2CEEFEC1B84E90AC2963AA2&Recipient='.$order->customer_number.'&CampId='.$camp_id.'&UniqueId='.urlencode($order->marketplace_reference_id));
                    \Log::info(json_encode($response));
                    \Log::info("ITS Robocall Logs completed");

                    $message = 'Call Initiated (ITS)';
                } elseif ($setting->value == config('enum.robo_call')['HTHREE']) {
                    \Log::info("HTHREE Robocall Logs started");
                    \Log::info('https://secure.h3techs.com/sms/api/send_voice?email=<EMAIL>&key=04e3167f928405a5d576286926ca151148&to='.$order->customer_number.'&type=dtmf&order_id='.urlencode($order->id).'&order_number='.urlencode($order->marketplace_reference_id).'&amount='.round($order->grand_total).'&seller_id='.$order->seller_id.'&voice_id=271&text=0');
                    $response = $client->get('https://secure.h3techs.com/sms/api/send_voice?email=<EMAIL>&key=04e3167f928405a5d576286926ca151148&to='.$order->customer_number.'&type=dtmf&order_id='.urlencode($order->id).'&order_number='.urlencode($order->marketplace_reference_id).'&amount='.round($order->grand_total).'&seller_id='.$order->seller_id.'&voice_id=271&text=0');
                    \Log::info(json_encode($response));
                    \Log::info("HTHREE Robocall Logs completed");

                    $message = 'Call Initiated (H3)';
                } elseif ($setting->value == config('enum.robo_call')['HTHREE_MERCHANT']) {
                    $api_key = Setting::hthreeAPIKey($order->seller_id);
                    $voice_id = Setting::hthreeVoiceId($order->seller_id);

                    \Log::info("HTHREE Merchant Robocall Logs started");
                    \Log::info('https://vox.unityretail.com/api/calls?api_key='.$api_key.'&caller_id='.$order->customer_number.'&key2='.urlencode($order->id).'&text2='.urlencode($order->marketplace_reference_id).'&amount='.round($order->grand_total).'&key1='.$order->seller_id.'&voice_id='.$voice_id);
                    $response = $client->get('https://vox.unityretail.com/api/calls?api_key='.$api_key.'&caller_id='.$order->customer_number.'&key2='.urlencode($order->id).'&text2='.urlencode($order->marketplace_reference_id).'&amount='.round($order->grand_total).'&key1='.$order->seller_id.'&voice_id='.$voice_id);
                    \Log::info(json_encode($response));
                    \Log::info("HTHREE Merchant Robocall Logs completed");

                    $message = 'Call Initiated (H3-Merchant)';
                } else {
                    return '<b>'.$setting->value.'</b> This RoboCall Provider does not exists in our providers list. Please contact Unity Team by clicking CHAT Button in the bottom right corner';
                }

                $orderComments = new OrderComments();
                $orderComments->order_id = $order->id;
                $orderComments->key = 'Robocall Process';
                $orderComments->value = $message;
                $orderComments->status = 'Success';
                $orderComments->run_by = ( session()->has('user') ? session('user')->id : ( auth()->check() ? NULL : 1 ) );
                $orderComments->save();
            } else {
                return 'No RoboCall Provider exists against your account. Please contact Unity Team by clicking CHAT Button in the bottom right corner';
            }
        }
    }

    return 'Customer will be called shortly';
});

Route::post('robocallu', function (Request $request) {
    foreach ($request->ids as $id) {
    }
    //return $response;
    return 'Customer will be called shortly';
});

Route::post('alexa', function (Request $request) {
    $req = $request->getContent();
    $req = json_decode($req);

    //var_dump($req->request->;
    //die;
    if ($req->request->type == 'LaunchRequest') {
        $response = [
            'outputSpeech' => [
                'type' => 'PlainText',
                'text' => 'This is Unity Retail',
            ],
        ];
    }

    if ($req->request->type == 'IntentRequest') {
        if ($req->request->intent->name == 'GetTotalOrders') {
            $response = [
                'outputSpeech' => [
                    'type' => 'PlainText',
                    'text' => Order::count().' orders yet',
                ],
                ];
        }
    }
    $data = [
                'version' => '0.1',
                'sessionAttributes' => [
                    'countActionList' => [
                        'Read' => true,
                        'category' => true,
                    ],
                ],
                'response' => $response,
                'shouldEndSession' => false,
        ];
    echo json_encode($data);
    die();
});

Route::post('order/couriers/list', function (Request $request) {
    if (! isset($request->marketplace_reference_id)) {
        return ['error' => 1, 'message' => 'Marketplace Reference ID parameter not found'];
    }

    $seller = Setting::where('key', config('enum.api_keys')['FLASH'])->where('value', $request->header('authorization'))->first();
    // return $seller;

    return Order::eligible_courier_list($request->marketplace_reference_id, $seller->seller_id);
})->middleware('flash');

Route::post('order/book', function (Request $request) {
    if (! isset($request->marketplace_reference_id)) {
        return ['error' => 1, 'message' => 'Marketplace Reference ID parameter not found'];
    } elseif (! isset($request->seller_id)) {
        return ['error' => 1, 'message' => 'Seller ID parameter not found'];
    } elseif (! isset($request->courier_id)) {
        return ['error' => 1, 'message' => 'Seller ID parameter not found'];
    } elseif (in_array($request->courier_id, [1, 9])) {
        if (! isset($request->tracking_number)) {
            return ['error' => 1, 'message' => 'Tracking Number Not Found'];
        }
    }

    $seller = Setting::where('key', config('enum.api_keys')['FLASH'])->where('value', $request->header('authorization'))->first();

    $temp = Order::eligible_for_dispatch($request->marketplace_reference_id, $seller->seller_id);

    $extras = [];

    if ($temp['error'] == 1) {
        return $temp;
    } else {
        $tracking = false;
        $service = $request->service;
        $warehouse = 'Default';
        $courier_id = $request->courier_id;
        $order = Order::findOrFail($temp['order_id']);

        //////////  Checking Order Eligibility ///////////
        $order_check = $order->order_check($courier_id, $service, false);

        if ($order_check['error'] != 0) {
            return ['error' => 1, 'message' => $order_check['message']];
        } else {
            $destination_city = $order_check['destination_city'];
        }
        //////////  Checking Order Eligibility ///////////

        /////////  Getting Order Details ////////
        $order_detail = $order->order_detail($order->items->pluck('id'), true, false, $warehouse, $courier_id);

        if ($order_detail['error'] != 0) {
            return ['error' => 1, 'message' => $order_detail['message']];
        } else {
            $data = [
                'shipper_order_id' => $order->marketplace_reference_id,
                'quantity' => $order_detail['quantity'],
                'weight' => $order_detail['weight'],
                'cod' => $order_detail['cod'],
                'cod_amount' => $order_detail['cod_amount'],
                'default_pickup_address' => $order_detail['default_pickup_address'],
                'description' => $order_detail['description'],
                'customer_email' => ($order->customer_email ? $order->customer_email : Seller::find($order->seller_id)->email),
                'destination_city' => $destination_city,
                'seller_city' => $order_detail['seller_city'],
                'seller_city_code' => $order_detail['seller_city_code'],
                'order' => $order,
                'courier_id' => $courier_id,
                'service' => $service,
                'warehouse' => $order_detail['warehouse'],
                'remarks' => $order_detail['remarks'],
            ];
        }
        /////////  Getting Order Details ////////

        ///////////// ManualDispatch OR From Courier 1 (Self) //////////////
        if ($courier_id == 1) {
            $tracking_number = $request->tracking_number;

            $tempResult = Shipment::where('tracking_number', $tracking_number)->where('seller_id', $order->seller_id)->get();
            if (count($tempResult) > 0) {
                return ['error' => 1, 'message' => 'Tracking Number Already Exists'];
            }

            $tracking = true;
        }

        ///////////// TCS Dispatch //////////////
        elseif ($courier_id == 2) {
            $temp = SellerCourierTCS::shipped($data);
            if ($temp['error'] == 1) {
                return ['error' => 1, 'message' => $temp['message']];
            } else {
                $tracking_number = $temp['message'];
                $tracking = true;
            }
        }

        ///////////// MnP Dispatch //////////////
        elseif ($courier_id == 4) {
            $temp = SellerCourierMNP::shipped($data);
            if ($temp['error'] == 1) {
                return ['error' => 1, 'message' => $temp['message']];
            } else {
                $tracking_number = $temp['message'];
                $tracking = true;
            }
        }

        ///////////// LCS Dispatch //////////////
        elseif ($courier_id == 5) {
            $temp = SellerCourierLCS::shipped($data);
            if ($temp['error'] == 1) {
                return ['error' => 1, 'message' => $temp['message']];
            } else {
                $tracking_number = $temp['message'];
                $tracking = true;
            }
        }

        ///////////// Call Courier Dispatch //////////////
        elseif ($courier_id == 7) {
            $temp = SellerCourierCallCourier::shipped($data);
            if ($temp['error'] == 1) {
                return ['error' => 1, 'message' => $temp['message']];
            } else {
                $tracking_number = $temp['message'];
                $tracking = true;
            }
        }

        ///////////// Trax Dispatch //////////////
        elseif ($courier_id == 8) {
            $temp = SellerCourierTraxNew::shipped($data);
            if ($temp['error'] == 1) {
                return ['error' => 1, 'message' => $temp['message']];
            } else {
                $tracking_number = $temp['message'];
                $tracking = true;
            }
        }

        ///////////// Pakistan Post Dispatch //////////////
        elseif ($courier_id == 9) {
            $tracking_number = $request->tracking_number;

            $tempResult = Shipment::where('tracking_number', $tracking_number)->where('courier_id', '9')->get();
            if (count($tempResult) > 0) {
                return ['error' => 1, 'message' => 'Tracking Number Already Assigned'];
            }

            $tracking = true;
        }

        ///////////// BlueEx Dispatch //////////////
        elseif ($courier_id == 10) {
            $temp = SellerCourierBlueEx::shipped($data);
            if ($temp['error'] == 1) {
                return ['error' => 1, 'message' => $temp['message']];
            } else {
                $tracking_number = $temp['message'];
                $tracking = true;
            }
        }

        ///////////// Rider Dispatch //////////////
        elseif ($courier_id == 11) {
            $temp = SellerCourierRider::shipped($data, $request->areas);
            if ($temp['error'] == 1) {
                return ['error' => 1, 'message' => $temp['message']];
            } else {
                $tracking_number = $temp['message'];
                $tracking = true;
            }
        }

        ///////////// TM Delivery Express Dispatch //////////////
        elseif ($courier_id == 12) {
            $temp = SellerCourierDeliveryExpress::shipped($data);
            if ($temp['error'] == 1) {
                return ['error' => 1, 'message' => $temp['message']];
            } else {
                $tracking_number = $temp['message'];
                $tracking = true;
            }
        }

        ///////////// TM Delivery Express Dispatch //////////////
        elseif ($courier_id == 13) {
            $temp = SellerCourierTCSNew::shipped($data);
            if ($temp['error'] == 1) {
                return ['error' => 1, 'message' => $temp['message']];
            } else {
                $tracking_number = $temp['message'];
                $tracking = true;
            }
        }

        ///////////// LCS-UAE Dispatch //////////////
        elseif ($courier_id == 14) {
            $temp = SellerCourierLCSUAE::shipped($data);
            if ($temp['error'] == 1) {
                return ['error' => 1, 'message' => $temp['message']];
            } else {
                $tracking_number = $temp['message'];
                $tracking = true;
            }
        }

        ///////////// Swyft Dispatch //////////////
        elseif ($courier_id == 15) {
            $temp = SellerCourierSwyft::shipped($data);
            if ($temp['error'] == 1) {
                return ['error' => 1, 'message' => $temp['message']];
            } else {
                $tracking_number = $temp['message'];
                $tracking = true;
            }
        }

        ///////////// DHL Dispatch //////////////
        elseif ($courier_id == 17) {
            $temp = SellerCourierDHL::shipped($data);
            if ($temp['error'] == 1) {
                return ['error' => 1, 'message' => $temp['message']];
            } else {
                $tracking_number = $temp['message'];
                $extras['label_image'] = $temp['label_image'];
                $extras['commercial_invoice'] = $temp['commercial_invoice'];
                $tracking = true;
            }
        }

        ///////////// MoveX Dispatch //////////////
        elseif ($courier_id == 19) {
            $temp = SellerCourierMoveX::shipped($data);
            if ($temp['error'] == 1) {
                return ['error' => 1, 'message' => $temp['message']];
            } else {
                $tracking_number = $temp['message'];
                $tracking = true;
            }
        }

        ///////////// Bykea Dispatch //////////////
        elseif ($courier_id == 20) {
            $temp = SellerCourierBykea::shipped($data);
            if ($temp['error'] == 1) {
                return ['error' => 1, 'message' => $temp['message']];
            } else {
                $tracking_number = $temp['message'];
                $extras['batch_booking_id'] = $temp['batch_booking_id'];
                $tracking = true;
            }
        }

        if ($tracking) {
            if ($request->shipping_fee) {
                $order->shipping_fee_charged = $tracking_number;
                $order->save();
            }

            $data['tracking_number'] = $tracking_number;
            $data['user_id'] = 1;

            //////////// Creating Shipment and Updating Order and its items ///////////
            $shipment = Shipment::dispatch($data, $order->items->pluck('id'), false, $extras);
            $message = 'Shipment # '.$tracking_number.' Created!';

            return ['error' => 0, 'message' => $message, 'tracking_number' => $tracking_number];
        } else {
            return ['error' => 1, 'message' => 'Shipment Not Booked'];
        }
    }
})->middleware('flash');

Route::post('order/book/manual', function (Request $request) {
    if (! isset($request->marketplace_reference_id)) {
        return ['error' => 1, 'message' => 'Marketplace Reference ID parameter not found'];
    } elseif (! isset($request->seller_id)) {
        return ['error' => 1, 'message' => 'Seller ID parameter not found'];
    } elseif (! isset($request->courier_id)) {
        return ['error' => 1, 'message' => 'Courier ID parameter not found'];
    } elseif (! isset($request->tracking_number)) {
        return ['error' => 1, 'message' => 'Tracking Number parameter not found'];
    }

    $order = Order::eligible_for_dispatch($request->marketplace_reference_id, $request->seller_id);

    if ($order['error'] == 0) {
        event(new OrderShippingEvent('Default', $order['order_id'], $request->courier_id, (isset($request->service_id) ? $request->service_id : null), false, 0, false, $request->tracking_number));

        return ['error' => 0, 'message' => 'Manual booking of this order is in queue'];
    } else {
        return ['error' => 1, 'message' => $order['message']];
    }
});

Route::post('order/set-origin', function (Request $request) {

    try {

        if (! ($request->header('Authorization'))) {
            throw new Exception('Auth Token Required', 401);
        } elseif (! isset($request->order_id)) {
            throw new Exception('Order ID parameter not found', 400);
        } elseif (! isset($request->location_id)) {
            throw new Exception('Location ID parameter not found', 400);
        } elseif (! isset($request->seller_id)) {
            throw new Exception('Seller ID parameter not found', 400);
        }
        
        
        $auth_token = base64_encode(hash_hmac('sha256', $request->getContent(), 'enterprise_order_location_sync', true));
        if($request->header('Authorization') != $auth_token) {
            throw new Exception('Invalid Auth Token', 401);
        }


        $order = Order::where('seller_id', $request->seller_id)->where('marketplace_reference_id',$request->order_id)->first();
        
        if($order && $order->status == 'Pending') {

            if ($order->seller_location_id && $order->seller_location_id != 1) {
                throw new Exception('Order already has a location', 409);
            }


            
            $location = SellerLocation::where('seller_id', $request->seller_id)->where('seller_reference_id', $request->location_id)->first(['id','location_name']);

            if ($location) {
                if(isset($request->items)){
                    $items = $request->items;
                    $order->assignLocationToOrderAndOrderItems($location->id, 'location has been assigned through api. <br>Location Name : <b>'.$location->location_name.'</b><br>Location Reference ID : <b>'.$request->location_id.'</b> through API', 1 , $items);
                }else{
                    $order->assignLocationToOrderAndOrderItems($location->id, 'location has been assigned through api. <br>Location Name : <b>'.$location->location_name.'</b><br>Location Reference ID : <b>'.$request->location_id.'</b> through API', 1);
                }

            } else {
                throw new Exception('Location not found against the seller id '.$request->seller_id, 409);
            }
            
            return response()->json(['error' => 0, 'message' =>  'Success'], 200);





        } elseif ($order && $order->status != 'Pending') {
            throw new Exception('Order is not in Pending state', 409);
        } else {
            throw new Exception('Order not found', 409);
        }


    } catch (Exception $e) {
        Log::critical('Code : '.$e->getCode().' | Message : '.$e->getMessage());
        return response()->json(['error' => 1, 'message' =>  $e->getMessage()], $e->getCode());
    }

})->withoutMiddleware('throttle:32000,1');

Route::post('order/set-origin-jdot', function (Request $request) {

    try {

        if (! ($request->header('Authorization'))) {
            throw new Exception('Auth Token Required', 401);
        } elseif (! isset($request->order_id)) {
            throw new Exception('Order ID parameter not found', 400);
        } elseif (! isset($request->location_id)) {
            throw new Exception('Location ID parameter not found', 400);
        } elseif (! isset($request->seller_id)) {
            throw new Exception('Seller ID parameter not found', 400);
        }
        
        
        $auth_token = base64_encode(hash_hmac('sha256', $request->getContent(), 'enterprise_order_location_sync', true));
        if($request->header('Authorization') != $auth_token) {
            throw new Exception('Invalid Auth Token', 401);
        }


        $order = Order::where('seller_id', $request->seller_id)->where('marketplace_reference_id',$request->order_id)->first();
        
        if($order && $order->status == 'Pending') {

            //commenting it temporarily for Jdot
            // if ($order->seller_location_id && $order->seller_location_id != 1) {
            //     throw new Exception('Order already has a location', 409);
            // }


            
            $location = SellerLocation::where('seller_id', $request->seller_id)->where('seller_reference_id', $request->location_id)->first(['id','location_name']);

            if ($location) {
                if(isset($request->items)){
                    $items = $request->items;
                    $order->assignLocationToOrderAndOrderItems($location->id, 'location has been assigned through api. <br>Location Name : <b>'.$location->location_name.'</b><br>Location Reference ID : <b>'.$request->location_id.'</b> through API', 1 , $items);
                }else{
                    $order->assignLocationToOrderAndOrderItems($location->id, 'location has been assigned through api. <br>Location Name : <b>'.$location->location_name.'</b><br>Location Reference ID : <b>'.$request->location_id.'</b> through API', 1);
                }

            } else {
                throw new Exception('Location not found against the seller id '.$request->seller_id, 409);
            }
            
            return response()->json(['error' => 0, 'message' =>  'Success'], 200);





        } elseif ($order && $order->status != 'Pending') {
            throw new Exception('Order is not in Pending state', 409);
        } else {
            throw new Exception('Order not found', 409);
        }


    } catch (Exception $e) {
        Log::critical('Code : '.$e->getCode().' | Message : '.$e->getMessage());
        return response()->json(['error' => 1, 'message' =>  $e->getMessage()], $e->getCode());
    }

})->withoutMiddleware('throttle:32000,1');

Route::post('rma/reverse-shipment/book', function(Request $request) {

    $id = 0;
    // return array('error' => 0, 'message' => 'yo');
    if(!($request->header('X-Auth-Token')))
    {
        return array('error' => 1, 'message' => 'Auth Token Required');
    }
    elseif(!isset($request->shipment_id))
    {
        return array('error' => 1, 'message' => 'Shipment ID parameter not found');
    }

    $auth_token = base64_encode(hash_hmac('sha256', strval($request->shipment_id), 'unity-mvp', true));
    if($request->header('X-Auth-Token') != $auth_token){
        return array('error' => 1, 'message' => 'Invalid Auth Token');
    }
    else{
        $shipment = Shipment::where('id',$request->shipment_id)->first();

        $data = [[]];

        if($shipment)
        {
            try{
                $data['shipment_id'] = $request->shipment_id;
                $data['courier_id'] = $shipment->courier_id;
                $data['warehouse'] = $shipment->seller_location_id;
                
                $result = Shipment::reverse($data, $shipment->seller_id);
                // return $result;
                if ($result['error'] == 0) {
                    return array('error' => 0, 'message' => 'Reverse Shipment Booked Successfully', 'cn' => $result['cn']);
                } 
                else {
                    return array('error' => 1, 'message' => $result['message']);
                }
                
            }
            catch(\Exception $e)
            {
                return array('error' => 1, 'message' => $e->getMessage());
            }
        }
        else{
            return array('error' => 1, 'message' => 'Shipment Not Found');
        }
        
    }
    // return $seller;

    // return Order::eligible_courier_list($request->marketplace_reference_id, $seller->seller_id);
});

Route::post('shipments/bulk-cancel', function(Request $request) {

    // $id = 0;
    // return array('error' => 0, 'message' => 'yo');
    if(!($request->header('X-Auth-Token')))
    {
        return array('error' => 1, 'message' => 'Auth Token Required');
    }

    $auth_token = 'yoitsasecretkey';
    if($request->header('X-Auth-Token') != $auth_token){
        return array('error' => 1, 'message' => 'Invalid Auth Token');
    }
    else{
        
        foreach($request->ids as $shipment_id)
        {
            event(new BulkShipmentCancellationEvent($shipment_id,0));
        }

        return array('error' => 0, 'message' => 'Processing started');
        
    }
    // return $seller;

    // return Order::eligible_courier_list($request->marketplace_reference_id, $seller->seller_id);
});

Route::post('order/comment/add', function(Request $request) {

    if (!isset($request->message)) {
        return array('error' => 1, 'message' => 'Message parameter not found');
    } elseif (!isset($request->order_id)) {
        return array('error' => 1, 'message' => 'Order ID parameter not found');
    } elseif (!isset($request->status)) {
        return array('error' => 1, 'message' => 'Status parameter not found');
    } elseif (!isset($request->key)) {
        return array('error' => 1, 'message' => 'Key parameter not found');
    }

    OrderComments::add($request->order_id,
                        $request->key,
                        $request->message ,
                        $request->status,
                        '1');

    return ['error' => 0, 'message' => 'Comment Added'];
})->withoutMiddleware('throttle:32000,1');

Route::post('order/khaadi/hacked', function (Request $request) {
    return 'This service not available right now';

    if (! isset($request->orders)) {
        return ['error' => 1, 'message' => 'Orders Not found'];
    }

    // return KhaadiHackedCode::index($request->orders);
});

Route::post('shipment-status-change', function(Request $request){

    $shipment = Shipment::whereId($request->shipment_id)->first();
    if(env('JDOT_MAGENTO_SELLER_ID') != $shipment->seller_id ){
        event(new ShipmentStatusChangeEvent($request->shipment_id,$request->shipment_status, $request->status_id));
        ShipmentHistory::where('id', $request->status_id)->update(['storefront_sync' => 1]);
    }

    if ($shipment->type == null) {
        
        CustomerIntimationService::execute($shipment->seller_id, [
            'event' => 'shipment_status',
            'status' => $request->shipment_status,
            'marketplace_reference_id' => $shipment->order->marketplace_reference_id,
            'tracking_number' => $shipment->tracking_number,
            'order_id' => $shipment->order_id,
            'courier_id' => $shipment->courier_id,
            'customer_email' => $shipment->order->customer_email,
            'customer_name' => $shipment->order->customer_name,
            'customer_number' => $shipment->order->customer_number
        ]);


        if ($request->shipment_status == config('enum.shipment_status')['CANCELLED']) {
            CancelFulfillment::dispatch($request->shipment_id)->onQueue('cancel_fulfillment');

            FulfillmentOrder::closedByShipment($request->shipment_id);
            FulfillmentOrder::revertShippingFeeIfApplicableByShipment($request->shipment_id);
        
        } elseif($request->shipment_status == config('enum.shipment_status')['RETURNED'] && !Setting::returnReceivingMode($shipment->seller_id)) {

            $shipment->status = config('enum.shipment_status')['RETURNED_RECEIVED'];
            $shipment->save();
            $shipment->shipment_history(config('enum.shipment_status')['RETURNED_RECEIVED'], now());
            if (AddOn::fbr($shipment->seller_id) && Setting::fbr($shipment->seller_id)) {
                FBR::sendReturnPosting($shipment);
            }
        }
    }

    return array('error' => 0, 'message' => 'Event Generated');
});

Route::post('order/cancel', function(Request $request) {
    $cancel = new OrderCancel($request, 'WooCommerce');
    return $cancel->execute();
})->middleware('customapiauth');

Route::post('order/tracking', function(Request $request) {

    if(!($request->header('X-Auth-Token')))
    {
        return response()->json(['error' => 1, 'message' => 'Auth Token Required']);
    }
    elseif(!isset($request->tracking_no))
    {
        return response()->json(['error' => 1, 'message' => 'Tracking Number not found']);
    }
    elseif(!isset($request->seller_key))
    {
        return response()->json(['error' => 1, 'message' => 'Seller Key not found']);
    }

    $auth_token = base64_encode(hash_hmac('sha256', strval($request->tracking_no), '38ae465f-4be5-4cea-bf14-2d3bd0d1014f', true));
    if($request->header('X-Auth-Token') != $auth_token){
        return response()->json(['error' => 1, 'message' => 'Invalid Auth Token']);
    }else{
        if($request->tracking_no == "10000121" && $request->seller_key == "sellerkey"){
            return response()->json(['text' => "Your order is in delivered status"]);
        }
        if($request->tracking_no == "10000122"  && $request->seller_key == "sellerkey"){
            return response()->json(['text' => "Your order is in ready for packing status"]);
        }
        if($request->tracking_no == "10000123"  && $request->seller_key == "sellerkey"){
            return response()->json(['text' => "Your order is in received in hub status"]);
        }
        if($request->tracking_no == "10000124"  && $request->seller_key == "sellerkey"){
            return response()->json(['text' => "Your order is in ready for delivery status"]);
        }else{
            $shipmentController = new ShipmentController;
            $seller_id = Setting::where('value', $request->seller_key)->value('seller_id');
            if($seller_id){
                $response =  $shipmentController->getStatusAgainstTrackingOrOrderNumber($request->tracking_no,$seller_id);
                if($response){
                    $text = 'The current status of the shipment is '.$response->status.' which was last updated by '.$response->name.' at '.$response->status_at;
                    return response()->json(['text' => $text]);
                }else{
                    return response()->json(['text' => "Unable to find anything against the tracking or order number"]);
                }
            }else{
                return response()->json(['text' => "Unable to find anything against the tracking or order number!"]);
            }
        }
    }


});

Route::post('customer-order-confirmation', function(Request $request) {

    $id = 0;
    // return array('error' => 0, 'message' => 'yo');
    if(!($request->header('X-Auth-Token')))
    {
        return response()->json(['message' => 'Auth Token Required'], 404);
    }
    elseif(!isset($request->id))
    {
        return response()->json(['message' => 'ID parameter not found'], 404);
    }
    elseif(!isset($request->lat))
    {
        return response()->json(['message' => 'Lat parameter not found'], 404);
    }
    elseif(!isset($request->long))
    {
        return response()->json(['message' => 'Long parameter not found'], 404);
    }
    elseif(!isset($request->error_msg))
    {
        return response()->json(['message' => 'Error Msg parameter not found'], 404);
    }
    elseif(!isset($request->error_msg_status))
    {
        return response()->json(['message' => 'Error Msg Status parameter not found'], 404);
    }


    $auth_token = base64_encode(hash_hmac('sha256', strval($request->id), 'yoitsasecretkey', true));
    if($request->header('X-Auth-Token') != $auth_token){
        return response()->json(['message' => 'Invalid Auth Token'], 404);

    }
    else{
        $order = Order::where('id',$request->id)->first();

        if($order){

            if(!SellerOrderConfirmation::where('seller_id',$order->seller_id)->where('order_id',$order->id)->where('confirmed',1)->exists()){
                $tag = Tag::whereSellerId($order->seller_id)->where('value','Confirmed')->where('key','order')->select('id','color','value')->first();
                if($request->error_msg_status == false){
                    $order->customer_lat = $request->lat;
                    $order->customer_long = $request->long;
                    $order->save();
                }
                else{
                    $order->geo_location_reason = $request->error_msg;
                    $order->save();
                }
                
                OrderComments::add($order->id, 'Customer Order Confirmtaion Process','Customer Confirmed the Order through SMS', 'Success', 1 );
                
                if (isset($tag['id'])) {
                    $ordertag = new App\Models\OrderTag;
                    $ordertag->updateOrCreate(['order_id' => $order->id],['tag_id' => $tag['id']]);
                    RecordOrderConfirmation::add($order->seller_id,$order->id,'SMS',2);
                    OrderComments::add($order->id, 'Order Tag Assignment Process', '<span class="label '.$tag['color'].'">'.$tag['value'].'</span> Tag has been assigned', 'Success', 1 );
                    OrderTag::tag_check($order->id, $tag);
                    
                    return response()->json(['message' => 'Success'], 200);
                }
                else{
                    \Log::info('Customer Order Confirmation | Order Tag Assignment | Failed | Order ID: '.$order->id);
                }
            } else{
                return response()->json(['message' => 'Success'], 200);
            }
        }
        else{
            return response()->json(['message' => 'Order not found'], 404);
        }
        
    }
    // return $seller;

    // return Order::eligible_courier_list($request->marketplace_reference_id, $seller->seller_id);
});

Route::post('customer-order-confirmation/cancelled', function(Request $request) {

    $id = 0;
    // return array('error' => 0, 'message' => 'yo');
    if(!($request->header('X-Auth-Token')))
    {
        return response()->json(['message' => 'Auth Token Required'], 404);
    }
    elseif(!isset($request->id))
    {
        return response()->json(['message' => 'ID parameter not found'], 404);
    }
    


    $auth_token = base64_encode(hash_hmac('sha256', strval($request->id), 'yoitsasecretkey', true));
    if($request->header('X-Auth-Token') != $auth_token){
        return response()->json(['message' => 'Invalid Auth Token'], 404);

    }
    else{
        $order = Order::where('id',$request->id)->first();

        if($order){
            if(!SellerOrderConfirmation::where('seller_id',$order->seller_id)->where('order_id',$order->id)->where('confirmed',1)->exists()){
                $tagg = Tag::whereSellerId($order->seller_id)->where('value','Confirmed')->where('key','order')->select('id','color','value')->first();
            
                //this is to cater auto order cancelation if enabled in settings
                $add_on_auto_order_cancellation = \App\Models\Setting::where('seller_id',$order->seller_id)->where('key',config('enum.settings')['AUTOCUSTOMERCANCELLATION'])->where('value','1')->first();
                if($add_on_auto_order_cancellation){
                    $orderController = new OrderController;
                    $orderController->cancelOrderByCustomer($order);
                }

                // OrderComments::add($order->id, 'Customer Order Confirmtaion Process','Customer Confirmed the Order', 'Success', 1 );
                
                
                // $tag = Tag::whereSellerId($order->seller_id)->where('value','Confirmed')->where('key','order')->select('id','color','value')->first();
                $tag = Tag::whereSellerId($order->seller_id)->whereValue('Cancelled')->select('id')->first();
                
                
                if (isset($tag['id'])) {
                    $ordertag = new App\Models\OrderTag;
                    $ordertag->updateOrCreate(['order_id' => $order->id],['tag_id' => $tag['id']]);
                    RecordOrderConfirmation::add($order->seller_id,$order->id,'SMS',2);
                    OrderComments::add($order->id, 'Order Tag Assignment Process', '<span class="label '.$tag['color'].'">'.$tag['value'].'</span> Tag has been assigned', 'Success', 1 );
                    OrderTag::tag_check($order->id, $tag);
                    
                    return response()->json(['message' => 'Success'], 200);
                }
                else{
                    \Log::info('Customer Order Confirmation | Order Tag Assignment | Failed | Order ID: '.$order->id);
                }
            }else{
                return response()->json(['message' => 'Success'], 200);
            }
        }
        else{
            return response()->json(['message' => 'Order not found'], 404);
        }
        
    }
    // return $seller;

    // return Order::eligible_courier_list($request->marketplace_reference_id, $seller->seller_id);
});



Route::get('marco/awb/{type}/{id}', function(Request $request) {

    try {
        $order_id = Order::whereSellerId(session('seller_id'))->whereMarketplaceReferenceId($request->route('id'))->value('id');
        if (!$order_id) {
            $shipment = Shipment::whereSellerId(session('seller_id'))->whereTrackingNumber($request->route('id'))->first(['id','courier_id']);
            if (!$shipment) {
                return response()->json(['error' => 1 , 'message' => 'None order and shipment exists againt this no #'.$request->route('id')], 406);
            }
        } else {
            $shipment = Shipment::whereOrderId($order_id)->latest()->first(['id','courier_id']);
            if (!$shipment) {
                return response()->json(['error' => 1 , 'message' => 'None shipment exists againt this order no #'.$request->route('id')], 406);
            }
        }

        if ($shipment->courier_id != 8) {
            return response()->json(['error' => 1 , 'message' => 'This shipment is not of TRAX courier #'.$request->route('id')], 406);
        }

        Auth::loginUsingId(session('seller_id'));
        $myRequest = new Request([
            'ids'   => [$shipment->id],
        ]);
        
        $shipmentController = new ShipmentController;

        if ($request->route('type') == 1) {
            $data = response()->make(base64_decode($shipmentController->print_address_labels($myRequest)[0][1]), 200, [
                'Content-Type' => 'application/pdf',
                'Content-Disposition' => 'inline; filename="asd.pdf"'
            ]);
        } elseif ($request->route('type') == 2) {
            $data = trim(preg_replace('/\s\s+/', ' ', $shipmentController->print_sticker_labels($myRequest)[0]));
        } else {
            $data = response()->json(['error' => 1 , 'message' => 'Invalid type #'.$request->route('type')], 406);
        }

        Auth::logout();
        session()->flush();

    } catch (Exception $e) {
        $data = response()->json(['error' => 1 , 'message' => $e->getMessage()], 406);
        \Log::info($e->getTraceAsString());
    }

    return $data; 

})->middleware('marcoapiauth');




Route::get('marco/invoice/{id}', function(Request $request) {
    
    try {
        $order_id = Order::whereSellerId(session('seller_id'))->whereMarketplaceReferenceId($request->route('id'))->value('id');
        if (!$order_id) {
            return response()->json(['error' => 1 , 'message' => 'No order found againt this no #'.$request->route('id')], 406);
        } else {
            $shipment = Shipment::whereOrderId($order_id)->latest()->first(['id','courier_id']);
            if (!$shipment) {
                return response()->json(['error' => 1 , 'message' => 'None shipment exists againt this order no #'.$request->route('id')], 406);
            }
        }

        if ($shipment->courier_id != 8) {
            return response()->json(['error' => 1 , 'message' => 'This shipment is not of TRAX courier #'.$request->route('id')], 406);
        }

        Auth::loginUsingId(session('seller_id'));
        $myRequest = new Request([
            'ids'   => [$shipment->id],
        ]);
        
        $shipmentController = new ShipmentController;

            $data = trim(preg_replace('/\s\s+/', ' ', $shipmentController->print_invoices($myRequest)[0]));

        Auth::logout();
        session()->flush();

    } catch (Exception $e) {
        $data = response()->json(['error' => 1 , 'message' => $e->getMessage()], 406);
        \Log::info($e->getTraceAsString());
    }

    return $data;

})->middleware('marcoapiauth');


Route::get('marco/location/{id}', function(Request $request) {
    
    try {
        $order = Order::whereSellerId(session('seller_id'))->whereMarketplaceReferenceId($request->route('id'))->first(['id','seller_location_id']);
        if (!$order) {
            return response()->json(['error' => 1 , 'status' => 'unassigned', 'message' => 'No order found againt this no #'.$request->route('id'), 'location' => [] ], 406);
        } else {
            if ($order->seller_location_id == 1) {
                return response()->json(['error' => 1 , 'status' => 'unassigned', 'message' => '#'.$request->route('id').' order doesnt have any location associated with it yet.', 'location' => [] ], 406);
            }
        }

        $location = SellerLocation::whereId($order->seller_location_id)->first(['location_name','seller_reference_id']);

        if (!$location || !$location->seller_reference_id) {
            return response()->json(['error' => 1 , 'status' => 'unassigned', 'message' => '#'.$request->route('id').' order location doesnt have reference id.', 'location' => [] ], 406);
        } else {
            $data = response()->json(['error' => 0 , 'status' => 'assigned', 'message' => 'success', 'location' => [ 'name' => $location->location_name, 'unity_id' => $order->seller_location_id, 'reference_id' => $location->seller_reference_id ] ], 200);
        }

    } catch (Exception $e) {
        $data = response()->json(['error' => 1 , 'status' => 'unassigned', 'message' => $e->getMessage(), 'location' => [] ], 406);
        \Log::info($e->getTraceAsString());
    }

    return $data;

})->middleware('marcoapiauth');

Route::get('shipment/generate-stickerlabel', function(Request $request) {
    $shipment = Shipment::whereSellerId(session('seller_id'))->whereTrackingNumber($request->tracking_no)->first(['id']);
    if($shipment){
        $myRequest = new Request([
            'ids'   => [$shipment->id],
        ]);
        Auth::loginUsingId(session('seller_id'));
    
        $shipmentController = new ShipmentController;
        try{
            $data = trim(preg_replace('/\s\s+/', ' ', $shipmentController->print_sticker_labels($myRequest)[0]));
            return $data;
        }catch(Exception $e){
            $data =  $e->getMessage();
            return response()->json(['error' => 1, 'message' => 'Somethig went wrong'], 404);
        }
    }else{
        return response()->json(['error' => 1, 'message' => 'Shipment doesnt exist against the tracking number'],404);
    }
})->middleware('universalapiauth');

Route::post('inventory/update', function(Request $request) {

    $id = 0;
    // return array('error' => 0, 'message' => 'yo');
    if(!($request->header('X-Auth-Key')))
    {
        return response()->json(['message' => 'Auth Key not found'], 404);
    }
    if(!isset($request->location_id))
    {
        return response()->json(['message' => 'Location ID parameter not found'], 404);
    }
    elseif(!isset($request->product_id))
    {
        return response()->json(['message' => 'Product ID parameter not found'], 404);
    }
    elseif(!isset($request->stock_count))
    {
        return response()->json(['message' => 'Stock Count parameter not found'], 404);
    }
    elseif(is_numeric($request->stock_count) != 1)
    {
        return response()->json(['message' => 'Stock Count parameter is not a valid numeric value'], 404);
    }
// return $request->header('X-Auth-Key');

    $seller = Setting::where('key',config('enum.api_keys')['INVENTORYUPDATEAPIKEY'])->where('value', $request->header('X-Auth-Key'))->first();
    // $auth_token = base64_encode(hash_hmac('sha256', strval($request->id), 'yoitsasecretkey', true));
    if(!$seller){
        return response()->json(['message' => 'Invalid Auth Key'], 404);
    }
    else{
        try{
            $seller_id = $seller->seller_id;
            $location = SellerLocation::where('seller_id',$seller_id)->where('seller_reference_id',$request->location_id)->first();
            if($location){
                $product = Product::where('seller_id',$seller_id)->where('SKU',$request->product_id)->first();
                if($product){
                    $inventory = Inventory::where('seller_location_id',$location->id)->where('product_id',$product->id)->first();
                    if($inventory){
                        $inventory->stock = $request->stock_count;
                        $inventory->save();

                        return response()->json(['message' => 'Inventory updated successfully'], 200);
                    } else{

                    }
                } else{
                    \Log::info("Unity | Inventory Update | Error | Location ID: ".$request->location_id." | Product ID: ".$request->product_id." | Seller ID: 1 | Error Message: Product not exist");

                    return response()->json(['message' => 'Product not exist'], 404);
                }
            } else{
                \Log::info("Unity | Inventory Update | Error | Location ID: ".$request->location_id." | Product ID: ".$request->product_id." | Seller ID: 1 | Error Message: Location not exist");

                return response()->json(['message' => 'Location not exist'], 404);
            }
        } catch(Exception $e){
            $message = "Unity | Inventory Update | Error | Location ID: ".$request->location_id." | Product ID: ".$request->product_id." | Seller ID: 1 | Error Message: ".$e->getMessage();
            \Log::info($message);

            $activity_id = activity()
            ->causedBy($seller_id)
            ->withProperties(['request' => $message , 'dump' => $e->getMessage()])
            ->log('Inventory Update');

            \Mail::raw('Unity | Inventory Update | Error | Something went wrong', function ($m) {
                $m->to('<EMAIL>')
                ->subject('Inventory Update');
            });

            return response()->json(['message' => 'Something went wrong'], 500);
        }
    }


 
});


Route::post('loadsheet/update', function(Request $request) {


    try {

        if(!($request->header('X-Auth-Key'))) {
            return response()->json(['error' => 1, 'message' => 'X-Auth-Key not found'], 404);
        } elseif(!isset($request->tracking_numbers)) {
            return response()->json(['error' => 1, 'message' => 'Tracking Numbers not found'], 404);
        }

        $setting = Setting::where('key',config('enum.api_keys')['INVENTORYUPDATEAPIKEY'])->where('value', $request->header('X-Auth-Key'))->first();

        if(!$setting) {
            return response()->json(['error' => 1, 'message' => 'Invalid X-Auth-Key'], 404);
        } else {

            $seller_id = $setting->seller_id;
            $loadsheet_enable = Setting::where('seller_id', $seller_id)->where('key','fulfilment')->where('value', 'loadsheet')->first();

            if (!$loadsheet_enable) {
                return response()->json(['error' => 1, 'message' => 'Loadsheet setting is disabled for your account, please enable them first'], 404);
            }

            $tracking_numbers = explode(',', str_replace( ['(',')'], '', $request->tracking_numbers ) );
            $shipment_ids = Shipment::whereSellerId($seller_id)->whereIn('tracking_number', $tracking_numbers)->select('id')->get()->pluck('id');

            if ($shipment_ids->isEmpty()) {
                return response()->json(['error' => 1, 'message' => 'Tracking numbers not found'], 404);
            }

            $loadsheet = new LoadsheetService;
            $result = $loadsheet->create($shipment_ids, null, null, $seller_id, true);

            Log::info($result);

            if ($result['error']) {
                return response()->json($result, 404);
            } else {
                return response()->json(['error' => 0, 'message' => 'success'], 200);
            }
        }


    } catch(Exception $e){
        
        activity()
        ->withProperties(['request' => $request->all() , 'dump' => $e->getMessage()])
        ->log('Loadsheet Update Api');
        
        Log::critical($e->getMessage(), $e->getTraceAsString());
        return response()->json(['message' => 'Something went wrong'], 500);
    }
 
});

Route::get('shipment/get-tracking-info', function(Request $request) {
    $auth_token = base64_encode(hash_hmac('sha256', strval($request->tracking_no), $request->header('Authorization'), true));
    if($request->header('X-Auth-Token') != $auth_token){
        return response()->json(['error' => 1, 'message' => 'Invalid Auth Token']);
    }else{
        $shipmentController = new ShipmentController;
        $seller_id=session('seller_id');
        if($seller_id){
            $response =  $shipmentController->getTrackingDetails($request->tracking_no,$seller_id);
            if($response['error'] == 0){
                return response()->json(['error'=>0,'message'=>'Success','data'=>$response['data']]);
            }else{
                return response()->json(['error'=>1,'message'=>'Tracking information not found against reference number provided']);
            }
        }else{
            return response()->json(['error'=>1,'message'=>'The requester doesnt have permission to view tracking details against this referece number']);
        }
    }

})->middleware('universalapiauth');



Route::post('shipment/update', function(Request $request) {


    try {

        if(!($request->header('X-Auth-Key'))) {
            return response()->json(['error' => 1, 'message' => 'X-Auth-Key not found'], 404);
        } elseif(!isset($request->type)) {
            return response()->json(['error' => 1, 'message' => 'Type not found'], 404);
        } elseif(!isset($request->tracking_numbers)) {
            return response()->json(['error' => 1, 'message' => 'Tracking Numbers not found'], 404);
        }

        $setting = Setting::where('key',config('enum.api_keys')['INVENTORYUPDATEAPIKEY'])->where('value', $request->header('X-Auth-Key'))->first();

        if(!$setting) {
            return response()->json(['error' => 1, 'message' => 'Invalid X-Auth-Key'], 404);
        } else {

            $seller_id = $setting->seller_id;

            if ($request->type == 2) {
                
                if (Setting::readyForDispatch($seller_id)) {

                    $tracking_numbers = explode(',', str_replace( ['(',')'], '', $request->tracking_numbers ) );
                    $shipments = Shipment::whereSellerId($seller_id)->whereIn('tracking_number', $tracking_numbers)->get(['id','status','tracking_number','order_id','type']);
    
                    if ($shipments->isEmpty()) {
                        return response()->json(['error' => 1, 'message' => 'Tracking numbers not found'], 404);
                    }

                    $count = 0;

                    foreach ($shipments as $shipment) {

                        if ($shipment->status == config('enum.shipment_status')['BOOKED']) {
                            $shipment->status = config('enum.shipment_status')['READY_FOR_DISPATCH'];
                            $shipment->save();
                            $shipment->shipment_history(config('enum.shipment_status')['READY_FOR_DISPATCH'], Carbon::now()->toDateTimeString());
                            $shipment->shipment_courier_history(config('enum.shipment_status')['READY_FOR_DISPATCH'], Carbon::now()->toDateTimeString(), 'Status updated through API service');
                            $count++;
                        }
                    }

                    return response()->json(['error' => 0, 'message' => 'success | #'.$count.' shipments status updated'], 200);

				} else {
                    return response()->json(['error' => 1, 'message' => 'Ready for dispatch setting is disabled for your account, please enable it first'], 404);
                }




            } elseif ($request->type == 3) {
                $loadsheet_enable = Setting::where('seller_id', $seller_id)->where('key','fulfilment')->where('value', 'loadsheet')->first();

                if (!$loadsheet_enable) {
                    return response()->json(['error' => 1, 'message' => 'Loadsheet setting is disabled for your account, please enable it first'], 404);
                }

                $tracking_numbers = explode(',', str_replace( ['(',')'], '', $request->tracking_numbers ) );
                $shipment_ids = Shipment::whereSellerId($seller_id)->whereIn('tracking_number', $tracking_numbers)->select('id')->get()->pluck('id');

                if ($shipment_ids->isEmpty()) {
                    return response()->json(['error' => 1, 'message' => 'Tracking numbers not found'], 404);
                }

                $loadsheet = new LoadsheetService;
                $result = $loadsheet->create($shipment_ids, null, null, $seller_id, true);

                Log::info($result);

                if ($result['error']) {
                    return response()->json($result, 404);
                } else {
                    return response()->json(['error' => 0, 'message' => 'success'], 200);
                }

            } else {
                return response()->json(['error' => 1, 'message' => 'Invalid Type'], 404);
            }
            
            
        }


    } catch(Exception $e){
        
        activity()
        ->withProperties(['request' => $request->all() , 'dump' => $e->getMessage()])
        ->log('Loadsheet Update Api');
        
        Log::critical($e->getMessage(), $e->getTraceAsString());
        return response()->json(['message' => 'Something went wrong'], 500);
    }
 
});

Route::post('order/assign-tag', function (Request $request) {

    try {

        if (! ($request->header('Authorization'))) {
            throw new Exception('Auth Token Required', 401);
        } elseif (! isset($request->order_id)) {
            throw new Exception('Order ID parameter not found', 400);
        }         
        
        $auth_token = base64_encode(hash_hmac('sha256',$request->order_id, 'order_tag_assignment', true));
        if($request->header('Authorization') != $auth_token) {
            throw new Exception('Invalid Auth Token', 401);
        }

        $order = Order::where('id', $request->order_id)->first();
        if($order){
            if(Setting::where('seller_id', $order->seller_id)->where('key',config('enum.settings')['ONDEMANDFAILED'])->where('value',1)->exists()){
                $tag = Tag::whereSellerId($order->seller_id)->whereValue('Ondemand Failed')->first();
                if (isset($tag['id'])) {
                    $ordertag = new OrderTag();
                    $ordertag->updateOrCreate(['order_id' => $order->id], ['tag_id' => $tag['id']]);
                    OrderComments::add($order->id, 'Order Tag Assignment Process', '<span class="label '.$tag['color'].'">'.$tag['value'].'</span> Tag has been assigned from the API', 'Success', 1);
                    OrderTag::tag_check($order->id, $tag);
                }
                return response()->json(['error' => 0, 'message' =>  'Success'], 200);
    
            }else{
                throw new Exception('Unable to find Tag', 409);
            }
        }
        else {
            throw new Exception('Order not found', 409);
        }


    } catch (Exception $e) {
        return response()->json(['error' => 1, 'message' =>  $e->getMessage()], $e->getCode());
    }

})->withoutMiddleware('throttle:32000,1');


Route::group(['prefix' => 'foree'], function() {
	Route::get('payment-details', 'PaymentController@paymentDetails');
});

Route::group(['prefix' => 'paymob'], function() {
	Route::post('payment-details', 'PaymentController@paymentDetailsPaymob');
});

Route::group(['prefix' => 'swich'], function() {
	Route::get('payment-details', 'PaymentController@paymentDetailsSwich');
});

Route::middleware(['wordpress_api'])->group(function () {
    Route::get('get-cities','CityController@getCitiesApi')->middleware('throttle:60,1');
});

Route::post('get-quote','OrderController@getUniversalQuote')->withoutMiddleware('throttle:60,1');


Route::group([
    'middleware' => 'GHQAuth',
    'prefix' => 'GHQ',
],function($router){
    Route::post('get-universal-courier-info',[GHQController::class,'getUniversalCourierInfo']);
    Route::post('check-courier-charges',[GHQController::class,'checkCourierCharges']);
    Route::post('wallet-adjustment',[GHQController::class,'walletAdjustment']);
    $router->post('mark-as-paid',[GHQController::class,'markPaid']);
    $router->post('get-unverified-sellers',[GHQController::class,'getUnverifiedSellers']);
    $router->post('verify-seller-billing-profile',[GHQController::class,'verifySellerBillingProfile']);
    $router->post('get-sellers-all',[GHQController::class,'getSellersAll']);
    $router->post('get-couriers-all',[GHQController::class,'getCouriersAll']);
    $router->post('manual-wallet-topup',[GHQController::class,'manualWalletTopup']);
    $router->post('get-packages-all',[GHQController::class,'getPackagesAll']);
    $router->post('get-previous-seller-packages',[GHQController::class,'getPreviousSellerPackages']);
    $router->post('seller-package-save',[GHQController::class,'sellerPackageSave']);
    $router->post('refund-cn',[GHQController::class,'refundCN']);
    $router->post('refund-wallet',[GHQController::class,'refundWallet']);
    $router->post('customer-intimations', [GHQController::class, 'saveIntimationShow']);
    $router->post('get-customer-intimations', [GHQController::class, 'getIntimationShow']);
    $router->post('get-courier-universal-accounts-applied', [GHQController::class, 'getUniversalCourierAccountsApplied']);
    $router->post('set-courier-universal-accounts-applied', [GHQController::class, 'setUniversalCourierAccountsApplied']);
    

});


Route::group([
    'middleware' => 'auth:sanctum',
    'prefix' => 'ffc',
],function($router){
    Route::get('get-info/{id}',[FFCController::class,'getInfo']);
    Route::post('get-stock-order-request',[FFCController::class,'getStockOrderRequest']);
    Route::post('update-stock-order-status',[FFCController::class,'updateStockOrderRequest'])->middleware('prevent.duplicate');
    Route::post('get-stock-order-request-items',[FFCController::class,'getStockOrderRequestItems']);
    Route::post('update-putaway',[FFCController::class,'updatePutaway']);
    Route::post('get-ffc-orders-asigned-to-user',[FFCController::class,'getFFCOrdersAssignedToUser']);
    // Route::post('get-ffc-orders-asigned-by-dom',[FFCController::class,'getFFCOrdersAssignedByDom']);
    Route::post('get-ffc-items-inventory',[FFCController::class,'getFFCItemsInventory']);
    Route::post('item-picked',[FFCController::class,'itemPicked']);
    // Route::post('update-order-temp-locations',[FFCController::class,'updateOrderTempLocations']);
    Route::post('get-inventory-against-product-barcode',[FFCController::class,'getInventoryAgainstProductBarcode']);
    Route::post('get-ffc-inventory-against-product-barcode',[FFCController::class,'getFFCInventoryAgainstProductBarcode']);
    Route::post('check-if-product-has-stock-movement',[FFCController::class,'checkIfProductHasStockMovement']);
    Route::get('putaway-suggest-location/{barode}',[FFCController::class,'putawaySuggestLocation']);
    Route::get('assign-picklist',[FFCController::class,'assignPicklist']);
    Route::get('assign-picklist/release',[FFCController::class,'releaseAssignedPickList']);
    Route::post('assign-picklist/submit-to-pd',[FFCController::class,'submitToPD']);
    Route::get('get-pending-putaway-items',[FFCController::class,'getPendingPutAwayItems']);
    Route::post('putaway_item',[FFCController::class,'getPutwayPendingItem']);
    Route::post('get-location-details-from-barcode',[FFCController::class,'getLocationDetailsFromBarcode']);
    Route::post('update-putaway-new',[FFCController::class,'updatePutawayNew']);
    Route::post('get-product-details-from-barcode',[FFCController::class,'getProductDetailsFromBarcode']);
    Route::post('create-stock-movement',[FFCController::class,'createStockMovement']);
    Route::get('check-for-stock-movement',[FFCController::class,'checkForStockMovement']);
    Route::post('get-location-details-from-barcode-if-capacity',[FFCController::class,'getLocationDetailsFromBarcodeIfCapacity']);
    Route::post('move-stock',[FFCController::class,'moveStock']);
    Route::post('get-location-if-product-inventory-exist',[FFCController::class,'getLocationIfProductInventoryExist']);
    Route::post('save-location-first-putaway',[FFCController::class,'saveLocationFirstPutaway']);
    Route::post('get-pending-putaway-item-from-barcode',[FFCController::class,'getPendingPutAwayItemFromBarcode']);
    Route::post('remove-committed-fo-unpicked-item',[FFCController::class,'removeCommittedFOunpickedItem']);

    
    
    Route::group(['prefix' => 'transfer-order'],function($router){
        
        Route::post('get-unassigned', [FFCController::class,'getUnassignedTransferOrder']);
        Route::post('get-details-for-picking', [FFCController::class,'getDetailForPickingTransferOrder'])->middleware('prevent.duplicate');
        Route::post('get-details-for-picking-item', [FFCController::class,'getDetailForPickingTransferOrderItem']);
        Route::post('assign-ffc-location-to-picking-item', [FFCController::class,'assignFfcLocationToTransferOrderItem']);
        // Route::post('update-order-temp-locations',[FFCController::class,'pickedTransferOrder']);
        Route::post('submit-to-pd',[FFCController::class,'submitToPDTransferOrder']);
        Route::post('item-picked',[FFCController::class,'itemPickedTransferOrder']);
    });
    
    Route::group(['prefix' => 'setting'],function($router) {
        Route::post('item-counting-mode', [FFCController::class,'itemCountingMode']);
    });
    
});

Route::group([
    'prefix' => 'ffc',
],function($router){
    Route::post('login', [AuthController::class, 'login']);
});


Route::group([
    'middleware' => 'flash',
    'prefix' => 'flash',
],function($router){
    $router->post('generate-loadsheet',[FlashController::class,'generateLoadsheet']);
});


Route::post('s-app/login', 'ShopifyController@login');
Route::post('s-app/setting', 'ShopifyController@setting');

Route::group(['prefix' => 's-app', 'middleware' => 'spwebauth'], function () {

	Route::post('customers/data_request', 'ShopifyController@customersDataRequest');
	Route::post('customers/redact', 'ShopifyController@customersRedact');
	Route::post('shop/redact', 'ShopifyController@shopRedact');
	
});

/// ROUTES FOR NAUTILUS
Route::post('nautilus/forrun-shipper-advice',[ShipperAdviceController::class,'addForrunShipperAdvice'])->middleware('nautilus');
Route::post('nautilus/force-dispatch-shipment',[ShipmentController::class,'dispatchShipment'])->middleware('nautilus');

Route::group([
    'middleware' => 'erpauth',
    'prefix' => 'erp',
],function($router){
    Route::post('generate-loadsheet',[ERPController::class,'generateLoadsheet']);
    Route::post('generate-sticker-label',[ERPController::class,'generateStickerLabel']);
    Route::post('generate-address-label',[ERPController::class,'generateAddressLabel']);
    Route::post('post-grn',[ERPController::class,'postGRN']);
    Route::post('create-stock-transfer-order',[ERPController::class,'createStockTransferOrder']);
    Route::post('udpate-return-receiving',[ERPController::class,'updateReturnReceiving']);
    Route::post('get-list-of-orders',[ERPController::class,'getListOfOrders']);
    Route::post('udpate-fbr-invoice_number',[ERPController::class,'updateFBRInvoiceNumber']);
    Route::post('order-payment-confirmation',[ERPController::class,'orderPaymentConfirmation']);
    Route::post('update-return-receiving',[ERPController::class,'updateReturnReceiving']);

});


Route::group([
    'middleware' => 'auth.myaliceapikey',
    'prefix' => 'myalice',
],function($router){
    Route::post('notify', [MyAliceController::class, 'notify']);
});
