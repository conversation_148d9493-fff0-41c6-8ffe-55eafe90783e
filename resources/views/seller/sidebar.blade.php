<!-- Main sidebar -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">

<div class="sidebar sidebar-main sidebar-default">
    <div class="sidebar-content">

        <!-- USER MENU -->
        <div class="sidebar-user-material">
            <div class="category-content">
                <div class="sidebar-user-material-menu">
                    <a href="#user-nav" data-toggle="collapse" style="color:#253138"><span>My account</span> <i class="caret"></i></a>
                </div>
            </div>

            <div class="navigation-wrapper collapse" id="user-nav">
                <ul class="navigation">

                    <li><a href="/seller/settings/timezone"><i style="font-size: 18px" class="fa fa-clock-o"></i><span>Timezone Setting</span></a></li>

                    @if (!session()->has('user'))
                        <li><a href="/seller/settings/billing-profile"><i style="font-size: 18px" class="icon-info22"></i>Billing Profile</a></li>
                    @endif

                </ul>
            </div>
        </div>
        <!-- /// USER MENU -->





        <!-- MAIN NAVIGATION -->
        <div class="sidebar-category sidebar-category-visible">
            <div class="category-content no-padding">
                <ul class="navigation navigation-main navigation-accordion">
                    
                    <li class="navigation-header"><span>Main</span> <i class="icon-menu" title="Main pages"></i></li>



                    <!-- GET STARTED -->
                    @if (!$others['shipment_exists'])
                        <li style="background-color:#F0692C"><a href="/seller/get-started"><i class="fa fa-hourglass-start"></i><span>Get Started</span></a></li>
                    @endif
                    <!-- /// GET STARTED -->



                    <!-- DASHBOARD -->
                    <li @if (Request::path() == 'seller/dashboard') class="active" @endif><a href="/seller/dashboard"><i class="icon-home4"></i><span>Dashboard</span></a></li>
                    <!-- /// DASHBOARD -->



                    <!-- PACKAGING -->
                    @if ($others['buy_packaging'])
                        <li><a href="https://packaging.unityretail.com/" target="_blank"><i class="icon-package"></i><span>Buy Packaging</span></a></li>
                    @endif
                    <!-- /// PACKAGING -->



                    <!-- PRODUCTS -->
                    <li>
                        <a href="#"><i class="icon-stack2"></i> <span>Products</span></a>
                        <ul>
                            <li @if (in_array(Request::path(), ['seller/product','seller/list','seller/grid'])) class="active" @endif><a href="/seller/product"><i class="icon-list2"></i>View</a></li>
                            <li @if (Request::path() == 'seller/product/bulk_add') class="active" @endif><a href="/seller/product/bulk_add"><i class="icon-file-upload"></i>Bulk Upload</a></li>
                            <li @if (Request::path() == 'seller/product/master-catalogue') class="active" @endif><a href="/seller/product/master-catalogue"><i class="icon-cube3"></i>Master Catalogue</a></li>

                            @if ( (!session()->has('permission') || session('permission')->product_conflict_view))
                                <li @if (Request::path() == 'seller/product/conflicts') class="active" @endif><a href="/seller/product/conflicts"><i class="icon-unlink2"></i>Conflicts</a></li>
                            @endif
                        </ul>
                    </li>
                    <!-- // PRODUCTS -->



                    <!-- SALES -->
                    <li>
                        <a href="#"><i class="icon-basket"></i> <span>Sales</span></a>
                        <ul>
                            <li @if (Request::segment(2) == 'order') class="active" @endif><a href="/seller/order"><i class="icon-box-add"></i>Orders</a></li>

                            @if ( (!session()->has('permission') || session('permission')->order_edit))
                                <li @if (Request::segment(2) == 'hold-for-processing-orders') class="active" @endif><a href="/seller/hold-for-processing-orders"><i class="icon-hand"></i>Hold For Processing</a></li>
                            @endif

                            <li @if (Request::segment(2) == 'shipment') class="active" @endif><a href="/seller/shipment"><i class="icon-database4"></i>Shipments</a></li>

                            @if ( (!session()->has('permission') || session('permission')->view_reverse_shipment) && $add_ons['reverse_shipment'])
                                <li @if (Request::segment(2) == 'reverse-shipment') class="active" @endif><a href="/seller/reverse-shipment"><i class="icon-database-arrow"></i>Reverse Shipment</a></li>
                            @endif

                            <li @if (Request::segment(2) == 'shipper-advice') class="active" @endif><a href="/seller/shipper-advice"><i class="icon-file-text"></i>Shipper Advice</a></li>

                            @if (Auth::id() == 1)
                                <li @if (Request::segment(2) == 'sticker_label') class="active" @endif><a href="/seller/sticker_label"><i class="icon-printer4"></i>Sticker Label</a></li>
                            @endif

                        </ul>
                    </li>
                    <!-- // SALES -->



                    <!-- FULFILLMENT CENTRE -->
                    <li>
                        <a href="#"><i class="icon-clipboard2"></i> <span>Fulfillment Centre</span></a>
                        <ul>
                            @if ($add_ons['ffc'])
                                <li @if (Request::segment(2) == 'ffc-dashboard') class="active" @endif><a href="/seller/ffc-dashboard"><i class="icon-home4"></i>Dashboard</a></li>
                            @endif

                            @if (!session()->has('permission') || session('permission')->ffc_location_create)
                                <li @if (Request::segment(3) == 'seller_locations') class="active" @endif><a href="/seller/inventory/seller_locations"><i class="icon-store2"></i>Locations</a></li>
                            @endif

                            @if ($add_ons['ffc'])

                                <li>
                                    <a href="#"><i class="icon-tree6"></i><span>Storage Location</span></a>
                                    <ul>

                                        <li @if (Request::segment(2) == 'location-hierarchy') class="active" @endif><a href="/seller/location-hierarchy"><i class="icon-cabinet"></i>Bins</a></li>
                                        <li @if (Request::segment(2) == 'location-zones') class="active" @endif><a href="/seller/location-zones"><i class="icon-radio-checked"></i>Zones</a></li>

                                    </ul>
                                </li>
                            @endif

                            @if (!session()->has('permission') || session('permission')->fo_report)
                                <li>
                                    <a href="#"><i class="icon-archive"></i><span>Inventory</span></a>
                                    <ul>

                                        @if ($add_ons['ffc'])
                                            <li @if (Request::segment(2) == 'inventory-hierarchy') class="active" @endif><a href="/seller/inventory-hierarchy"><i class="icon-table2"></i>FFC</a></li>
                                        @endif

                                        @if ($add_ons['inventory'])
                                            <li @if (Request::segment(2) == 'inventory' && Request::segment(3) != 'seller_locations' && Request::segment(3) != 'bulk_add') class="active" @endif><a href="/seller/inventory"><i class="icon-table2"></i>Ledger</a></li>
                                            
                                            @if (!session()->has('permission') || session('permission')->inventory_bulk_upload)
                                                <li @if (Request::path() == 'seller/inventory/bulk_add') class="active" @endif><a href="/seller/inventory/bulk_add"><i class="icon-file-upload"></i>Bulk Upload</a></li>
                                            @endif
                                        @endif

                                    </ul>
                                </li>
                            @endif

                            @if ($add_ons['ffc'])
                                <li @if (Request::segment(2) == 'stock-order' || Request::segment(2) == 'stock-transfer-order') class="active" @endif><a href="/seller/stock-order"><i class="icon-box-add"></i>Stock Orders</a></li>
                                <li @if (Request::segment(2) == 'fulfillment-orders') class="active" @endif><a href="/seller/fulfillment-orders"><i class="icon-package"></i>Fulfillment Orders</a></li>
                                <li @if (Request::path() == 'seller/pick-pack/active-pickers') class="active" @endif><a href="/seller/pick-pack/active-pickers"><i class="icon-box"></i>Pick & Pack</a></li>
                            @endif

                            @if ((!session()->has('permission') || session('permission')->loadsheet) && $others['seller_activated'] && $settings['loadsheet'])
                                <li @if (Request::segment(2) == 'loadsheet') class="active" @endif><a href="/seller/loadsheet"><i class="icon-stack-check"></i>Loadsheet</a></li>
                            @endif

                            @if ( (!session()->has('permission') || session('permission')->return_received) && $settings['return_received_manual'])
                                <li @if (Request::segment(2) == 'return-received') class="active" @endif><a href="/seller/return-received"><i class="icon-undo"></i>Return Receiving</a></li>
                            @endif

                            @if ( (!session()->has('permission') || session('permission')->carton_view))
                                <li @if (Request::segment(2) == 'cartons') class="active" @endif><a href="/seller/cartons"><i class="icon-dropbox"></i>Cartons Management</a></li>
                            @endif

                            @if ( (!session()->has('permission') || session('permission')->fo_report) && $add_ons['ffc'])
                                <li>
                                    <a href="#"><i class="icon-graph"></i><span>Reports</span></a>
                                    <ul>
                                        @if($add_ons['inventory_log'])
                                            <li>
                                                <a href="#"><i class="icon-archive"></i> <span>Inventory Reports</span></a>
                                                <ul>
                                                    <li @if (Request::path() == 'seller/reports/ledger-inventory-report') class="active" @endif><a href="/seller/reports/ledger-inventory-report">Ledger Inventory Report</a></li>
                                                    <li @if (Request::path() == 'seller/reports/ffc-inventory-report') class="active" @endif><a href="/seller/reports/ffc-inventory-report">FFC Inventory Report</a></li>
                                                </ul>
                                            </li>
                                        @endif
                                        <li @if (Request::segment(2) == 'report-inventory-in-hand') class="active" @endif><a href="/seller/report-inventory-in-hand">Inventory In Hand</a></li>
                                        <li @if (Request::segment(2) == 'report-fulfillment-order') class="active" @endif><a href="/seller/report-fulfillment-order">Fulfillment Orders</a></li>
                                        <li @if (Request::segment(2) == 'replenishment') class="active" @endif><a href="/seller/replenishment">Replenishment </a></li>
                                        <li @if (Request::segment(2) == 'pending-putaway') class="active" @endif><a href="/seller/pending-putaway">Pending Put Away </a></li>
                                        <li @if (Request::segment(2) == 'stock-out') class="active" @endif><a href="/seller/stock-out">Stock Out</a></li>
                                        <li @if (Request::segment(2) == 'inventory-snaphot') class="active" @endif><a href="/seller/inventory-snaphot">Inventory Snapshot </a></li>
                                        <li @if (Request::segment(2) == 'bin-wise-fulfillment-order-items') class="active" @endif><a href="/seller/bin-wise-fulfillment-order-items">Bin wise Fulfillment Order Items</a></li>
                                        <li @if (Request::segment(2) == 'transfer-outbound-dump') class="active" @endif><a href="/seller/transfer-outbound-dump">Transfer Outbound</a></li>
                                        <li @if (Request::segment(2) == 'fulfillment-order-dump') class="active" @endif><a href="/seller/fulfillment-order-dump">Fulfillment Data Dump</a></li>
                                        <li @if (Request::segment(2) == 'inbound-snapshot') class="active" @endif><a href="/seller/inbound-snapshot">Inbound Snapshot</a></li>
                                        <li @if (Request::segment(2) == 'inbound-receiving') class="active" @endif><a href="/seller/inbound-receiving">Inbound Receiving</a></li>
                                        <li @if (Request::segment(2) == 'inventory-not-available') class="active" @endif><a href="/seller/inventory-not-available">Item Not Available</a></li>
                                    </ul>
                                </li>
                            @endif

                            @if ((!session()->has('permission') || session('permission')->scan_n_ship) && isset($add_ons['scan-n-ship']) && $add_ons['scan-n-ship'] && !$add_ons['ffc'])
                                <li>
                                    <a href="#"><i class="icon-graph"></i><span>Reports</span></a>
                                    <ul>
                                        <li @if (Request::segment(2) == 'packer-performance') class="active" @endif><a href="/seller/packer-performance">Packer Performance</a></li>
                                    </ul>
                                </li>
                            @endif



                            @if ((!session()->has('permission') || session('permission')->scan_n_ship) && isset($add_ons['scan-n-ship']) && $add_ons['scan-n-ship'])
                            <li @if (Request::segment(2) == 'scan-n-ship') class="active" @endif><a href="/seller/scan-n-ship"><i class="icon-rocket"></i>Scan n Ship</a></li>
                            @endif
                        </ul>
                    </li>
                    <!-- // FULFILLMENT CENTRE -->



                    <!-- AUTO BOOKING -->
                    @if ( (!session()->has('permission') || session('permission')->auto_shipped) && $add_ons['auto_shipped'])
                        <li>
                            <a href="#"><i class="icon-spell-check2"></i> <span>Auto Booking</span></a>
                            <ul>
                                {{-- <li @if (Request::segment(2) == 'auto_shipped') class="active" @endif><a href="/seller/auto_shipped"><i class="icon-touch"></i><span>Run</span></a></li> --}}
                                <li @if (Request::segment(3) == 'auto_shipped') class="active" @endif><a href="/seller/settings/auto_shipped"><i class="icon-list2"></i><span>Rules</span></a></li>
                                <li @if (Request::segment(3) == 'auto_shipped_setting') class="active" @endif><a href="/seller/settings/auto_shipped_setting"><i class="icon-gear"></i><span>Setting</span></a></li>

                                @if ($add_ons['distributed'])
                                    <li @if (Request::segment(2) == 'distributed_system') class="active" @endif><a href="/seller/distributed_system"><i class="icon-pie-chart3"></i><span>Distributed System Criteria</span></a></li>
                                @endif
                            </ul>
                        </li>
                    @endif
                    <!-- // AUTO BOOKING -->



                    <!-- CUSTOMER SUPPORT -->
                    <li>
                        <a href="#"><i class="icon-loop"></i> <span>Customer Support</span></a>
                        <ul>
                            <li @if (Request::segment(2) == 'rma') class="active" @endif><a href="/seller/rma"><i class="icon-ticket"></i><span>Tickets</span></a></li>

                            @if ( (!session()->has('permission') || session('permission')->rma_request) && $add_ons['rma'])
                                <li @if (Request::path() == 'seller/settings/rma-settings') class="active" @endif><a href="/seller/settings/rma-settings"><i class="icon-gear"></i>RMA Settings</a></li>
                            @endif
                        </ul>
                    </li>
                    <!-- // CUSTOMER SUPPORT -->



                    <!-- TRACKAR -->
                    @if ($add_ons['trackar'])
                        <li @if (Request::segment(2) == 'trackar') class="active" @endif><a href="/seller/trackar"><i class="icon-location4"></i> <span>Trackar</span></a></li>
                    @endif
                    <!-- // TRACKAR -->



                    <!-- COD REPORTS -->
                    @if (!session()->has('permission') || session('permission')->cod_reconciliation)
                        <li>
                            <a href="#"><i class="icon-cash3"></i> <span>COD</span></a>
                            <ul>
                                <li @if (Request::path() == 'seller/reports/cod_receivables') class="active" @endif><a href="/seller/reports/cod_receivables"><i class="icon-file-download"></i>COD Receivables</a></li>
                                <li @if (Request::path() == 'seller/reports/cod_payments') class="active" @endif><a href="/seller/reports/cod_payments"><i class="icon-cash"></i>COD Payments</a></li>
                                <li @if (Request::path() == 'seller/reports/cod_received') class="active" @endif><a href="/seller/reports/cod_received"><i class="icon-file-check"></i>COD Received</a></li>
                            </ul>
                        </li>
                    @endif
                    <!-- // COD REPORTS -->



                    <!-- REPORTS -->
                    @if ( (!session()->has('permission') || session('permission')->reports) && $add_ons['basic_report'])
                        <li>
                            <a href="#"><i class="icon-graph"></i> <span>Reports</span></a>
                            <ul>
                                <li>
                                    <a href="#"><i class="icon-cash3"></i> <span>COD Reports</span></a>
                                    <ul>
                                        <li @if (Request::path() == 'seller/reports/cod-liability-report') class="active" @endif><a href="/seller/reports/cod-liability-report">COD Liability Report</a></li>
                                        <li @if (Request::path() == 'seller/reports/cod-receivables-aging-report') class="active" @endif><a href="/seller/reports/cod-receivables-aging-report">COD Receivables Aging Report</a></li>
                                        <li @if (Request::path() == 'seller/reports/cod-return-in-transit-aging-report') class="active" @endif><a href="/seller/reports/cod-return-in-transit-aging-report">COD Return in Transit Aging Report</a></li>
                                        <li @if (Request::path() == 'seller/reports/cod-return-report') class="active" @endif><a href="/seller/reports/cod-return-report">COD Returns Report</a></li>
                                    </ul>
                                </li>
                                <li @if (Request::path() == 'seller/reports/ops-productivity-report') class="active" @endif><a href="/seller/reports/ops-productivity-report">OPS Productivity Report</a></li>
                                <li @if (Request::path() == 'seller/reports/courier-performance-report') class="active" @endif><a href="/seller/reports/courier-performance-report">Courier Performance Report</a></li>
                                {{-- <li @if (Request::path() == 'seller/reports/order-master-report') class="active" @endif><a href="/seller/reports/order-master-report">Order Master Report</a></li> --}}
                                <li @if (Request::path() == 'seller/reports/shipment-master-report') class="active" @endif><a href="/seller/reports/shipment-master-report">Shipment Master Report</a></li>
                                
                                @if ( (!session()->has('permission') || session('permission')->return_received) && $settings['return_received_manual'])
                                    <li @if (Request::path() == 'seller/reports/return-receiving-report') class="active" @endif><a href="/seller/reports/return-receiving-report">Return Receiving Report</a></li>
                                @endif
                                
                                <li @if (Request::path() == 'seller/reports/exception-report') class="active" @endif><a href="/seller/reports/exception-report">Exception Report</a></li>

                                @if ($add_ons['advanced_report'])
                                    <li @if (Request::path() == 'seller/reports/aging-report') class="active" @endif><a href="/seller/reports/aging-report">Aging Report (hour)</a></li>
                                    <li @if (Request::path() == 'seller/reports/aging-report-month-wise') class="active" @endif><a href="/seller/reports/aging-report-month-wise">Aging Report (month)</a></li>
                                    <li @if (Request::path() == 'seller/reports/last-mile-aging-report') class="active" @endif><a href="/seller/reports/last-mile-aging-report">Last Mile Aging Report</a></li>
                                    <li @if (Request::path() == 'seller/reports/aging-report-courier-wise') class="active" @endif><a href="/seller/reports/aging-report-courier-wise">Aging Report (Courier)</a></li>
                                    <li @if (Request::path() == 'seller/reports/aging-report-day-wise') class="active" @endif><a href="/seller/reports/aging-report-day-wise">Aging Report (Day)</a></li>
                                    <li @if (Request::path() == 'seller/reports/aging-report-time-wise') class="active" @endif><a href="/seller/reports/aging-report-time-wise">Aging Report (Time)</a></li>

                                    <li @if (Request::path() == 'seller/reports/order-delivery-tat') class="active" @endif><a href="/seller/reports/order-delivery-tat">Order Delivery TAT</a></li>
                                    <li @if (Request::path() == 'seller/reports/last-mile-payments-reconciliation') class="active" @endif><a href="/seller/reports/last-mile-payments-reconciliation">Last Mile Reconciliation Summary</a></li>
                                    <li @if (Request::path() == 'seller/reports/last-mile-reconciliation-sheet') class="active" @endif><a href="/seller/reports/last-mile-reconciliation-sheet">Last Mile Reconciliation Sheet</a></li>
                                    <li @if (Request::path() == 'seller/reports/fulfillment-velocity-report') class="active" @endif><a href="/seller/reports/fulfillment-velocity-report">Fulfillment Velocity Report</a></li>
                                    <li @if (Request::path() == 'seller/reports/courier-kpi') class="active" @endif><a href="/seller/reports/courier-kpi">Courier KPI</a></li>
                                    <li @if (Request::path() == 'seller/reports/last-mile-dashboard') class="active" @endif><a href="/seller/reports/last-mile-dashboard">Last Mile Dashboard</a></li>

                                    <li @if (Request::path() == 'seller/reports/order-data-dump-report') class="active" @endif><a href="/seller/reports/order-data-dump-report">Order Data Dump Report</a></li>

                                    <li @if (Request::path() == 'seller/reports/order-cancellation-analysis-report') class="active" @endif><a href="/seller/reports/order-cancellation-analysis-report">Order Cancellation Analysis Report</a></li>
                                    <li @if (Request::path() == 'seller/reports/sales-analysis-report') class="active" @endif><a href="/seller/reports/sales-analysis-report">Sales Analysis Report</a></li>
                                    <li @if (Request::path() == 'seller/reports/order-cancellation-reasons-report') class="active" @endif><a href="/seller/reports/order-cancellation-reasons-report">Order Cancellation Reason Report</a></li>
                                    <li @if (Request::path() == 'seller/reports/product-dump-report') class="active" @endif><a href="/seller/reports/product-dump-report">Product Dump Report</a></li>
                                    <li @if (Request::path() == 'seller/reports/courier-statuses-history-report') class="active" @endif><a href="/seller/reports/courier-statuses-history-report">Courier Statuses History Report</a></li>
                                    <li @if (Request::path() == 'seller/reports/shipment-master-dump-report') class="active" @endif><a href="/seller/reports/shipment-master-dump-report">Shipment Master Dump Report</a></li>
                                    <li @if (Request::path() == 'seller/reports/shipment-status-timestamp-report') class="active" @endif><a href="/seller/reports/shipment-status-timestamp-report">Shipment Status Timestamp Report</a></li>
                                    <li @if (Request::path() == 'seller/reports/shipment-status-history-report') class="active" @endif><a href="/seller/reports/shipment-status-history-report">Shipment Status History Report</a></li>
                                    <li @if (Request::path() == 'seller/reports/lost-shipment-items-report') class="active" @endif><a href="/seller/reports/lost-shipment-items-report">Lost Shipments & Items Report</a></li>
                                    <li @if (Request::path() == 'seller/reports/seven-days-dispatch-dump-report') class="active" @endif><a href="/seller/reports/seven-days-dispatch-dump-report">Seven Days Dispatch Report</a></li>

                                    @endif

                            </ul>
                        </li>
                    @endif
                    <!-- // REPORTS -->



                    <!-- SETTINGS -->
                    @if (!session()->has('user') || session('permission')->setting == 1)
                        <li>
                            <a href="#"><i class="icon-gear"></i> <span>Settings</span></a>
                            <ul>
                                <li>
                                    <a href="#"><i class="icon-truck"></i> <span>Couriers</span></a>
                                    <ul>
                                        <li @if (Request::segment(3) == 'couriers') class="active" @endif><a href="/seller/settings/couriers">Couriers List</a></li>
                                        
                                        @if ($add_ons['auto_shipped'])
                                            <li @if (Request::segment(3) == 'courier_limit') class="active" @endif><a href="/seller/settings/courier_limit">Couriers Limit</a></li>
                                        @endif
                                    </ul>
                                </li>

                                <li>
                                    <a href="#"><i class="icon-price-tag2"></i> <span>Tag / Reason</span></a>
                                    <ul>
                                        <li @if (Request::segment(3) == 'tag') class="active" @endif><a href="/seller/settings/tag">Order Status Tag</a></li>
                                        <li @if (Request::path() == 'seller/settings/reason') class="active" @endif><a href="/seller/settings/reason">Shipments Return Reasons</a></li>
                                        <li @if (Request::path() == 'seller/settings/reason/cancel-home') class="active" @endif><a href="/seller/settings/reason/cancel-home">Order Cancellation Reasons</a></li>
                                    </ul>
                                </li>

                                @if ($add_ons['audit_log'])
                                    <li>
                                        <a href="#"><i class="icon-price-tag2"></i> <span>Audit Log</span></a>
                                        <ul>
                                            <li @if (Request::path() == 'seller/settings/audit-log-settings') class="active" @endif><a href="/seller/settings/audit-log-settings">Audit Log Settings</a></li>
                                            <li @if (Request::path() == 'seller/settings/audit-logs') class="active" @endif><a href="/seller/settings/audit-logs">Audit Logs</a></li>
                                        </ul>
                                    </li>
                                @endif

                                <li>
                                    <a href="#"><i class="icon-three-bars"></i> <span>Others</span></a>
                                    <ul>
                                        @if ($add_ons['auto_order_confirm'])
                                            <li @if (Request::path() == 'seller/settings/auto-order-confirmation') class="active" @endif><a href="/seller/settings/auto-order-confirmation">Order Confirmation Settings</a></li>
                                        @endif

                                            <li @if (Request::path() == 'seller/settings/
                                            ') class="active" @endif><a href="/seller/settings/order_settings">Order Settings</a></li>

                                        <li @if (Request::path() == 'seller/settings/shipment_settings') class="active" @endif><a href="/seller/settings/shipment_settings">Shipment Settings</a></li>

                                        <li @if (Request::path() == 'seller/settings/shipping_settings') class="active" @endif><a href="/seller/settings/shipping_settings">Shipping Settings</a></li>

                                        @if ($add_ons['ffc'])
                                            <li @if (Request::path() == 'seller/settings/ffc_settings') class="active" @endif><a href="/seller/settings/ffc_settings">FFC Settings</a></li>
                                        @endif

                                        @if ($add_ons['omni_location_assignment'])
                                            <li @if (Request::path() == 'seller/settings/omni_location_assignment') class="active" @endif><a href="/seller/settings/omni_location_assignment">Omni Location Settings</a></li>
                                        @endif

                                        <li @if (Request::path() == 'seller/settings/exception-list') class="active" @endif><a href="/seller/settings/exception-list">Exception List</a></li>

                                        @if ($add_ons['shipping_method'])
                                            <li @if (Request::path() == 'seller/settings/shipping_methods') class="active" @endif><a href="/seller/settings/shipping_methods">Shipping Methods</a></li>
                                        @endif

                                        @if ($add_ons['fbr'])
                                            <li @if (Request::path() == 'seller/settings/fbr') class="active" @endif><a href="/seller/settings/fbr">FBR </a></li>
                                        @endif

                                        <li @if (Request::path() == 'seller/settings/invoice_customization') class="active" @endif><a href="/seller/settings/invoice_customization">Invoice Customization</a></li>

                                        @if ($add_ons['payment_method'])
                                            <li @if (Request::path() == 'seller/settings/shipping_methods') class="active" @endif><a href="/seller/settings/payment_methods">Payment Methods</a></li>
                                        @endif

                                        @if ($add_ons['wallet'])
                                            <li @if (Request::path() == 'seller/settings/transactions') class="active" @endif><a href="/seller/settings/transactions">Transaction History</a></li>
                                        @endif

                                        <li @if (Request::path() == 'seller/settings/integration') class="active" @endif><a href="/seller/settings/integration">Connect Online Store</a></li>

                                        @if ($add_ons['sub_user'])
                                            <li @if (Request::segment(2) == 'user') class="active" @endif><a href="/seller/user">Sub Users</a></li>
                                        @endif

                                        <li @if (Request::path() == 'seller/settings/change_password') class="active" @endif><a href="/seller/settings/change_password">Change Password</a></li>
                                    </ul>
                                </li>

                            </ul>
                        </li>
                    @endif
                    <!-- // SETTINGS -->



                    <!-- LOGOUT -->
                    <li><a href="#" onclick="document.forms['logout'].submit(); return false;"><i style="font-size: 18px" class="icon-switch2"></i>Logout</a></li>
                    <!-- // LOGOUT -->

                </ul>
            </div>
        </div>
        <!-- /main navigation -->
    </div>
</div>
<!-- /main sidebar -->