 @extends('seller.admin_template')

@section('pageTitle', 'Settings')
@section('content')

<script src="{{ asset ("assets/js/plugins/forms/styling/switchery.min.js ") }}"></script>
<script src="{{ asset ("assets/js/plugins/forms/styling/switch.min.js ") }}"></script>

<script src="{{ asset ("assets/js/plugins/forms/styling/uniform.min.js ") }}"></script>
<script src="{{ asset ("assets/js/pages/form_inputs.js ") }}"></script>
<script src="{{ asset ("assets/js/plugins/ui/ripple.min.js ") }}"></script>
<script src="{{ asset ("assets/js/pages/uploader_bootstrap.js ") }}"></script>
<script src="{{ asset ("assets/js/plugins/uploaders/fileinput/fileinput.min.js ") }}"></script>

<style>
a.disabled {
  pointer-events: none;
  cursor: default;
}
</style>


<script src="{{ asset ("assets/js/pages/form_layouts.js ") }}"></script>

@include('seller.notification')



<!-- 2 columns form -->
<form class="form-horizontal" action="" method="POST" enctype="multipart/form-data">
    <div class="panel panel-primary">
        <div class="panel-heading">
            <h5 class="panel-title"><i class="icon-truck"></i>&nbsp Courier Configure :  <span class="label" style="background: white; color: black; font-size: medium; "><b>{{ $courier['name'] }}</b></span> </h5>
        </div>

        <div class="panel-body">
            <div class="row">
				
				@if (isset($courier_status) && $courier_status->is_universal == 2)
					<div class="col-md-12">
				@else
					<div class="col-md-9">
				@endif

					<input type="hidden" name="courier_id" value="{{ $courier['id'] }}">

                    <fieldset id="account_details">
                        <legend class="text-semibold"><i class="icon-file-text position-left"></i>Account Details</legend>

						@if ($courier['id'] == 1)

							<div class="form-group">
								<div class="col-md-3">
									<label><b>Prefix (For Tracking Number)</b></label>
									<input type="text" class="form-control" name="prefix" maxLength="50" placeholder="Prefix" @if ($self) value="{{ $self['prefix'] }}" @endif required>
								</div>
								
								<div class="col-md-5 text-center">
									<label><b>User Generated - Random Generated</b></label>
									<div style="display: flex; justify-content: center; padding-right: 70px">
										<div class="checkbox checkbox-switchery">
											<input type="checkbox" id="prefix_enable" name="prefix_enable" class="switchery" value="1" @if ($self && $self['tracking_number_option']) checked @endif> 
										</div>
									</div>
									<span class="text-muted small">Note : User Generated option is only for single shipment booking , for multiple you have to select random generated option</span>
								</div>

								<div class="col-md-4" id="random_generated">
									<div class="radio">
										<label>
											<input type="radio" name="radio_button" value="random" class="styled" @if ($self && $self['tracking_number_option'] == 'random') checked @endif>
											{ Prefix } - Random Number (7 digit)
										</label>
									</div>

									<div class="radio">
										<label>
											<input type="radio" name="radio_button" value="not_random" class="styled"  @if ($self && $self['tracking_number_option'] == 'not_random') checked @endif>
											{ Prefix } - { Order Reference No } - { Shipment Number }
										</label>
									</div>
								</div>
							</div>
							<div class="form-group">
								<label class="col-lg-3 pt-15 control-label"><b>Name : <span style="color:red">*</span></b></label>
								<div class="col-lg-3">
									<input type="text" class="form-control" name="name" maxLength="20" placeholder="Enter Name"  @if ($self) value="{{ $self['name'] }}" @endif required>
								</div>
							</div>
							<div class="form-group">
								<label class="col-lg-3 pt-15 control-label"><b>Image</b></label>
								<div class="col-lg-9">
									<input type="file" name="image" class="file-styled" data-preview-file-type="text" @if (!$self) required @endif>
								</div>
							</div>

						@elseif ($courier['id'] == 4)

								<div class="form-group">
									<label class="col-lg-3 pt-15 control-label"><b>Title of Account : <span style="color:red">*</span></b></label>
									<div class="col-lg-9">
										<input type="text" class="form-control" maxLength="30" placeholder="Enter your Title of Account" name="account_title"  @if ($mnp) value="{{ $mnp['account_title'] }}" @endif required data-validation-required-message="Please enter your title of account">
										<span class="text-muted">Note : This is going to be display on your shipment's address / sticker label only in unity-retail.</span>
									</div>
								</div>

								<div class="form-group">
									<label class="col-lg-3 pt-15 control-label"><b>Username : <span style="color:red">*</span></b></label>
									<div class="col-lg-9">
										<input type="text" class="form-control" maxLength="60" id="username" placeholder="Enter your username which is provided by courier" name="username" @if ($mnp) value="{{ $mnp['username'] }}" @endif required data-validation-required-message="Please enter username">
									</div>
								</div>

								<div class="form-group">
									<label class="col-lg-3 pt-15 control-label"><b>Password : <span style="color:red">*</span></b></label>
									<div class="col-lg-9">
										<input type="password" class="form-control" maxLength="60" id="password" placeholder="Enter your password which is provided by courier" name="password" @if ($mnp) value="{{ $mnp['password'] }}" @endif required data-validation-required-message="Please enter password">
									</div>
								</div>

								<div class="form-group">
									<label class="col-lg-3 pt-15 control-label"><b>Account No : <span style="color:red">*</span></b></label>
									<div class="col-lg-9">
										<input type="text" class="form-control" maxLength="20" id="account_no" placeholder="Enter your account no which is provided by courier" name="account_no" @if ($mnp) value="{{ $mnp['account_no'] }}" @endif required data-validation-required-message="Please enter account no">
									</div>
								</div>

						@elseif ($courier['id'] == 5)

							<div class="form-group">
								<label class="col-lg-3 pt-15 control-label"><b>Title of Account : <span style="color:red">*</span></b></label>
								<div class="col-lg-9">
									<input type="text" class="form-control" maxLength="30" placeholder="Enter your Title of Account" name="account_title"  @if ($lcs) value="{{ $lcs['account_title'] }}" @endif required data-validation-required-message="Please enter your title of account">
									<span class="text-muted">Note : This is going to be display on your shipment's address / sticker label only in unity-retail.</span>
								</div>
							</div>

							<div class="form-group">
								<label class="col-lg-3 pt-15 control-label"><b>API Key : <span style="color:red">*</span></b></label>
								<div class="col-lg-9">
									<input type="text" class="form-control" maxLength="60" id="api_key" placeholder="Enter your api key which is provided by courier" name="api_key" @if ($lcs) value="{{ $lcs['api_key'] }}" @endif required data-validation-required-message="Please enter api key">
								</div>
							</div>

							<div class="form-group">
								<label class="col-lg-3 pt-15 control-label"><b>API Password : <span style="color:red">*</span></b></label>
								<div class="col-lg-9">
									<input type="password" class="form-control" maxLength="60" id="api_password" placeholder="Enter your api password which is provided by courier" name="api_password" @if ($lcs) value="{{ $lcs['api_password'] }}" @endif required data-validation-required-message="Please enter api password">
								</div>
							</div>

							<div class="form-group">
								<label class="col-lg-3 pt-15 control-label"><b>Operation Person Email : </b></label>
								<div class="col-lg-9">
									<input type="email" class="form-control" maxLength="40" id="email" placeholder="Enter your email" name="email" @if ($lcs) value="{{ $lcs['email'] }}" @endif >
									<span class="text-muted">Note : Courier will send return and cancellation emails on this email.</span>
								</div>
							</div>

						@elseif ($courier['id'] == 7)

							<div class="form-group">
								<label class="col-lg-3 pt-15 control-label"><b>Title of Account : <span style="color:red">*</span></b></label>
								<div class="col-lg-9">
									<input type="text" class="form-control" maxLength="30" placeholder="Enter your Title of Account" name="account_title"  @if ($call_courier) value="{{ $call_courier['account_title'] }}" @endif required data-validation-required-message="Please enter your title of account">
									<span class="text-muted">Note : This is going to be display on your shipment's address / sticker label only in unity-retail.</span>
								</div>
							</div>

							<div class="form-group">
								<label class="col-lg-3 pt-15 control-label"><b>Username : <span style="color:red">*</span></b></label>
								<div class="col-lg-9">
									<input type="text" class="form-control" maxLength="50" id="username" placeholder="Enter your username which is provided by courier" name="username" @if ($call_courier) value="{{ $call_courier['username'] }}" @endif required data-validation-required-message="Please enter username">
								</div>
							</div>

							<div class="form-group">
								<label class="col-lg-3 pt-15 control-label"><b>Password : <span style="color:red">*</span></b></label>
								<div class="col-lg-9">
									<input type="password" class="form-control" maxLength="50" id="password" placeholder="Enter your password which is provided by courier" name="password" @if ($call_courier) value="{{ $call_courier['password'] }}" @endif required data-validation-required-message="Please enter password">
								</div>
							</div>

							<div class="form-group">
								<label class="col-lg-3 pt-15 control-label"><b>Account ID : <span style="color:red">*</span></b></label>
								<div class="col-lg-9">
									<input type="text" class="form-control" maxLength="50" id="account_id" placeholder="Enter your account id which is provided by courier" name="account_id" @if ($call_courier) value="{{ $call_courier['account_id'] }}" @endif required data-validation-required-message="Please enter account id">
								</div>
							</div>
							
						@elseif ($courier['id'] == 8)

							<input type="hidden" name= "default_picker_address_id" @if ($trax_new) value="{{ $trax_new['default_picker_address_id'] }}" @endif >

							<div class="form-group">
								<label class="col-lg-3 pt-15 control-label"><b>Title of Account : <span style="color:red">*</span></b></label>
								<div class="col-lg-9">
									<input type="text" class="form-control" maxLength="30" placeholder="Enter your Title of Account" name="account_title"  @if ($trax_new) value="{{ $trax_new['account_title'] }}" @endif required data-validation-required-message="Please enter your title of account">
									<span class="text-muted">Note : This is going to be display on your shipment's address / sticker label only in unity-retail.</span>
								</div>
							</div>

							<div class="form-group">
								<label class="col-lg-3 pt-15 control-label"><b>Authorization Key : <span style="color:red">*</span></b></label>
								<div class="col-lg-9">
									<input type="text" class="form-control" maxLength="150" id="authorization_key" placeholder="Enter your authorization key which is provided by courier" name="authorization_key" @if ($trax_new) value="{{ $trax_new['authorization_key'] }}" @endif required data-validation-required-message="Please enter authorization key">
								</div>
							</div>

						@elseif ($courier['id'] == 10)
							
							<div class="form-group">
									
								<label class="col-lg-3 pt-15 control-label"><b>Title of Account : <span style="color:red">*</span></b></label>
								<div class="col-lg-9">
									<input type="text" class="form-control" maxLength="30" placeholder="Enter your Title of Account" name="account_title"  @if ($blueEx) value="{{ $blueEx['account_title'] }}" @endif required data-validation-required-message="Please enter your title of account">
									<span class="text-muted">Note : This is going to be display on your shipment's address / sticker label only in unity-retail.</span>
								</div>
							</div>
							<div class="form-group">
								<label class="col-lg-3 pt-15 control-label"><b>Api Key : <span style="color:red">*</span></b></label>
								<div class="col-lg-9">
									<input type="text" class="form-control" name= "api_key" maxLength="50" id="api_key" placeholder="Enter your api key which is provided by courier" @if ($blueEx) value="{{ $blueEx['api_key'] }}" @endif required data-validation-required-message="Please enter api key">
								</div>
							</div>
							

							<div class="form-group">
								<label class="col-lg-3 pt-15 control-label"><b>User ID : <span style="color:red">*</span></b></label>
								<div class="col-lg-9">
									<input type="text" class="form-control" maxLength="50" id="user_id" placeholder="Enter your user id which is provided by courier" name="user_id" @if ($blueEx) value="{{ $blueEx['user_id'] }}" @endif required data-validation-required-message="Please enter user id">
								</div>
							</div>

							<div class="form-group">
								<label class="col-lg-3 pt-15 control-label"><b>Password : <span style="color:red">*</span></b></label>
								<div class="col-lg-9">
									<input type="password" class="form-control" maxLength="50" id="password" placeholder="Enter your password which is provided by courier" name="password" @if ($blueEx) value="{{ $blueEx['password'] }}" @endif required data-validation-required-message="Please enter password">
								</div>
							</div>
							
							<div class="form-group">
								<label class="col-lg-3 pt-15 control-label"><b>Account No : <span style="color:red">*</span></b></label>
								<div class="col-lg-9">
									<input type="text" class="form-control" maxLength="30" id="account_no" placeholder="Enter your account no which is provided by courier" name="account_no" @if ($blueEx) value="{{ $blueEx['account_no'] }}" @endif required data-validation-required-message="Please enter account no">
								</div>
							</div>

						@elseif ($courier['id'] == 11)

							<div class="form-group">
								<label class="col-lg-3 pt-15 control-label"><b>Title of Account : <span style="color:red">*</span></b></label>
								<div class="col-lg-9">
									<input type="text" class="form-control" maxLength="30" placeholder="Enter your Title of Account" name="account_title"  @if ($rider) value="{{ $rider['account_title'] }}" @endif required data-validation-required-message="Please enter your title of account">
									<span class="text-muted">Note : This is going to be display on your shipment's address / sticker label only in unity-retail.</span>
								</div>
							</div>

							<div class="form-group">
								<label class="col-lg-3 pt-15 control-label"><b>Login ID : <span style="color:red">*</span></b></label>
								<div class="col-lg-9">
									<input type="text" class="form-control" maxLength="50" id="login_id" placeholder="Enter your login id which is provided by courier" name="login_id" @if ($rider) value="{{ $rider['login_id'] }}" @endif required data-validation-required-message="Please enter login id">
								</div>
							</div>

							<div class="form-group">
								<label class="col-lg-3 pt-15 control-label"><b>API Key : <span style="color:red">*</span></b></label>
								<div class="col-lg-9">
									<input type="text" class="form-control" maxLength="250" id="api_key" placeholder="Enter your api key which is provided by courier" name="api_key" @if ($rider) value="{{ $rider['api_key'] }}" @endif required data-validation-required-message="Please enter api key">
								</div>
							</div>
								
						@elseif ($courier['id'] == 12)

							<div class="form-group">
								<label class="col-lg-3 pt-15 control-label"><b>Title of Account : <span style="color:red">*</span></b></label>
								<div class="col-lg-9">
									<input type="text" class="form-control" maxLength="30" placeholder="Enter your Title of Account" name="account_title"  @if ($delivery_express) value="{{ $delivery_express['account_title'] }}" @endif required data-validation-required-message="Please enter your title of account">
									<span class="text-muted">Note : This is going to be display on your shipment's address / sticker label only in unity-retail.</span>
								</div>
							</div>

							<div class="form-group">
								<label class="col-lg-3 pt-15 control-label"><b>API Key : <span style="color:red">*</span></b></label>
								<div class="col-lg-9">
									<input type="text" class="form-control" maxLength="50" id="api_key" placeholder="Enter your api key which is provided by courier" name="api_key" @if ($delivery_express) value="{{ $delivery_express['api_key'] }}" @endif required data-validation-required-message="Please enter api key">
								</div>
							</div>

						@elseif ($courier['id'] == 13)

								<div class="form-group">
									<label class="col-lg-3 pt-15 control-label"><b>Title of Account : <span style="color:red">*</span></b></label>
									<div class="col-lg-9">
										<input type="text" class="form-control" maxLength="30" placeholder="Enter your Title of Account" name="account_title"  @if ($tcs_new) value="{{ $tcs_new['account_title'] }}" @endif required data-validation-required-message="Please enter your title of account">
										<span class="text-muted">Note : This is going to be display on your shipment's address / sticker label only in unity-retail.</span>
									</div>
								</div>

								<div class="form-group">
									<label class="col-lg-3 pt-15 control-label"><b>Username : <span style="color:red">*</span></b></label>
									<div class="col-lg-9">
										<input type="text" class="form-control" maxLength="60" id="username" placeholder="Enter your username which is provided by courier" name="username" @if ($tcs_new) value="{{ $tcs_new['username'] }}" @endif required data-validation-required-message="Please enter username">
									</div>
								</div>

								<div class="form-group">
									<label class="col-lg-3 pt-15 control-label"><b>Password : <span style="color:red">*</span></b></label>
									<div class="col-lg-9">
										<input type="password" class="form-control" maxLength="60" id="password" placeholder="Enter your password which is provided by courier" name="password" @if ($tcs_new) value="{{ $tcs_new['password'] }}" @endif required data-validation-required-message="Please enter password">
									</div>
								</div>

						@elseif ($courier['id'] == 14)

							<div class="form-group">
								<label class="col-lg-3 pt-15 control-label"><b>Title of Account : <span style="color:red">*</span></b></label>
								<div class="col-lg-9">
									<input type="text" class="form-control" maxLength="30" placeholder="Enter your Title of Account" name="account_title"  @if ($lcs_uae) value="{{ $lcs_uae['account_title'] }}" @endif required data-validation-required-message="Please enter your title of account">
									<span class="text-muted">Note : This is going to be display on your shipment's address / sticker label only in unity-retail.</span>
								</div>
							</div>

							<div class="form-group">
								<label class="col-lg-3 pt-15 control-label"><b>Account Number : <span style="color:red">*</span></b></label>
								<div class="col-lg-9">
									<input type="text" class="form-control" maxLength="50" id="account_number" placeholder="Enter your account number which is provided by courier" name="account_number" @if ($lcs_uae) value="{{ $lcs_uae['account_number'] }}" @endif required data-validation-required-message="Please enter account number">
								</div>
							</div>

						@elseif ($courier['id'] == 15)

							<div class="form-group">
								<label class="col-lg-3 pt-15 control-label"><b>Title of Account : <span style="color:red">*</span></b></label>
								<div class="col-lg-9">
									<input type="text" class="form-control" maxLength="30" placeholder="Enter your Title of Account" name="account_title"  @if ($swyft) value="{{ $swyft['account_title'] }}" @endif required data-validation-required-message="Please enter your title of account">
									<span class="text-muted">Note : This is going to be display on your shipment's address / sticker label only in unity-retail.</span>
								</div>
							</div>

							<div class="form-group">
								<label class="col-lg-3 pt-15 control-label"><b>Vendor ID : <span style="color:red">*</span></b></label>
								<div class="col-lg-9">
									<input type="text" class="form-control" maxLength="50" id="vendor_id" placeholder="Enter your vendor id which is provided by courier" name="vendor_id" @if ($swyft) value="{{ $swyft['vendor_id'] }}" @endif required data-validation-required-message="Please enter vendor id">
								</div>
							</div>

							<div class="form-group">
								<label class="col-lg-3 pt-15 control-label"><b>Vendor Secret : <span style="color:red">*</span></b></label>
								<div class="col-lg-9">
									<input type="text" class="form-control" maxLength="200" id="vendor_secret" placeholder="Enter your vendor secret which is provided by courier" name="vendor_secret" @if ($swyft) value="{{ $swyft['vendor_secret'] }}" @endif required data-validation-required-message="Please enter vendor secret">
								</div>
							</div>

						@elseif ($courier['id'] == 16)

							<div class="form-group">
								<label class="col-lg-3 pt-15 control-label"><b>Title of Account : <span style="color:red">*</span></b></label>
								<div class="col-lg-9">
									<input type="text" class="form-control" maxLength="30" placeholder="Enter your Title of Account" name="account_title"  @if ($dpd) value="{{ $dpd['account_title'] }}" @endif required data-validation-required-message="Please enter your title of account">
									<span class="text-muted">Note : This is going to be display on your shipment's address / sticker label only in unity-retail.</span>
								</div>
							</div>

							<div class="form-group">
								<label class="col-lg-3 pt-15 control-label"><b>Username : <span style="color:red">*</span></b></label>
								<div class="col-lg-9">
									<input type="text" class="form-control" maxLength="50" id="username" placeholder="Enter your username which is provided by courier" name="username" @if ($dpd) value="{{ $dpd['username'] }}" @endif required data-validation-required-message="Please enter username">
								</div>
							</div>

							<div class="form-group">
								<label class="col-lg-3 pt-15 control-label"><b>Password : <span style="color:red">*</span></b></label>
								<div class="col-lg-9">
									<input type="password" class="form-control" maxLength="50" id="password" placeholder="Enter your password which is provided by courier" name="password" @if ($dpd) value="{{ $dpd['password'] }}" @endif required data-validation-required-message="Please enter password">
								</div>
							</div>

						@elseif ($courier['id'] == 17)

							<div class="form-group">
								<label class="col-lg-3 pt-15 control-label"><b>Title of Account : <span style="color:red">*</span></b></label>
								<div class="col-lg-9">
									<input type="text" class="form-control" maxLength="30" placeholder="Enter your Title of Account" name="account_title"  @if ($dhl) value="{{ $dhl['account_title'] }}" @endif required data-validation-required-message="Please enter your title of account">
									<span class="text-muted">Note : This is going to be display on your shipment's address / sticker label only in unity-retail.</span>
								</div>
							</div>

							<div class="form-group">
								<label class="col-lg-3 pt-15 control-label"><b>Username : <span style="color:red">*</span></b></label>
								<div class="col-lg-9">
									<input type="text" class="form-control" maxLength="50" id="username" placeholder="Enter your username which is provided by courier" name="username" @if ($dhl) value="{{ $dhl['username'] }}" @endif required data-validation-required-message="Please enter username">
								</div>
							</div>

							<div class="form-group">
								<label class="col-lg-3 pt-15 control-label"><b>Password : <span style="color:red">*</span></b></label>
								<div class="col-lg-9">
									<input type="password" class="form-control" maxLength="50" id="password" placeholder="Enter your password which is provided by courier" name="password" @if ($dhl) value="{{ $dhl['password'] }}" @endif required data-validation-required-message="Please enter password">
								</div>
							</div>

							<div class="form-group">
								<label class="col-lg-3 pt-15 control-label"><b>Acoount No : <span style="color:red">*</span></b></label>
								<div class="col-lg-9">
									<input type="text" class="form-control" maxLength="50" id="account_no" placeholder="Enter your account no which is provided by courier" name="account_no" @if ($dhl) value="{{ $dhl['account_no'] }}" @endif required data-validation-required-message="Please enter account no">
								</div>
							</div>

							
							

						@elseif ($courier['id'] == 18)
						
							<div class="form-group">
								<label class="col-lg-3 pt-15 control-label"><b>Title of Account : <span style="color:red">*</span></b></label>
								<div class="col-lg-9">
									<input type="text" class="form-control" maxLength="30" placeholder="Enter your Title of Account" name="account_title"  @if ($courierx) value="{{ $courierx['account_title'] }}" @endif required data-validation-required-message="Please enter your title of account">
									<span class="text-muted">Note : This is going to be display on your shipment's address / sticker label only in unity-retail.</span>
								</div>
							</div>

							<div class="form-group">
								<label class="col-lg-3 pt-15 control-label"><b>Client Code : <span style="color:red">*</span></b></label>
								<div class="col-lg-9">
									<input type="text" class="form-control" maxLength="50" id="client_code" placeholder="Enter your client code which is provided by courier" name="client_code" @if ($courierx) value="{{ $courierx['client_code'] }}" @endif required data-validation-required-message="Please enter client code">
								</div>
							</div>

						@elseif ($courier['id'] == 19)

							<div class="form-group">
								<label class="col-lg-3 pt-15 control-label"><b>Title of Account : <span style="color:red">*</span></b></label>
								<div class="col-lg-9">
									<input type="text" class="form-control" maxLength="30" placeholder="Enter your Title of Account" name="account_title"  @if ($movex) value="{{ $movex['account_title'] }}" @endif required data-validation-required-message="Please enter your title of account">
									<span class="text-muted">Note : This is going to be display on your shipment's address / sticker label only in unity-retail.</span>
								</div>
							</div>

							<div class="form-group">
								<label class="col-lg-3 pt-15 control-label"><b>API Key : <span style="color:red">*</span></b></label>
								<div class="col-lg-9">
									<input type="text" class="form-control" maxLength="50" id="api_key" placeholder="Enter your api key which is provided by courier" name="api_key" @if ($movex) value="{{ $movex['api_key'] }}" @endif required data-validation-required-message="Please enter api key">
								</div>
							</div>

						@elseif ($courier['id'] == 20)

							<div class="form-group">
								<label class="col-lg-3 pt-15 control-label"><b>Title of Account : <span style="color:red">*</span></b></label>
								<div class="col-lg-9">
									<input type="text" class="form-control" maxLength="30" placeholder="Enter your Title of Account" name="account_title"  @if ($bykea) value="{{ $bykea['account_title'] }}" @endif required data-validation-required-message="Please enter your title of account">
									<span class="text-muted">Note : This is going to be display on your shipment's address / sticker label only in unity-retail.</span>
								</div>
							</div>

							<div class="form-group">
								<label class="col-lg-3 pt-15 control-label"><b>Username : <span style="color:red">*</span></b></label>
								<div class="col-lg-9">
									<input type="text" class="form-control" maxLength="50" id="username" placeholder="Enter your username which is provided by courier" name="username" @if ($bykea) value="{{ $bykea['username'] }}" @endif required data-validation-required-message="Please enter username">
								</div>
							</div>

							<div class="form-group">
								<label class="col-lg-3 pt-15 control-label"><b>Password : <span style="color:red">*</span></b></label>
								<div class="col-lg-9">
									<input type="password" class="form-control" maxLength="50" id="password" placeholder="Enter your password which is provided by courier" name="password" @if ($bykea) value="{{ $bykea['password'] }}" @endif required data-validation-required-message="Please enter password">
								</div>
							</div>

						@elseif ($courier['id'] == 21)

							<div class="form-group">
								<label class="col-lg-3 pt-15 control-label"><b>Title of Account : <span style="color:red">*</span></b></label>
								<div class="col-lg-9">
									<input type="text" class="form-control" maxLength="30" placeholder="Enter your Title of Account" name="account_title"  @if ($carson) value="{{ $carson['account_title'] }}" @endif required data-validation-required-message="Please enter your title of account">
									<span class="text-muted">Note : This is going to be display on your shipment's address / sticker label only in unity-retail.</span>
								</div>
							</div>

							<div class="form-group">
								<label class="col-lg-3 pt-15 control-label"><b>Email : <span style="color:red">*</span></b></label>
								<div class="col-lg-9">
									<input type="text" class="form-control" maxLength="50" id="email" placeholder="Enter your email which is provided by courier" name="email" @if ($carson) value="{{ $carson['email'] }}" @endif required data-validation-required-message="Please enter email">
								</div>
							</div>

							<div class="form-group">
								<label class="col-lg-3 pt-15 control-label"><b>Password : <span style="color:red">*</span></b></label>
								<div class="col-lg-9">
									<input type="password" class="form-control" maxLength="50" id="password" placeholder="Enter your password which is provided by courier" name="password" @if ($carson) value="{{ $carson['password'] }}" @endif required data-validation-required-message="Please enter password">
								</div>
							</div>

							<div class="form-group">
								<label class="col-lg-3 pt-15 control-label"><b>HUB : <span style="color:red">*</span></b></label>
								<div class="col-lg-9">
									<input type="text" class="form-control" maxLength="50" id="hub" placeholder="Enter your hub which is provided by courier" name="hub" @if ($carson) value="{{ $carson['hub'] }}" @endif required data-validation-required-message="Please enter hub">
								</div>
							</div>

							<div class="form-group">
								<label class="col-lg-3 pt-15 control-label"><b>Merchant Name : <span style="color:red">*</span></b></label>
								<div class="col-lg-9">
									<input type="text" class="form-control" maxLength="50" id="merchant_name" placeholder="Enter your merchant name which is provided by courier" name="merchant_name" @if ($carson) value="{{ $carson['merchant_name'] }}" @endif required data-validation-required-message="Please enter merchant name">
								</div>
							</div>

							<div class="form-group">
								<label class="col-lg-3 pt-15 control-label"><b>Merchant Code : <span style="color:red">*</span></b></label>
								<div class="col-lg-9">
									<input type="text" class="form-control" maxLength="50" id="merchant_code" placeholder="Enter your merchant code which is provided by courier" name="merchant_code" @if ($carson) value="{{ $carson['merchant_code'] }}" @endif required data-validation-required-message="Please enter merchant code">
								</div>
							</div>

						@elseif ($courier['id'] == 22)

							<div class="form-group">
								<label class="col-lg-3 pt-15 control-label"><b>Title of Account : <span style="color:red">*</span></b></label>
								<div class="col-lg-9">
									<input type="text" class="form-control" maxLength="30" placeholder="Enter your Title of Account" name="account_title"  @if ($delybell) value="{{ $delybell['account_title'] }}" @endif required data-validation-required-message="Please enter your title of account">
									<span class="text-muted">Note : This is going to be display on your shipment's address / sticker label only in unity-retail.</span>
								</div>
							</div>

							<div class="form-group">
								<label class="col-lg-3 pt-15 control-label"><b>API Token : <span style="color:red">*</span></b></label>
								<div class="col-lg-9">
									<input type="text" class="form-control" maxLength="50" id="api_token" placeholder="Enter your api token which is provided by courier" name="api_token" @if ($delybell) value="{{ $delybell['api_token'] }}" @endif required data-validation-required-message="Please enter api token">
								</div>
							</div>

						@elseif ($courier['id'] == 23)

							<div class="form-group">
								<label class="col-lg-3 pt-15 control-label"><b>Title of Account : <span style="color:red">*</span></b></label>
								<div class="col-lg-9">
									<input type="text" class="form-control" maxLength="30" placeholder="Enter your Title of Account" name="account_title"  @if ($quiqup) value="{{ $quiqup['account_title'] }}" @endif required data-validation-required-message="Please enter your title of account">
									<span class="text-muted">Note : This is going to be display on your shipment's address / sticker label only in unity-retail.</span>
								</div>
							</div>

							<div class="form-group">
								<label class="col-lg-3 pt-15 control-label"><b>App Key : <span style="color:red">*</span></b></label>
								<div class="col-lg-9">
									<input type="text" class="form-control" maxLength="50" id="app_key" placeholder="Enter your app key which is provided by courier" name="app_key" @if ($quiqup) value="{{ $quiqup['app_key'] }}" @endif required data-validation-required-message="Please enter app key">
								</div>
							</div>

							<div class="form-group">
								<label class="col-lg-3 pt-15 control-label"><b>App Secret : <span style="color:red">*</span></b></label>
								<div class="col-lg-9">
									<input type="text" class="form-control" maxLength="50" id="app_secret" placeholder="Enter your app secret which is provided by courier" name="app_secret" @if ($quiqup) value="{{ $quiqup['app_secret'] }}" @endif required data-validation-required-message="Please enter app secret">
								</div>
							</div>
						
						
						@elseif ($courier['id'] == 24)
							
								
								<div class="form-group">
									
									<label class="col-lg-3 pt-15 control-label"><b>Client ID : <span style="color:red">*</span></b></label>
									<div class="col-lg-9">
										<input type="text" class="form-control" name="client_id" id="client_id" placeholder="Enter client id which is provided by courier" @if ($pandago) value="{{ $pandago['client_id'] }}" @endif required data-validation-required-message="Please enter client id"  >
									</div>
								</div>

								<div class="form-group">
									
									<label class="col-lg-3 pt-15 control-label"><b>Key ID : <span style="color:red">*</span></b></label>
									<div class="col-lg-9">
										<input type="text" class="form-control" name="key_id" id="key_id" placeholder="Enter key id which is provided by courier" @if ($pandago) value="{{ $pandago['key_id'] }}" @endif required data-validation-required-message="Please enter key id"  >
									</div>
								</div>
	
								

								<div class="form-group">
									
									<label class="col-lg-3 pt-15 control-label"><b>Scope : <span style="color:red">*</span></b></label>
									<div class="col-lg-9">
										<input type="text" class="form-control" name="scope" id="scope" placeholder="Enter scope which is provided by courier" @if ($pandago) value="{{ $pandago['scope'] }}" @endif required data-validation-required-message="Please enter scope"  >
									</div>
								</div>
	
								<div class="form-group">
									
									<label class="col-lg-3 pt-15 control-label"><b>Account Title : <span style="color:red">*</span></b></label>
									<div class="col-lg-9">
										<input type="text" class="form-control" name="account_title" id="account_title" placeholder="Enter account_title" @if ($pandago) value="{{ $pandago['account_title'] }}" @endif required data-validation-required-message="Please enter account title"  >
									</div>
								</div>
	

							@if($pandago)	
							</fieldset>

							
							<fieldset @if(!$pandago->public_key && !$pandago->private_key) style="border-color: red" @endif id="pandago_key_section">
								<legend class="text-semibold" @if(!$pandago->public_key && !$pandago->private_key) style="border-color: red" @endif><i class="icon-traffic-lights position-left"></i>KeyPairs</legend>
							
								<div class="panel-body">
									<div class="tabbable">
										<ul class="nav nav-tabs bg-slate nav-tabs-component nav-justified">
											<li class="active"><a href="#solid-rounded-justified-tab1" data-toggle="tab">Download</a></li>
											<li><a href="#solid-rounded-justified-tab2" data-toggle="tab">Upload</a></li>
										</ul>

										<div class="tab-content">
											<div class="tab-pane active" id="solid-rounded-justified-tab1">
												<div class="form-group" id="gen_key_div">
													
													<label class="col-lg-2"><b>@if($pandago->public_key && $pandago->private_key) Re-Generate Keys @else Generate Keys @endif</b></label>
													<div class="col-lg-10">
														
														<button type="button" id="generate_key_modal" data-toggle="modal" data-target="#key_gen_confirm_modal" class="btn btn-primary"><i class="icon-file-plus2 position-left"></i>@if($pandago->public_key && $pandago->private_key) Re-Generate @else Generate @endif </button>
													</div>
												</div>
												<div class="form-group">
													<label class="col-lg-2"><b>Public Key</b></label>
													<div class="col-lg-10">
														<a @if($pandago && $pandago->public_key) href="{{route('download_files','pandago_public_key')}}" @endif id="down_pub" target="_blank" class="btn btn-primary @if(!$pandago || !$pandago->public_key) disabled @endif">Download</a>
													</div>
												</div>
													
												<div class="form-group">
													<label class="col-lg-2"><b>Private Key</b></label>
													<div class="col-lg-10">
														<a @if($pandago && $pandago->private_key) href="{{route('download_files','pandago_private_key')}}" @endif id="down_pri" target="_blank" class="btn btn-primary @if(!$pandago || !$pandago->private_key) disabled @endif">Download</a>
													</div>
												</div>
											</div>

											<div class="tab-pane" id="solid-rounded-justified-tab2">
												<div class="form-group">
													<label class="col-lg-2"><b>Public Key</b></label>
													<div class="col-lg-10">
														<input type="file" class="file-input" id="a" data-show-upload="false" data-show-preview="false" data-show-remove="false" name="upload_public_key" id="public_key">
														<span class="help-block">allowed ext(.txt, .pub)</span>
													</div>
												</div>
					
												<div class="form-group">
													<label class="col-lg-2"><b>Private Key</b></label>
													<div class="col-lg-10">
														<input type="file" class="file-input" id="b" data-show-upload="false" data-show-preview="false" data-show-remove="false" name="upload_private_key" id="private_key">
														<span class="help-block">allowed ext(.txt, .pem)</span>
													</div>
												</div>
											</div>

											
										</div>
									</div>
								</div>
								
							@endif
								
							
						@elseif ($courier['id'] == 25)

							<div class="form-group">
								<label class="col-lg-3 pt-15 control-label"><b>Api Key : <span style="color:red">*</span></b></label>
								<div class="col-lg-9">
									<input type="text" class="form-control" maxLength="50" id="api_key" placeholder="Enter your api key which is provided by courier" name="api_key" @if ($amazon_shipping) value="{{ $amazon_shipping['api_key'] }}" @endif required data-validation-required-message="Please enter api key">
								</div>
							</div>

							<div class="form-group">
								<label class="col-lg-3 pt-15 control-label"><b>Title of Account : <span style="color:red">*</span></b></label>
								<div class="col-lg-9">
									<input type="text" class="form-control" maxLength="30" placeholder="Enter your Title of Account" name="account_title"  @if ($amazon_shipping) value="{{ $amazon_shipping['account_title'] }}" @endif required data-validation-required-message="Please enter your title of account">
									<span class="text-muted">Note : This is going to be display on your shipment's address / sticker label only in unity-retail.</span>
								</div>
							</div>


						@elseif ($courier['id'] == 26)

							<div class="form-group">
								<label class="col-lg-3 pt-15 control-label"><b>Title of Account : <span style="color:red">*</span></b></label>
								<div class="col-lg-9">
									<input type="text" class="form-control" maxLength="30" placeholder="Enter your Title of Account" name="account_title"  @if ($aramex) value="{{ $aramex['account_title'] }}" @endif required data-validation-required-message="Please enter your title of account">
									<span class="text-muted">Note : This is going to be display on your shipment's address / sticker label only in unity-retail.</span>
								</div>
							</div>

							<div class="form-group">
								<label class="col-lg-3 pt-15 control-label"><b>Account Entity : <span style="color:red">*</span></b></label>
								<div class="col-lg-9">
									<input type="text" class="form-control" maxLength="50" id="account_entity" placeholder="Enter account entity which is provided by courier" name="account_entity" @if ($aramex) value="{{ $aramex['account_entity'] }}" @endif required data-validation-required-message="Please enter account entity">
								</div>
							</div>

							<div class="form-group">
								<label class="col-lg-3 pt-15 control-label"><b>Account Country Code : <span style="color:red">*</span></b></label>
								<div class="col-lg-9">
									<input type="text" class="form-control" maxLength="50" id="account_country_code" placeholder="Enter account country code which is provided by courier" name="account_country_code" @if ($aramex) value="{{ $aramex['account_country_code'] }}" @endif required data-validation-required-message="Please enter country code">
								</div>
							</div>

							<div class="form-group">
								<label class="col-lg-3 pt-15 control-label"><b>User Name : <span style="color:red">*</span></b></label>
								<div class="col-lg-9">
									<input type="text" class="form-control" maxLength="50" id="user_name" placeholder="Enter user name which is provided by courier" name="user_name" @if ($aramex) value="{{ $aramex['user_name'] }}" @endif required data-validation-required-message="Please enter user name">
								</div>
							</div>
							

							<div class="form-group">
								<label class="col-lg-3 pt-15 control-label"><b>Password : <span style="color:red">*</span></b></label>
								<div class="col-lg-9">
									<input type="password" class="form-control" maxLength="50" id="password" placeholder="Enter your password which is provided by courier" name="password" @if ($aramex) value="{{ $aramex['password'] }}" @endif required data-validation-required-message="Please enter password">
								</div>
							</div>

							<div class="form-group">
								<label class="col-lg-3 pt-15 control-label"><b>Account Number : <span style="color:red">*</span></b></label>
								<div class="col-lg-9">
									<input type="text" class="form-control" maxLength="50" id="account_number" placeholder="Enter your account number which is provided by courier" name="account_number" @if ($aramex) value="{{ $aramex['account_number'] }}" @endif required data-validation-required-message="Please enter account number">
								</div>
							</div>
							<div class="form-group">
								<label class="col-lg-3 pt-15 control-label"><b>Account Pin : <span style="color:red">*</span></b></label>
								<div class="col-lg-9">
									<input type="text" class="form-control" maxLength="50" id="account_pin" placeholder="Enter your account pin which is provided by courier" name="account_pin" @if ($aramex) value="{{ $aramex['account_pin'] }}" @endif required data-validation-required-message="Please enter account pin">
								</div>
							</div>
						@elseif ($courier['id'] == 27)

							<div class="form-group">
								<label class="col-lg-3 pt-15 control-label"><b>Account ID : <span style="color:red">*</span></b></label>
								<div class="col-lg-9">
									<input type="text" class="form-control" maxLength="50" id="account_id" placeholder="Enter your account id which is provided by courier" name="account_id" @if ($forrun) value="{{ $forrun['account_id'] }}" @endif required data-validation-required-message="Please enter account id">
								</div>
							</div>

							<div class="form-group">
								<label class="col-lg-3 pt-15 control-label"><b>Api Token : <span style="color:red">*</span></b></label>
								<div class="col-lg-9">
									<input type="text" class="form-control" maxLength="150" id="api_token" placeholder="Enter your api token which is provided by courier" name="api_token" @if ($forrun) value="{{ $forrun['api_token'] }}" @endif required data-validation-required-message="Please enter api token">
								</div>
							</div>
							

							<div class="form-group">
								<label class="col-lg-3 pt-15 control-label"><b>Title of Account : <span style="color:red">*</span></b></label>
								<div class="col-lg-9">
									<input type="text" class="form-control" maxLength="30" placeholder="Enter your Title of Account" name="account_title"  @if ($forrun) value="{{ $forrun['account_title'] }}" @endif required data-validation-required-message="Please enter your title of account">
									<span class="text-muted">Note : This is going to be display on your shipment's address / sticker label only in unity-retail.</span>
								</div>
							</div>
						
						@elseif ($courier['id'] == 28)

						<div class="form-group">
							<!-- <label class="col-lg-3 pt-15 control-label"><b>Authorization : <span style="color:red">*</span></b></label>
							<div class="col-lg-9"> -->
								<input type="hidden" class="form-control" maxLength="50" id="authorization" placeholder="Enter your account id which is provided by courier" name="authorization" @if (env('POSTEX_AUTH')) value="{{ env('POSTEX_AUTH') }}" @endif required data-validation-required-message="Please enter authorization id">
							<!-- </div> -->
						</div>

						<div class="form-group">
							<label class="col-lg-3 pt-15 control-label"><b>Api Token : <span style="color:red">*</span></b></label>
							<div class="col-lg-9">
								<input type="text" class="form-control" maxLength="150" id="token" placeholder="Enter your api token which is provided by courier" name="token" @if ($postex) value="{{ $postex['token'] }}" @endif required data-validation-required-message="Please enter api token">
							</div>
						</div>


						<div class="form-group">
							<label class="col-lg-3 pt-15 control-label"><b>Title of Account : <span style="color:red">*</span></b></label>
							<div class="col-lg-9">
								<input type="text" class="form-control" maxLength="30" placeholder="Enter your Title of Account" name="account_title"  @if ($postex) value="{{ $postex['account_title'] }}" @endif required data-validation-required-message="Please enter your title of account">
								<span class="text-muted">Note : This is going to be display on your shipment's address / sticker label only in unity-retail.</span>
							</div>
						</div>

						@elseif ($courier['id'] == 29)

							<div class="form-group">
								<label class="col-lg-3 pt-15 control-label"><b>Username : <span style="color:red">*</span></b></label>
								<div class="col-lg-9">
									<input type="text" class="form-control" maxLength="50" id="username" placeholder="Enter your account id which is provided by courier" name="username" @if ($stallion) value="{{ $stallion['username'] }}" @endif required data-validation-required-message="Please enter username">
								</div>
							</div>
							<div class="form-group">
								<label class="col-lg-3 pt-15 control-label"><b>Password : <span style="color:red">*</span></b></label>
								<div class="col-lg-9">
									<input type="text" class="form-control" maxLength="150" id="password" placeholder="Enter your password which is provided by courier" name="password" @if ($stallion) value="{{ $stallion['password'] }}" @endif required data-validation-required-message="Please enter password">
								</div>
							</div>
							<div class="form-group">
								<label class="col-lg-3 pt-15 control-label"><b>Title of Account : </b></label>
								<div class="col-lg-9">
									<input type="text" class="form-control" maxLength="30" placeholder="Enter your Title of Account" name="account_title"  @if ($stallion) value="{{ $stallion['account_title'] }}" @endif required data-validation-required-message="Please enter your title of account">
								</div>
							</div>

							@elseif ($courier['id'] == 30)

							<div class="form-group">
								<label class="col-lg-3 pt-15 control-label"><b>Account Number : <span style="color:red">*</span></b></label>
								<div class="col-lg-9">
									<input type="text" class="form-control" maxLength="50" id="account_no" placeholder="Enter your account number which is provided by courier" name="account_no" @if ($time_express) value="{{ $time_express['account_no'] }}" @endif required data-validation-required-message="Please enter account number">
								</div>
							</div>

							<div class="form-group">
								<label class="col-lg-3 pt-15 control-label"><b>Title of Account : <span style="color:red">*</span></b></label>
								<div class="col-lg-9">
									<input type="text" class="form-control" maxLength="30" placeholder="Enter your Title of Account" name="account_title"  @if ($time_express) value="{{ $time_express['account_title'] }}" @endif required data-validation-required-message="Please enter your title of account">
									<span class="text-muted">Note : This is going to be display on your shipment's address / sticker label only in unity-retail.</span>
								</div>
							</div>


						@elseif ($courier['id'] == 31)

							<div class="form-group">
								<label class="col-lg-3 pt-15 control-label"><b>Title of Account : <span style="color:red">*</span></b></label>
								<div class="col-lg-9">
									<input type="text" class="form-control" maxLength="30" placeholder="Enter your Title of Account" name="account_title"  @if ($insta) value="{{ $insta['account_title'] }}" @endif required data-validation-required-message="Please enter your title of account">
									<span class="text-muted">Note : This is going to be display on your shipment's address / sticker label only in unity-retail.</span>
								</div>
							</div>

							<div class="form-group">
								<label class="col-lg-3 pt-15 control-label"><b>API Key : <span style="color:red">*</span></b></label>
								<div class="col-lg-9">
									<input type="text" class="form-control" maxLength="250" id="api_key" placeholder="Enter your api key which is provided by courier" name="api_key" @if ($insta) value="{{ $insta['api_key'] }}" @endif required data-validation-required-message="Please enter api key">
								</div>
							</div>


						@elseif ($courier['id'] == 33)

							<div class="form-group">
								<label class="col-lg-3 pt-15 control-label"><b>Title of Account : <span style="color:red">*</span></b></label>
								<div class="col-lg-9">
									<input type="text" class="form-control" maxLength="30" placeholder="Enter your Title of Account" name="account_title"  @if ($tcs_uae) value="{{ $tcs_uae['account_title'] }}" @endif required data-validation-required-message="Please enter your title of account">
									<span class="text-muted">Note : This is going to be display on your shipment's address / sticker label only in unity-retail.</span>
								</div>
							</div>

							<div class="form-group">
								<label class="col-lg-3 pt-15 control-label"><b>User Id : <span style="color:red">*</span></b></label>
								<div class="col-lg-9">
									<input type="text" class="form-control" maxLength="250" id="user_id" placeholder="Enter your user id which is provided by courier" name="user_id" @if ($tcs_uae) value="{{ $tcs_uae['user_id'] }}" @endif required data-validation-required-message="Please enter user id">
								</div>
							</div>

							<div class="form-group">
								<label class="col-lg-3 pt-15 control-label"><b>API Key : <span style="color:red">*</span></b></label>
								<div class="col-lg-9">
									<input type="text" class="form-control" maxLength="250" id="api_key" placeholder="Enter your api key which is provided by courier" name="api_key" @if ($tcs_uae) value="{{ $tcs_uae['api_key'] }}" @endif required data-validation-required-message="Please enter api key">
								</div>
							</div>

							<div class="form-group">
								<label class="col-lg-3 pt-15 control-label"><b>Account No : <span style="color:red">*</span></b></label>
								<div class="col-lg-9">
									<input type="text" class="form-control" maxLength="250" id="account_number" placeholder="Enter your account number which is provided by courier" name="account_number" @if ($tcs_uae) value="{{ $tcs_uae['account_number'] }}" @endif required data-validation-required-message="Please enter account number">
								</div>
							</div>

							@elseif ($courier['id'] == 34)


							<div class="form-group">
								<label class="col-lg-3 pt-15 control-label"><b>User : <span style="color:red">*</span></b></label>
								<div class="col-lg-9">
									<input type="text" class="form-control" maxLength="250" id="user" placeholder="Enter your user  which is provided by courier" name="user" @if ($daewoo) value="{{ $daewoo['user'] }}" @endif required data-validation-required-message="Please enter user ">
								</div>
							</div>

							<div class="form-group">
								<label class="col-lg-3 pt-15 control-label"><b>Password : <span style="color:red">*</span></b></label>
								<div class="col-lg-9">
									<input type="text" class="form-control" maxLength="250" id="password" placeholder="Enter your password which is provided by courier" name="password" @if ($daewoo) value="{{ $daewoo['password'] }}" @endif required data-validation-required-message="Please enter password">
								</div>
							</div>

							<div class="form-group">
								<label class="col-lg-3 pt-15 control-label"><b>API Key : <span style="color:red">*</span></b></label>
								<div class="col-lg-9">
									<input type="text" class="form-control" maxLength="250" id="api_key" placeholder="Enter your api key which is provided by courier" name="api_key" @if ($daewoo) value="{{ $daewoo['api_key'] }}" @endif required data-validation-required-message="Please enter api key">
								</div>
							</div>

							

						

						@elseif ($courier['id'] == 35)

							<div class="form-group">
								<label class="col-lg-3 pt-15 control-label"><b>Title of Account : <span style="color:red">*</span></b></label>
								<div class="col-lg-9">
									<input type="text" class="form-control" maxLength="30" placeholder="Enter your Title of Account" name="account_title"  @if ($lcs_merchant) value="{{ $lcs_merchant['account_title'] }}" @endif required data-validation-required-message="Please enter your title of account">
									<span class="text-muted">Note : This is going to be display on your shipment's address / sticker label only in unity-retail.</span>
								</div>
							</div>

							<div class="form-group">
								<label class="col-lg-3 pt-15 control-label"><b>API Key : <span style="color:red">*</span></b></label>
								<div class="col-lg-9">
									<input type="text" class="form-control" maxLength="60" id="api_key" placeholder="Enter your api key which is provided by courier" name="api_key" @if ($lcs_merchant) value="{{ $lcs_merchant['api_key'] }}" @endif required data-validation-required-message="Please enter api key">
								</div>
							</div>

							<div class="form-group">
								<label class="col-lg-3 pt-15 control-label"><b>API Password : <span style="color:red">*</span></b></label>
								<div class="col-lg-9">
									<input type="password" class="form-control" maxLength="60" id="api_password" placeholder="Enter your api password which is provided by courier" name="api_password" @if ($lcs_merchant) value="{{ $lcs_merchant['api_password'] }}" @endif required data-validation-required-message="Please enter api password">
								</div>
							</div>

							<div class="form-group">
								<label class="col-lg-3 pt-15 control-label"><b>Operation Person Email : </b></label>
								<div class="col-lg-9">
									<input type="email" class="form-control" maxLength="40" id="email" placeholder="Enter your email" name="email" @if ($lcs_merchant) value="{{ $lcs_merchant['email'] }}" @endif >
									<span class="text-muted">Note : Courier will send return and cancellation emails on this email.</span>
								</div>
							</div>

							@elseif ($courier['id'] == 36)

							<input type="hidden" name= "default_picker_address_id" @if ($fly_courier) value="{{ $fly_courier['default_picker_address_id'] }}" @endif >

							<div class="form-group">
								<label class="col-lg-3 pt-15 control-label"><b>Title of Account : <span style="color:red">*</span></b></label>
								<div class="col-lg-9">
									<input type="text" class="form-control" maxLength="30" placeholder="Enter your Title of Account" name="account_title"  @if ($fly_courier) value="{{ $fly_courier['account_title'] }}" @endif required data-validation-required-message="Please enter your title of account">
									<span class="text-muted">Note : This is going to be display on your shipment's address / sticker label only in unity-retail.</span>
								</div>
							</div>

							<div class="form-group">
								<label class="col-lg-3 pt-15 control-label"><b>API Key : <span style="color:red">*</span></b></label>
								<div class="col-lg-9">
									<input type="text" class="form-control" maxLength="150" id="api_key" placeholder="Enter your api key which is provided by courier" name="api_key" @if ($fly_courier) value="{{ $fly_courier['api_key'] }}" @endif required data-validation-required-message="Please enter api key">
								</div>
							</div>

							@elseif ($courier['id'] == 37)

							<input type="hidden" name= "default_picker_address_id" @if ($smsa) value="{{ $smsa['default_picker_address_id'] }}" @endif >

							<div class="form-group">
								<label class="col-lg-3 pt-15 control-label"><b>Title of Account : <span style="color:red">*</span></b></label>
								<div class="col-lg-9">
									<input type="text" class="form-control" maxLength="30" placeholder="Enter your Title of Account" name="account_title"  @if ($smsa) value="{{ $smsa['account_title'] }}" @endif required data-validation-required-message="Please enter your title of account">
									<span class="text-muted">Note : This is going to be display on your shipment's address / sticker label only in unity-retail.</span>
								</div>
							</div>

							<div class="form-group">
								<label class="col-lg-3 pt-15 control-label"><b>API Key : <span style="color:red">*</span></b></label>
								<div class="col-lg-9">
									<input type="text" class="form-control" maxLength="150" id="pass_key" placeholder="Enter your pass key which is provided by courier" name="pass_key" @if ($smsa) value="{{ $smsa['pass_key'] }}" @endif required data-validation-required-message="Please enter pass key">
								</div>
							</div>

							@elseif ($courier['id'] == 38)

							<div class="form-group">
								<!-- <label class="col-lg-3 pt-15 control-label"><b>Authorization : <span style="color:red">*</span></b></label>
								<div class="col-lg-9"> -->
									<input type="hidden" class="form-control" maxLength="50" id="authorization" placeholder="Enter your account id which is provided by courier" name="authorization" @if (env('POSTEX_AUTH')) value="{{ env('POSTEX_AUTH') }}" @endif required data-validation-required-message="Please enter authorization id">
								<!-- </div> -->
							</div>

							<div class="form-group">
								<label class="col-lg-3 pt-15 control-label"><b>Api Token : <span style="color:red">*</span></b></label>
								<div class="col-lg-9">
									<input type="text" class="form-control" maxLength="150" id="token" placeholder="Enter your api token which is provided by courier" name="token" @if ($postex) value="{{ $postex['token'] }}" @endif required data-validation-required-message="Please enter api token">
								</div>
							</div>


							<div class="form-group">
								<label class="col-lg-3 pt-15 control-label"><b>Title of Account : <span style="color:red">*</span></b></label>
								<div class="col-lg-9">
									<input type="text" class="form-control" maxLength="30" placeholder="Enter your Title of Account" name="account_title"  @if ($postex) value="{{ $postex['account_title'] }}" @endif required data-validation-required-message="Please enter your title of account">
									<span class="text-muted">Note : This is going to be display on your shipment's address / sticker label only in unity-retail.</span>
								</div>
							</div>

							@elseif ($courier['id'] == 39)

                                <div class="form-group">
                                    <label class="col-lg-3 pt-15 control-label"><b>Title of Account : <span style="color:red">*</span></b></label>
                                    <div class="col-lg-9">
                                        <input type="text" class="form-control" maxLength="30" placeholder="Enter your Title of Account" name="account_title"  @if ($tqs) value="{{ $tqs['account_title'] }}" @endif required data-validation-required-message="Please enter your title of account">
                                        <span class="text-muted">Note : This is going to be display on your shipment's address / sticker label only in unity-retail.</span>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="col-lg-3 pt-15 control-label"><b>Client Code : <span style="color:red">*</span></b></label>
                                    <div class="col-lg-9">
                                        <input type="text" class="form-control" maxLength="250" id="client_code" placeholder="Enter your client code which is provided by courier" name="client_code" @if ($tqs) value="{{ $tqs['client_code'] }}" @endif required data-validation-required-message="Please enter client code">
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="col-lg-3 pt-15 control-label"><b>Profile ID : <span style="color:red">*</span></b></label>
                                    <div class="col-lg-9">
                                        <input type="text" class="form-control" maxLength="250" id="profile_id" placeholder="Enter your Profile ID which is provided by courier" name="profile_id" @if ($tqs) value="{{ $tqs['profile_id'] }}" @endif required data-validation-required-message="Please enter profile id">
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="col-lg-3 pt-15 control-label"><b>Authorization (Auth) Key : <span style="color:red">*</span></b></label>
                                    <div class="col-lg-9">
                                        <input type="text" class="form-control" maxLength="250" id="auth_key" placeholder="Enter your auth key which is provided by courier" name="auth_key" @if ($tqs) value="{{ $tqs['auth_key'] }}" @endif required data-validation-required-message="Please enter authorization key">
                                    </div>
                                </div>


							@elseif ($courier['id'] == 40)
							
								<div class="form-group">
                                    <label class="col-lg-3 pt-15 control-label"><b>Title of Account : <span style="color:red">*</span></b></label>
                                    <div class="col-lg-9">
                                        <input type="text" class="form-control" maxLength="30" placeholder="Enter your Title of Account" name="account_title"  @if ($dex) value="{{ $dex['account_title'] }}" @endif required data-validation-required-message="Please enter your title of account">
                                        <span class="text-muted">Note : This is going to be display on your shipment's address / sticker label only in unity-retail.</span>
                                    </div>
                                </div>
								<div class="form-group">
                                    <label class="col-lg-3 pt-15 control-label"><b>External Seller Id : <span style="color:red">*</span></b></label>
                                    <div class="col-lg-9">
                                        <input type="text" class="form-control" maxLength="250" id="external_seller_id" placeholder="Enter your external seller id" name="external_seller_id" @if ($dex) value="{{ $dex['external_seller_id'] }}" @endif required data-validation-required-message="Please enter external seller id">
									</div>
								</div>
	
							@elseif ($courier['id'] == 41)
								
								<div class="form-group">
                                    <label class="col-lg-3 pt-15 control-label"><b>Title of Account : <span style="color:red">*</span></b></label>
                                    <div class="col-lg-9">
                                        <input type="text" class="form-control" maxLength="30" placeholder="Enter your Title of Account" name="account_title"  @if ($city_mail) value="{{ $city_mail['account_title'] }}" @endif required data-validation-required-message="Please enter your title of account">
                                        <span class="text-muted">Note : This is going to be display on your shipment's address / sticker label only in unity-retail.</span>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="col-lg-3 pt-15 control-label"><b>Customer Account Number : <span style="color:red">*</span></b></label>
                                    <div class="col-lg-9">
                                        <input type="text" class="form-control" maxLength="250" id="customer_acno" placeholder="Enter customer account number provided by courier" name="customer_acno" @if ($city_mail) value="{{ $city_mail['customer_acno'] }}" @endif required data-validation-required-message="Please enter customer account no">
                                    </div>
                                </div>
	
							@elseif ($courier['id'] == 42)
								
								<div class="form-group">
                                    <label class="col-lg-3 pt-15 control-label"><b>Title of Account : <span style="color:red">*</span></b></label>
                                    <div class="col-lg-9">
                                        <input type="text" class="form-control" maxLength="30" placeholder="Enter your Title of Account" name="account_title"  @if ($jld) value="{{ $jld['account_title'] }}" @endif required data-validation-required-message="Please enter your title of account">
                                        <span class="text-muted">Note : This is going to be display on your shipment's address / sticker label only in unity-retail.</span>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="col-lg-3 pt-15 control-label"><b>API Token : <span style="color:red">*</span></b></label>
                                    <div class="col-lg-9">
                                        <input type="text" class="form-control" maxLength="250" id="api_token" placeholder="Enter API token provided by courier" name="api_token" @if ($jld) value="{{ $jld['api_token'] }}" @endif required data-validation-required-message="Please enter api token">
                                    </div>
                                </div>
							
							@elseif ($courier['id'] == 43)
								<div class="form-group">
									<label class="col-lg-3 pt-15 control-label"><b>Title of Account : <span style="color:red">*</span></b></label>
									<div class="col-lg-9">
										<input type="text" class="form-control" maxLength="30" placeholder="Enter your Title of Account" name="account_title"  @if ($moveo) value="{{ $moveo['account_title'] }}" @endif required data-validation-required-message="Please enter your title of account">
										<span class="text-muted">Note : This is going to be display on your shipment's address / sticker label only in unity-retail.</span>
									</div>
								</div>

								<div class="form-group">
									<label class="col-lg-3 pt-15 control-label"><b>API Token : <span style="color:red">*</span></b></label>
									<div class="col-lg-9">
										<input type="text" class="form-control" maxLength="250" id="api_token" placeholder="Enter API token provided by courier" name="api_token" @if ($moveo) value="{{ $moveo['api_token'] }}" @endif required data-validation-required-message="Please enter api token">
									</div>
								</div>

							@endif

                    </fieldset>

					@if ($courier['id'] == 17)
					<fieldset>
                        <legend class="text-semibold"><i class="icon-traffic-lights position-left"></i>Payment Info Option Setting</legend>

						@if ($payment_info_options)
									
							@foreach ($payment_info_options as $item)
								<div class="form-group">
									<label class="col-lg-3 "><b>{{$item->name}} :</b></label>
									<div class="col-lg-3 ">
										<input type="checkbox" class="switchery" name="payment_info_option_enable[{{$item->id}}]" <?php  echo (in_array($item->id, $selected_payment_info_options) ? "checked" : "") ?> value=1>    
									</div>
								</div>
							@endforeach
						
						@endif

						@if ($seller_payment_info_options)
								<div class="form-group">
									<label class="col-lg-3 "><b>Default Payment Info Option :</b></label>
									<div class="col-lg-3">
										<select name="default_payment_info_option" id="default_payment_info_option" class="select">
											@foreach($seller_payment_info_options as $data)
												@if(in_array($data->paymentInfoOption->id,$selected_payment_info_options))
													<option @if ($data->default == 1) selected @endif value="{{ $data->payment_info_options_id }}">{{ $data->paymentInfoOption->name }}</option>
												@endif
											@endforeach
										</select>
									</div>
									{{-- <span class="text-muted">Note: Make sure to select the enable service</span> --}}
								</div>
						@endif
					</fieldset>
						
					@endif

		
					@if($mps == 1)
						<fieldset>
							<legend class="text-semibold"><i class="icon-traffic-lights position-left"></i>Enable Multi-Package Shipping</legend>

							<div class="form-group">
								<label class="col-lg-3 pt-5  control-label"><b>Disable - Enable:</b></label>
								<div class="col-lg-9">
									<input type="checkbox" value=1 class="switchery" name="mps_seller" id="mps_seller"  @if ($mps_seller == 1) checked @endif>
									<br><span class="text-muted">Note : This feature is not available for automated shipment booking.</span>
								</div>
							</div>
						</fieldset>
					@endif
					<fieldset>
                        <legend class="text-semibold"><i class="icon-traffic-lights position-left"></i>Status</legend>

						<div class="form-group">
							<label class="col-lg-3 pt-5  control-label"><b>Disable - Enable:</b></label>
							<div class="col-lg-9">
							@if ($courier['id'] == 24)
								<input type="checkbox" value=1 class="switchery" name="status" value="1" id="courier_status_checkbox"  @if (!empty($courier_status) && $courier_status['status'] == 1) checked @endif>
							@else
							<input type="checkbox" value=1 class="switchery-primary" name="status" value="1" id="courier_status_checkbox"  @if (!empty($courier_status) && $courier_status['status'] == 1) checked @endif>
							@endif

							@if ($courier['id'] == 40)
							<br><br>
							<span class="text-muted">Note : In order to use DEX please choose "Fulfilment order id" in <b>Shipping Settings</b> inside the section "Use Following as Order Id".</span>
							@endif
							</div>
						</div>
					</fieldset>

					@if ($courier['id'] == 24)


					
					
					<fieldset>
                        <legend class="text-semibold"><i class="icon-traffic-lights position-left"></i>Order Status / Tag Confguration</legend>

						<div class="form-group">
							<label class="col-lg-3 pt-5  control-label"><b>Mark cancelled order as pending:</b></label>
							<div class="col-lg-9" style="width:8%">
								<input type="checkbox" value=1 class="switchery" name="mark_as_return" value="1" id="mark_as_return" @if (!empty($mark_as_return) && $mark_as_return == 1) checked @endif>
							</div>
							<label class="col-lg-6 pt-5  control-label"><b> Apply "Ondemand Failed" tag to orders that have received rejected bookings/cancelled shipments</b></label>
							<div >
								<input type="checkbox" value=1 class="switchery" name="on_demand_failed" value="1" id="on_demand_failed" @if (!empty($on_demand_failed) && $on_demand_failed == 1) checked @endif>
							</div>
						</div>
					</fieldset>
					<fieldset>
                        <legend class="text-semibold"><i class="icon-traffic-lights position-left"></i>Cut-off timings</legend>
						<div class="form-group">
								<label class="col-lg-3 pt-15 control-label"><b>Cut of Time (Hour) : <span style="color:red">*</span></b></label>
								<div class="col-lg-4">
									<input type="text" class="form-control" maxLength="50" id="cut_off_time" placeholder="Enter your location cutt off time in hour , e.g 3" name="cutofftimings" value=@if (!empty($cutofftimings)) {{$cutofftimings->value}} @endif >
								</div>
							</div>
					<table id="firstTable" class="table table-hover">
							<thead>
								<tr>
									<th>S. NO.</th>
									<th>Pick up Location</th>
									<th>Operating Hours</th>
									<th>Cut of TIme</th>
						
								</tr>
							</thead>
							<tbody>
							@foreach($seller_locations as $key => $value)
									<tr>
										<td>{{ $seller_locations_counter++}}</td>

										<td><a href="/seller/inventory/seller_locations/{{$value->id}}">  [{{ $value->location_name }}] - {{ $value->address }} </a></td>
										<td>{{ $value->start_time }} - {{ $value->end_time }}</td>
										<td>{{(isset($cutofftimings->value)) ? $cutofftimings->value : 0}}</td>
										
									</tr>
							</tbody>
							@endforeach

						</table>
						</fieldset>
						@endif

                </div>

						

				@if(isset($courier_status) && $courier_status->is_universal == 2)
					
				@else
					<div class="col-md-3">
						<fieldset>
							
							<legend class="text-semibold"><i class="icon-stairs position-left"></i> Services</legend>
							
							<div class="form-group text-center">
								@if($courier['id'] == 1)
									<img style="max-height:100px; max-width:100%; padding:10px" src="/img/custom_couriers/{{ $courier['image'] }}" alt="">
								@else
									<img style="max-height:100px; max-width:100%; padding:10px" src="/img/thumb-img/{{ $courier['image'] }}" alt="">
								@endif
							</div>

							@forelse ($services as $service)
								<div class="form-group">
									<label class="col-lg-6  text-right"><b>{{$service->name}} :</b></label>
									<div class="col-lg-6 ">
										<input type="checkbox" class="switchery" name="service_enable[{{$service->id}}]" <?php  echo (in_array($service->id, $seller_services) ? "checked" : "") ?> value=1>    
									</div>
								</div>
							@empty
								<div class="form-group text-center">
									<label class="col-lg-12"><b>No Service Found</b></label>
								</div>
							@endforelse
							<br>

							
							@if ($services)
							<legend class="text-semibold"><i class="icon-stairs position-left"></i> Default Service</legend>
								<div class="form-group text-center">
									<div style="text-align:center; margin: auto; padding-right:30px; padding-left:30px">
										<select name="default_service" id="default_service" class="select">
											@foreach($services as $service)
												<option @if ($default_service == $service->id) selected @endif value="{{ $service->id }}">{{ $service->name }}</option>
											@endforeach
										</select>
									</div>
									<span class="text-muted">Note: Make sure to select the enable service</span>
								</div>
							@endif

							

						</fieldset>
					</div>
				@endif
                {{ csrf_field() }}
            </div>

            <div class="text-right">
                <button type="submit" id="main_form_submit" class="btn btn-primary">Submit </button>
            </div>
        </div>
    </div>
</form>
<!-- /2 columns form -->



<div id="key_gen_confirm_modal" class="modal fade">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header bg-danger" style="">
				<button type="button" class="close" data-dismiss="modal">&times;</button>
				<h6 class="modal-title">Confirmation</h6>
			</div>
			
	  

			<div class="modal-body">
				Are you sure you want to Generate a new Key Pair?
				
				
			</div>

			<div class="modal-footer">
				<button type="button" class="btn" data-dismiss="modal">Cancel</button>

				<button type="button" id="generate_key" class="btn btn-link" style="border: hidden"><span id="yes_text">Yes</span><span id="spinner_btn" hidden><i class="icon-spinner2 spinner"></i></span></button>
			</div>
		</div>
	</div>
</div>



<!-- Danger modal -->
<div id="billing_profile_warning" class="modal fade" data-backdrop="false" hidden>
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger">
                
                <h6 class="modal-title">Alert</h6>
            </div>

            <div class="modal-body">
                
                <p>Please <a href="/seller/settings/billing-profile">complete your billing profile</a> to be eligible to ship with Unity Retail universal accounts.</p>

            </div>

            <div class="modal-footer">
                <button type="button" class="btn btn-link" id="cls">Close</button>
                
            </div>
        </div>
    </div>
</div>
<!-- /default modal -->

<script>

	$('#generate_key').click(function(){
		$(this).prop('disabled',true);
		$('#yes_text').prop('hidden',true);
		$('#spinner_btn').prop('hidden',false);
		$.ajax({
			url: '/seller/generate-pandago-keys',
			data:{
				"_token": "{{ csrf_token() }}",
			},
			type: 'post',
			success: function(response) {
				console.log(response.error);
				if(response.error == 'false'){
					
					$('#down_pri').removeClass('disabled');
					$('#down_pri').prop('href',"{{route('download_files','pandago_private_key')}}");

					$('#down_pub').removeClass('disabled');
					$('#down_pub').prop('href',"{{route('download_files','pandago_public_key')}}");

					$('#key_gen_confirm_modal').modal('hide');
					notify('Key Pair Generated Successfully',' ','icon-spinner9 spinner','bg-success',false);
					setTimeout(function() {
						location.reload();
					}, 800);
					
				}
			}

		});
	});

		var modalTemplate = '<div class="modal-dialog modal-lg" role="document">\n' +
        '  <div class="modal-content">\n' +
        '    <div class="modal-header">\n' +
        '      <div class="kv-zoom-actions btn-group">{toggleheader}{fullscreen}{borderless}{close}</div>\n' +
        '      <h6 class="modal-title">{heading} <small><span class="kv-zoom-title"></span></small></h6>\n' +
        '    </div>\n' +
        '    <div class="modal-body">\n' +
        '      <div class="floating-buttons btn-group"></div>\n' +
        '      <div class="kv-zoom-body file-zoom-content"></div>\n' + '{prev} {next}\n' +
        '    </div>\n' +
        '  </div>\n' +
        '</div>\n';

		var previewZoomButtonClasses = {
        toggleheader: 'btn btn-default btn-icon btn-xs btn-header-toggle',
        fullscreen: 'btn btn-default btn-icon btn-xs',
        borderless: 'btn btn-default btn-icon btn-xs',
        close: 'btn btn-default btn-icon btn-xs'
    };

    // Icons inside zoom modal classes
    var previewZoomButtonIcons = {
        prev: '<i class="icon-arrow-left32"></i>',
        next: '<i class="icon-arrow-right32"></i>',
        toggleheader: '<i class="icon-menu-open"></i>',
        fullscreen: '<i class="icon-screen-full"></i>',
        borderless: '<i class="icon-alignment-unalign"></i>',
        close: '<i class="icon-cross3"></i>'
    };

    // File actions
    var fileActionSettings = {
        zoomClass: 'btn btn-link btn-xs btn-icon',
        zoomIcon: '<i class="icon-zoomin3"></i>',
        dragClass: 'btn btn-link btn-xs btn-icon',
        dragIcon: '<i class="icon-three-bars"></i>',
        removeClass: 'btn btn-link btn-icon btn-xs',
        removeIcon: '<i class="icon-trash"></i>',
        indicatorNew: '<i class="icon-file-plus text-slate"></i>',
        indicatorSuccess: '<i class="icon-checkmark3 file-icon-large text-success"></i>',
        indicatorError: '<i class="icon-cross2 text-danger"></i>',
        indicatorLoading: '<i class="icon-spinner2 spinner text-muted"></i>'
    };

	var a = $('#a').fileinput({
        browseLabel: 'Browse',
        browseIcon: '<i class="icon-file-plus"></i>',
        uploadIcon: '<i class="icon-file-upload2"></i>',
        layoutTemplates: {
            icon: '<i class="icon-file-check"></i>',
            modal: modalTemplate
        },
        initialCaption: "No file selected",
		allowedFileExtensions: ["pub", "txt"],
        previewZoomButtonClasses: previewZoomButtonClasses,
        previewZoomButtonIcons: previewZoomButtonIcons,
        fileActionSettings: fileActionSettings
    });

	var a = $('#b').fileinput({
        browseLabel: 'Browse',
        browseIcon: '<i class="icon-file-plus"></i>',
        uploadIcon: '<i class="icon-file-upload2"></i>',
        layoutTemplates: {
            icon: '<i class="icon-file-check"></i>',
            modal: modalTemplate
        },
        initialCaption: "No file selected",
		allowedFileExtensions: ["pem", "txt"],
        previewZoomButtonClasses: previewZoomButtonClasses,
        previewZoomButtonIcons: previewZoomButtonIcons,
        fileActionSettings: fileActionSettings
    });

	if (Array.prototype.forEach) {
		var elems = Array.prototype.slice.call(document.querySelectorAll('.switchery'));
		elems.forEach(function(html) {
			var switchery = new Switchery(html);
		});
	}
	else {
		var elems = document.querySelectorAll('.switchery');
		for (var i = 0; i < elems.length; i++) {
			var switchery = new Switchery(elems[i]);
		}
	}

	@if ($courier['id'] != 24)
	// Colored switches
	var primary = document.querySelector('.switchery-primary');
	var switchery = new Switchery(primary, { color: '#2196F3' });
	@endif


	$(".styled, .multiselect-container input").uniform({
        radioClass: 'choice'
	});

	

	$(".file-styled").uniform({
        fileButtonHtml: '<i class="icon-file-plus"></i>'
    });
	
	$('#prefix_enable').on('change',function(){
		if ($(this).is(':checked')) {
			console.log('checked');
			$('#random_generated').show('slow');
        } else {
			$('#random_generated').hide('slow');
			console.log('un checked');
		}

	});

	@if (isset($courier_status->is_universal) && in_array($courier_status->is_universal, [1,2]))
		$("#account_details").attr('disabled','disabled');
		$("#account_details").children().prop('disabled',true);

		$('#pandago_key_section').attr('disabled','disabled');
	@endif

	$('#cls').click(function(){
	
		$('#billing_profile_warning').modal('hide');
	});
	
	$("#account_detail_checkbox").change(function() {
		
		if(this.checked) {
			
			
			@if (isset($courier_status->is_universal) && $courier_status->is_universal != 2 || !isset($courier_status->is_universal))
				@if(auth()->user()->entity_type != null && auth()->user()->bill_to != null && auth()->user()->cnic != null && auth()->user()->billing_address != null && auth()->user()->contact_person != null && auth()->user()->billing_email != null && auth()->user()->bank != null)
					@if(auth()->user()->entity_type == 'Individual' && auth()->user()->cnic_front != null && auth()->user()->cnic_back != null )
						$("#universal_account_enable").modal().show();
					@elseif(auth()->user()->entity_type != 'Individual' && auth()->user()->certificate != null)
						$("#universal_account_enable").modal().show();
					@else
						$("#account_detail_checkbox").attr('checked',false);
						$("#billing_profile_warning").modal().show();
						
					@endif
				@else
					$("#account_detail_checkbox").attr('checked',false);
					$("#billing_profile_warning").modal().show();
				@endif
			@endif
		} else {
			@if (isset($courier_status->is_universal) && $courier_status->is_universal == 2)
				$("#universal_account_disable").modal().show();
			@endif
		}
	});
	
	@if($courier['id'] == 1 && $self && !$self['tracking_number_option'])
		(function () {
			$('#random_generated').hide();
		})();
	@endif

</script>
@endsection