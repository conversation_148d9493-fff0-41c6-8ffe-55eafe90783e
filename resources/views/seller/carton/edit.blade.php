@extends('seller.admin_template') 
@section('pageTitle', 'Orders') 
@section('content')

<style>
    @media (min-width: 1025px) {
        .put-text-at-bottom {
            position: absolute;
            bottom : 0;
        }
    }
</style>



<script type="text/javascript" src="{{ asset ("assets/js/plugins/pickers/pickadate/picker.js") }}"></script>
<script type="text/javascript" src="{{ asset ("assets/js/plugins/pickers/pickadate/picker.date.js") }}"></script>
<script type="text/javascript" src="{{ asset ("assets/js/plugins/forms/styling/uniform.min.js") }}"></script>
<script type="text/javascript" src="{{ asset ("assets/js/plugins/buttons/ladda.min.js") }}"></script>
<script type="text/javascript" src="{{ asset ("assets/js/plugins/forms/inputs/touchspin.min.js") }}"></script>

<script src="{{ asset ("assets/js/pages/form_layouts.js ") }}"></script>


@include('seller.notification')

<form class="form-horizontal"  action="/seller/cartons" method="POST" enctype="multipart/form-data">

    <div class="panel panel-primary">
        <div class="panel-heading">
            <h5 class="panel-title">{{$page_title}}</h5>
        </div>
        

        {{-- Hidden Field For products --}}
        <input type="hidden" name="products" id="products">
        {{-- Hidden Field For products --}}

        <div class="panel-body">

            <div class="row">
                <div class="col-md-12">
                    <fieldset>

                        <legend class="text-semibold"><i class="icon-file-text position-left"></i>Carton Details</legend>

                            <div class="form-group">

                                <label class="col-lg-1 control-label"><b>Name <span style="color:red">*</span></b></label>
                                <div class="col-lg-5">
                                    <input type="text" class="form-control" maxLength="60" value="{{ $carton->name }}" placeholder="Enter Carton Name" name="name" required>
                                </div>

                                <label class="col-lg-1 control-label"><b>Barcode <span style="color:red">*</span></b></label>
                                <div class="col-lg-5">
                                    <input type="text" class="form-control" maxLength="60" value="{{ $carton->barcode }}" placeholder="Enter Carton Barcode" name="barcode" required>
                                </div>
                            </div>

                    </fieldset>
                </div>
            </div>
            <br>

            {{-- Carton Product Form --}}
            <div class="row" id="carton_product_div">
                <div class="col-md-12">
                    <fieldset>
                        <legend class="text-semibold"><i class="icon-cart position-left"></i>Carton Product Details</legend>
                            
                            <div class="row pl-10 pb-10">

                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label><b>Products <span style="color:red">*</span></b></label>
                                        <select name="product" id="product" data-placeholder="Select Product" class="select-product-remote-data">
                                        </select>
                                        <span id="total_count"></span>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="col-md-10">
                                        <div class="form-group">
                                            <label><b>Quantity <span style="color:red">*</span></b></label>
                                            <input type="text" value="1" id="quantity" class="touchspin-set-value" required>
                                        </div>
                                    </div>               

                                    <div class="col-md-2">
                                        <div class="form-group" style="padding-top:25px">
                                            <button type="button" title="ADD" onclick="addProduct()" class="btn bg-primary btn-icon btn-rounded"><i class="icon-plus3"></i></button>
                                        </div>
                                    </div>
                                </div>

                            </div>

                            <div class="row pl-20 pb-10">
                                <div class="table-responsive pre-scrollable">
                                    <table style="max-height:340px" class="table table-xxs" id='table_id'>
                                        <thead class="bg-primary">
                                            <tr>
                                                <th>#</th>
                                                <th>Product</th>
                                                <th>Qty</th>
                                                <th>Action</th>
                                            </tr>
                                        </thead>
                                        <tbody id="table-body">
                                            <tr>
                                                <td style="text-align:center" colspan="6">Not Available</td>
                                            </tr>
                                        </tbody>
                                        <tfoot class="bg-primary">
                                            <tr>
                                                <th>#</th>
                                                <th>Product</th>
                                                <th>Qty</th>
                                                <th>Action</th>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>
                            </div>

                    </fieldset>  
                </div>
                
            </div>

            {{ csrf_field() }}
            <div class="text-right">
                {{-- Hidden Submit button --}}
                <button type="submit" class="hide  submit-button-hidden"></button>
                <button type="button" class="btn bg-primary submit-button">Submit<i class="icon-arrow-right14 position-right"></i></button>
            </div>
        </div>
    </div>
</form>


<script>

    
    $(document).ready(function() {

        @foreach ($carton_products as $product)
            addProductMain('{{ $product->product_id }}', '{{ $product->product->SKU." | ".$product->product->barcode." | ".$product->product->product_name }}', '{{ $product->quantity }}', false);
        @endforeach
    });    



    $(".touchspin-set-value").TouchSpin({
        initval: 40
    });


    $(".select-product-remote-data").select2({
        ajax: {
        url: "/seller/products/stock-order/search",
            dataType: 'json',
            delay: 250,
            data: function (params) {
                return {
                    string: params.term, // search term
                };
            },
            processResults: function (data) {
                return {
                    results: $.map(data, function (item) {
                        return {
                            text: item.SKU + " | " + item.barcode + " | " + item.product_name,
                            id: item.id,
                            product_name: item.SKU + " | " + item.barcode + " | " + item.product_name,
                        }
                    })
                };
            },
            cache: true
        },
    });



    var items = [];
    var body = $('#table-body');
    var selected_sale = 0;
    var total = 0;
    var total_skus = 0;
    var total_items = 0;
    var qty_notify = 0;

    
    function addProduct() {

        let product = $('#product').val();
        let productName = $('#product').select2('data')[0].product_name;
        let quantity = $('#quantity').val();

        addProductMain(product, productName, quantity);
    }




    function addProductMain(product, productName, quantity, notify = true) {

        if (this.validate(product,quantity) == 1) {

            //////////// For First Item /////////////////
            if (items.length == 0) {
                body.empty();
            }

            index = this.findItem(product);
            if (index == -1) {

                /////////////// Adding Row in Array & Table /////////
                items.push({id : product, name : productName, quantity : quantity});

                this.addRow(product,items.length,productName,quantity);
                updateTotalCount(1, quantity);

                if (notify) {
                    this.notify('Success', 'Product is added.', 'icon-check', 'bg-success', 'hide');
                }


            } else {

                ////////////// Update value in Array ////////////
                items[index]['quantity'] = parseInt(items[index]['quantity']) + parseInt(quantity);

                ////////////// Update value on table //////////////
                $('#'+(items[index]['id'])+'quantity').text(items[index]['quantity']);

                updateTotalCount(0, quantity);
            }

        }
    }

    function addRow(id,no,name,quantity) {

        row = $('<tr class="item-row"></tr>');
            row.append($("<td id='"+id+"no'>"+no+"</td>")); 
            row.append($("<td id='"+id+"name'>"+name+"</td>")); 
            row.append($("<td id='"+id+"quantity'>"+quantity+"</td>")); 
            row.append($("<td><a data-id='"+id+"' class='deleteItem'><i style='color:red' class='icon-trash'></i></a></td>")); 
        body.append(row);
        
    }

    function findItem(product) {
        
        var found = items.find(function(element) { 
            return element.id == product;
        }); 

        return items.indexOf(found);
    }
    
    
    function validate(product,quantity) {

        if (!product) {
            
            this.notify('Error', 'Please Select a Product.', 'icon-blocked', 'bg-danger');
            return 0;
            
        } else if (!quantity || Number.isInteger(quantity) || quantity < 1) {
            if(qty_notify == 0){
                this.notify('Error', 'Please Enter a Valid Quantity.', 'icon-blocked', 'bg-danger',true,true);
                qty_notify++;
            }
            return 0;
            
        } else {
            return 1;
        }
    }


    ////////////// Remove an Item //////////////
    $('.table').on('click','.deleteItem',function(){
        var div = $(this);
        id = $(this).data("id");
        index = findItem(id);

        if (index != -1) {

            items.splice(index,1);
            div.closest('.item-row').hide('slow');

            notify('Success', 'Product is Remove from the list.', 'icon-trash', 'bg-success');

            if (items.length == 0) {
                row = $('<tr></tr>');
                    row.append($('<td style="text-align:center" colspan="6">Not Available</td>')); 
                body.append(row);
            }
        }
    });



    ////////////// Form Submit //////////////
    $('.submit-button').on('click',function(){

        if (items.length != 0) {
            $('#products').val(JSON.stringify(items));
            $('.submit-button-hidden').trigger('click');
            console.log(items);
        } else {
            notify('Error', 'Please add Product first.', 'icon-blocked', 'bg-danger',true,true);
        }
    });



    function updateTotalCount(unique_sku, quantity)
    {
        total_skus += unique_sku;
        total_items += parseInt(quantity);
        $('#total_count').html(`Total SKU(s) : <b>${total_skus}</b><br> Total Item(s) : <b>${total_items}</b>`);
    }

</script>


@endsection