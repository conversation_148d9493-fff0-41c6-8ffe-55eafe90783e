@extends('seller.admin_template')

@section('pageTitle', 'Orders')

@section('content')


    {{-- Scripts --}}
    <script type="text/javascript" src="{{ asset ("assets/js/plugins/tables/datatables/extensions/col_reorder.min.js") }}"></script>
	<script type="text/javascript" src="{{ asset ("assets/js/plugins/tables/datatables/extensions/select.min.js") }}"></script>
	<script type="text/javascript" src="{{ asset ("assets/js/plugins/tables/datatables/extensions/pdfmake/pdfmake.min.js") }}"></script>
	<script type="text/javascript" src="{{ asset ("assets/js/plugins/tables/datatables/extensions/pdfmake/vfs_fonts.min.js") }}"></script>

	<script src="{{ asset ("assets/js/pages/form_layouts.js ") }}"></script>
	<script type="text/javascript" src="{{ asset ("assets/js/plugins/pickers/daterangepicker.js") }}"></script>
    {{-- Scripts End --}}

    {{-- Custom CSS --}}
    <style>
        .error-row {
            background-color: #ffcdd2 !important; /* Very light red background */
        }

        .error-row:hover {
            background-color: #ffc3c9 !important; /* Slightly darker red on hover */
        }

        .error-row td {
            background-color: transparent !important; /* Ensure cells inherit the row background */
        }


        .warning-row {
            background-color: #fff7cd !important; /* Very light red background */
        }

        .warning-row:hover {
            background-color: #fef3b9 !important; /* Slightly darker red on hover */
        }

        .warning-row td {
            background-color: transparent !important; /* Ensure cells inherit the row background */
        }
    </style>




    {{-- Content --}}
    <div class="row">

        <div class="col-lg-4">
            <div style="background-color: #e7fcff" class="panel border-xlg border-info has-bg-image text-info"data-popup="popover" title="<b>Pending From Courier</b>" data-html="true" data-placement="bottom" data-trigger="hover" data-content="Total Cartons.">
                <div class="panel-body p-15">
                <h1 style="line-height: 0.90;" class="no-margin"><b> @formatInteger($summary['total']) </b><span style="font-size:14px"> Total Cartons</span></h1>
                </div>
            </div>
        </div>
        <div class="col-lg-4">
            <div style="background-color: #e7ffe9" class="panel border-xlg border-success has-bg-image text-success"  data-popup="popover" title="<b>Bank Reconciliation Pending</b>" data-html="true" data-placement="bottom" data-trigger="hover" data-content="Total Active Cartons.">
                <div class="panel-body p-15">
                    <h1 style="line-height: 0.90;" class="no-margin"><b> @formatInteger( $summary['active'] ) </b><span style="font-size:14px"> Active Cartons</span></h1>
                </div>
            </div>
        </div>
        <div class="col-lg-4">
            <div style="background-color: #ffe8e7" class="panel border-xlg border-danger has-bg-image text-danger"  data-popup="popover" title="<b>Bank Reconciliation Pending</b>" data-html="true" data-placement="bottom" data-trigger="hover" data-content="Total InActive Cartons.">
                <div class="panel-body p-15">
                    <h1 style="line-height: 0.90;" class="no-margin"><b> @formatInteger( $summary['inactive'] ) </b><span style="font-size:14px"> In-Active Cartons</span></h1>
                </div>
            </div>
        </div>

    </div>

    @include('seller.notification')


    <!-- Page length options -->
    <div class="panel panel-primary">
        <div class="panel-heading">
            <h5 class="panel-title"><i class="icon-dropbox"></i>&nbsp {{$page_title}}</h5>
            <div class="heading-elements visible-elements">

                <a class="btn btn-default" href="cartons/create"><i class="icon-dropbox"></i>&nbsp Create</a>

            </div>
        </div>

        <div class="panel-body">

            <table id="main-table" class="table">
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>Barccode</th>
                        <th>Total Products</th>
                        <th>Total Quantity</th>
                        <th>Status</th>
                        <th>Created At</th>
                        <th>Updated At</th>
                        <th>Actions</th>
                    </tr>
                </thead>
            </table>

        </div>
    </div>
    <!-- /page length options -->

    {{-- Content --}}





<script>

	$.extend( $.fn.dataTable.defaults, {
		dom: '<"datatable-header"fB><"datatable-scroll-wrap"rt><"datatable-footer"ip>',
		buttons: {
			buttons: [
				{extend: 'pageLength',className: 'btn bg-slate-600'},
				{
					extend: 'collection',
					text: 'Export',
					className: 'btn bg-indigo-400 btn-icon',
					buttons: [
						{extend: 'copyHtml5',text: '<i class="icon-copy3"></i>&nbsp &nbsp Copy ',className: 'btn btn-default',
							exportOptions: {
								columns: ':visible'
							}
						},
						{extend: 'csvHtml5',text: '<i class="icon-file-excel"></i>&nbsp &nbsp CSV ',className: 'btn btn-default',
							exportOptions: {
								columns: ':visible'
							}
						},
						{extend: 'pdfHtml5',text: '<i class="icon-printer2"></i>&nbsp &nbsp pdf ',className: 'btn btn-default',
							exportOptions: {
								columns: ':visible'
							}
						}
					]
				},
				{extend: 'colvis',columns: ':gt(1)',text: '<i class="icon-grid3"></i> <span class="caret"></span>',className: 'btn bg-indigo-400 btn-icon'}
			]
		},
		select: {
			style: 'os',
			selector: 'td:first-child'
		},
		colReorder: true,
		stateSave: true,
		scrollX: true,
		scrollY: '50vh',
		scrollCollapse: true,
		"deferLoading": true,
		"processing": true,
		"language": {"processing": '<i style="color:green;font-size:50px" class="icon-spinner4 spinner"></i>'}

	});


    var main_table = $('#main-table').DataTable( {
        lengthMenu: [
            [ 20, 30, 50, 100, 500, 2000, -1],
            [ '20 rows', '30 rows', '50 rows', '100 rows', '500 rows', '2000 rows', 'All rows (Not Recommended)']
        ],
        retrieve: true,
        "serverSide": true,
        "ajax": "/seller/cartons/all",
        columnDefs: [ {
            orderable: false,
            searchable: false,
            targets:   [7]
        }],
        columns: [
            
            { data: 'name', name: 'cartons.name', render: function(data, type, row) {
                    return ('<a target="_blank" href="cartons/'+row.id+'/edit">'+data+'</a>');
                }
            },
            { data: 'barcode', name: 'cartons.barcode' },
            { data: 'total_product', name: 'total_product' },
            { data: 'total_quantity', name: 'total_quantity' },
            { data: 'status', name: 'status', render: function(data, type, row) {
                    if(data == 1) {
                        return '<td><span class="label label-success">Active</span></td>'
                    } else if(data == 0) {
                        return '<td><span class="label label-danger">Inactive</span></td>'
                    }
                }
            },
            { data: 'created_at', name: 'created_at' },
            { data: 'updated_at', name: 'updated_at' },
            { render: function(data, type, row) {
                
                    if (row.status == 1) {
                        return '<a class="btn btn-danger btn-xs" onclick="changeStatus('+row.id+', 0)"><i class="icon-blocked"></i> InActive</a> <a class="btn btn-danger btn-xs" onclick="deleteCarton('+row.id+')"><i class="icon-trash"></i> Delete</a>';
                    } else {
                        return '<a class="btn btn-success btn-xs" onclick="changeStatus('+row.id+', 1)"><i class="icon-checkmark"></i> Active</a> <a class="btn btn-danger btn-xs" onclick="deleteCarton('+row.id+')"><i class="icon-trash"></i> Delete</a>';
                    }
                }
            }
        ],
        "order": [[5, 'desc']]
    });
    main_table.draw();


    /// For changing the status of a carton
    function changeStatus(carton_id, status)
    {
        if (!confirm('Are you sure you want to change the status of this carton ?')) {
            return;
        }

        $.ajax({
            url: '{{ route("cartons.toggle-status") }}',
            type: 'POST',
            data: {
                _token: '{{ csrf_token() }}',
                status: 0, // 0 for inactive, 1 for active
                carton_id: carton_id
            },
            success: function(response) {

                if (response.success) {

                    alert(response.message);
                    main_table.ajax.reload();

                } else {
                    alert('Error: ' + response.message);
                }
            },
            error: function(xhr) {
                alert('Error performing action. Please try again.');
            }
        });
    }


    function deleteCarton(carton_id)
    {
        if (!confirm('Are you sure you want to delete this carton?')) {
            return;
        }

        $.ajax({
            url: '{{ route("cartons.destroy", ":carton") }}'.replace(':carton', carton_id),
            type: 'DELETE',
            data: {
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                alert(response.message);
                main_table.ajax.reload();
            },
            error: function(xhr) {
                alert('Error performing action. Please try again.');
            }
        });
    }


</script>
@endsection