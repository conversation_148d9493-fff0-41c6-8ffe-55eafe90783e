<!DOCTYPE html>
<html lang="en">

<head>



	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<title>{{ $page_title ?? "Unity Dashboard" }}</title>

	<!-- Global stylesheets -->
	<link href="{{ asset ("assets/css/icons/icomoon/styles.css") }}" rel="stylesheet" type="text/css">
	<link href="{{ asset ("assets/css/bootstrap.css") }}" rel="stylesheet" type="text/css">
	<link href="{{ asset ("assets/css/core.css") }}" rel="stylesheet" type="text/css">
	<link href="{{ asset ("assets/css/components.css") }}" rel="stylesheet" type="text/css">
	<link href="{{ asset ("assets/css/colors.css") }}" rel="stylesheet" type="text/css">
	<link href="{{ asset ("/assets/css/extras/animate.min.css") }}" rel="stylesheet" type="text/css">
	<link rel="icon" href="{{ asset ("img/core-img/favicon.ico") }}" type="image/x-icon">
	<!-- /global stylesheets -->
	@if (isset($gtm_product_analysis) && $gtm_product_analysis != 0)
	<!-- Google Tag Manager -->
	<script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
	new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
	j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
	'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
	})(window,document,'script','dataLayer','GTM-K2GL24J');</script>
	<!-- End Google Tag Manager -->
	@endif


	<!-- Core JS files -->
	<script type="text/javascript" src="{{ asset ("assets/js/plugins/loaders/pace.min.js") }}"></script>
	<script type="text/javascript" src="{{ asset ("assets/js/core/libraries/jquery.min.js") }}"></script>
	<script type="text/javascript" src="{{ asset ("assets/js/core/libraries/bootstrap.min.js") }}"></script>
	<script type="text/javascript" src="{{ asset ("assets/js/plugins/loaders/blockui.min.js") }}"></script>
	<!-- /core JS files -->

	<!-- Theme JS files -->
	<script type="text/javascript" src="{{ asset ("assets/js/plugins/tables/datatables/datatables.min.js") }}"></script>
	<script type="text/javascript" src="{{ asset ("assets/js/plugins/tables/datatables/extensions/buttons.min.js") }}"></script>
	<script type="text/javascript" src="{{ asset ("assets/js/plugins/forms/selects/select2.min.js") }}"></script>


	<script type="text/javascript" src="{{ asset ("assets/js/plugins/forms/styling/uniform.min.js") }}"></script>
	<script type="text/javascript" src="{{ asset ("assets/js/plugins/ui/moment/moment.min.js") }}"></script>

	<script type="text/javascript" src="{{ asset ("assets/js/core/app.js") }}"></script>


	<script type="text/javascript" src="{{ asset ("assets/js/plugins/ui/ripple.min.js") }}"></script>
	<script type="text/javascript" src="{{ asset ("assets/js/plugins/notifications/pnotify.min.js") }}"></script>

	<!-- /theme JS files -->

</head>

<style>
	.panel-primary>.panel-heading , .btn-primary , .bg-primary {
		background-color: #8d86ce;
	}

	.low_credit{
        color: red;
        font-weight: 600;
    }

	/* Reusable class for danger panel with popover */
	.danger-panel-popover {
		background-color: #ffe8e7;
	}
	.search-bar-style{
		/* padding-left: 20px; */
		float: inherit;
		padding-top:4px;
		width: 100%;
	}
	.search-bar-style::placeholder{
		color: white
	}
	.form-nav-setting{
		width: 35%;
	}

	@media only screen and (max-width: 1024px) {
		.form-nav-setting{
			width: 20%;
		}
	}

	@media only screen and (max-width: 750px) {
		.form-nav-setting{
			width: 10%;
		}
	}

	@media only screen and (max-width: 450px) {
		.form-nav-setting{
			width: 100%;
		}
	}
</style>


<script>

    function notify(title, text, icon, color, hide, closer=false, sticker=false) {
        permanotice = new PNotify({
            title: title,
            text: text,
            icon: icon,
            addclass: color,
			hide: hide,
			buttons: {
				closer: closer,
				sticker: sticker
			}
        });
		return permanotice;
	}
	
	function removeNotify() {
		new PNotify.removeAll()
	}

    function showOverlay() {
        $('body').block({
            message: '<i style="font-size:100px;" class="icon-spinner4 spinner"></i>',
            overlayCSS: {
                backgroundColor: '#1B2024',
                opacity: 0.85,
                cursor: 'wait'
            },
            css: {
                border: 0,
                padding: 0,
                backgroundColor: 'none',
                color: '#fff'
            }
        });
    }

    function hideOverlay() {
        $('body').unblock();
    }    

	function formatRepo (repo) {
		if (repo.loading) {
			return repo.text;
		}

		if(val == 1){
			var $container = $(
				"<a href='/seller/order/"+repo.id+"' target='_blank' style='color:black;font-weight:300'>"+
				"<div class='select2-result-repository clearfix'> " +
				"<div class='select2-result-repository__avatar'><img src='/img/thumb-img/6.png' /></div>" +
				"<div class='select2-result-repository__meta'>" +
					"<div class='select2-result-repository__title'></div>" +
					"<div class='select2-result-repository__description'></div>" +
					"<div class='select2-result-repository__statistics'>" +
					"<div class='select2-result-repository__forks'><i class='icon-location3'></i> </div>" +
					"<div class='select2-result-repository__stargazers'><i class='fa fa-star'></i> </div>" +
					"<div class='select2-result-repository__watchers'><i class='icon-calendar'></i> </div>" +
					"</div>" +
				"</div>" +
				"</div>"+
				"</a>"
			);

			
			$container.find(".select2-result-repository__title").html('<span style="font-weight:bold;font-size:16px">'+repo.title+'</span>');
			$container.find(".select2-result-repository__description").text(repo.description);
			$container.find(".select2-result-repository__forks").append(repo.destination);
			$container.find(".select2-result-repository__stargazers").append(repo.status);
			$container.find(".select2-result-repository__watchers").append(repo.date);

		} else{
			var $container = $(
				"<a href='/seller/shipment/"+repo.id+"' target='_blank' style='color:black;font-weight:300'>"+
				"<div class='select2-result-repository clearfix'>" +
				"<div class='select2-result-repository__avatar'><img src='/img/thumb-img/4.png' /></div>" +
				"<div class='select2-result-repository__meta'>" +
					"<div class='select2-result-repository__title'></div>" +
					"<div class='select2-result-repository__description'></div>" +
					"<div class='select2-result-repository__statistics'>" +
					"<div class='select2-result-repository__forks'><i class='icon-cash3'></i> </div>" +
					"<div class='select2-result-repository__stargazers'><i class='fa fa-star'></i> </div>" +
					"<div class='select2-result-repository__watchers'><i class='icon-calendar'></i> </div>" +
					"</div>" +
				"</div>" +
				"</div>"
			);
			$container.find(".select2-result-repository__title").html('<span style="font-weight:bold;font-size:16px">'+repo.title+'</span>');
			$container.find(".select2-result-repository__description").text(repo.description);
			$container.find(".select2-result-repository__forks").append(repo.destination);
			$container.find(".select2-result-repository__stargazers").append(repo.status);
			$container.find(".select2-result-repository__watchers").append(repo.date);
		}

		

		return $container;
    }

    // Format selection
    function formatRepoSelection (repo) {
        return repo.full_name || repo.text;
    }

	var val = 1;

	function radioA(){
		val = 1;
		$('#sh_chk').prop('checked',false);
		$(".select2-search__field").val('');
		$(".select2-search__field").attr('placeholder','Search Unity...');
		$('.select2-results__options').empty();
		
		
	}

	function radioB(){
		val = 2;
		$('#or_chk').prop('checked',false);
		$(".select2-search__field").val('');
		$(".select2-search__field").attr('placeholder','Search Unity...');
		$('.select2-results__options').empty();
	}

	$(document).ready(function () {
		
        if (localStorage['mini_sidebar'] == 'true') {
            $("body").addClass("sidebar-xs");
        }

        $('.select-remote-data-header').select2().on('select2:open', () => {
			$(".select2-results:not(:has(input))").prepend('<span style="padding-left:15px"></span>Search Filters:&nbsp;&nbsp;&nbsp;<input type="radio" onclick="radioA();" id="or_chk" checked value="Order">&nbsp;Order&nbsp;&nbsp;&nbsp;<input type="radio" id="sh_chk" onclick="radioB();">&nbsp;Shipment<hr>');
		})
		
		$('.select-remote-data-header').select2().on('select2:open', () => {
			$(".select2-search__field").attr('placeholder','Search Unity...');
		});
		var filt = null;
		
		
		var ss = $(".select-remote-data-header").select2({
			placeholder:'Search Unity...',
			ajax: {
				url: "/seller/header-search",
				dataType: 'json',
				delay: 250,
				data: function (params) {
					return { 
						string: params.term,
						val:val, // search term
					};
				},
				processResults: function (data) {
					// parse the results into the format expected by Select2
					// since we are using custom formatting functions we do not need to
					// alter the remote JSON data, except to indicate that infinite
					// scrolling can be used
					

					return {
						results: data.items,
						
					};
				},
				cache: true
			},
			escapeMarkup: function (markup) { return markup; }, // let our custom formatter work
			minimumInputLength: 1,
			closeOnSelect: false,
			templateResult: formatRepo, // omitted for brevity, see the source of this page
			templateSelection: formatRepoSelection // omitted for brevity, see the source of this page
		});
		console.log($('.select2-selection__rendered').text());

		$(".select-remote-data-header").on('select2:close',function (e) {
			$('.select2-selection__rendered').text('Search Unity...');
		});
	});
	
    
</script>
@if (isset($hubspot) && $hubspot != false)
<!-- Start of HubSpot Embed Code --> 
<!-- <script type="text/javascript" id="hs-script-loader" async defer 
src="//js.hs-scripts.com/9260165.js"></script> -->
<!-- End of HubSpot Embed Code -->
@endif

<body>

	@if (isset($gtm_product_analysis) && $gtm_product_analysis != 0)
	<!-- Google Tag Manager (noscript) -->
	<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-K2GL24J"
	height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
	<!-- End Google Tag Manager (noscript) -->
	@endif
	<!-- Google Tag Manager (noscript) --> 
	<!-- <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-TC3SW49" height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>  -->
	<!-- End Google Tag Manager (noscript) -->

	<!-- Main navbar -->
	@include('seller.header')
	<!-- /main navbar -->


	<!-- Page container -->
	<div class="page-container">

		<!-- Page content -->
		<div class="page-content">

			<!-- Main sidebar -->
			@include('seller.sidebar')
			<!-- /main sidebar -->


			<!-- Main content -->
			<div class="content-wrapper">



				<!-- Content area -->
				<div class="content">


					@if ($wallet_enable)
						@include('seller.wallet_alert')
						@include('seller.wallet_popup')
					@endif

					 @yield('content')


					<!-- Footer -->
					@include('seller.footer')
					<!-- /footer -->

				</div>
				<!-- /content area -->

			</div>
			<!-- /main content -->

		</div>
		<!-- /page content -->

	</div>
	<!-- /page container -->

	<!-- Start of unityretail Zendesk Widget script -->
	{{-- <script id="ze-snippet" src="https://static.zdassets.com/ekr/snippet.js?key=18c14415-a46f-448c-b033-a81f8956436c"> </script> --}}
	<!-- End of unityretail Zendesk Widget script -->

</body>

</html>
