<?php 

namespace App\Service\Booking;

use App\Helpers\CheckBlacklistOrder;
use App\Http\Controllers\AutoShippedController;
use App\Models\AddOn;
use App\Models\AutoShippingLog;
use App\Models\Courier\City;
use App\Models\Courier\Courier;
use App\Models\Courier\CourierCity;
use App\Models\Courier\CustomCourier;
use App\Models\Courier\SellerCourier;
use App\Models\Courier\SellerCourierBlueEx;
use App\Models\Courier\SellerCourierBykea;
use App\Models\Courier\SellerCourierCallCourier;
use App\Models\Courier\SellerCourierCarson;
use App\Models\Courier\SellerCourierCityMail;
use App\Models\Courier\SellerCourierCourierX;
use App\Models\Courier\SellerCourierDaewoo;
use App\Models\Courier\SellerCourierDeliveryExpress;
use App\Models\Courier\SellerCourierDelybell;
use App\Models\Courier\SellerCourierDEX;
use App\Models\Courier\SellerCourierDHL;
use App\Models\Courier\SellerCourierFlyCourier;
use App\Models\Courier\SellerCourierForrun;
use App\Models\Courier\SellerCourierInsta;
use App\Models\Courier\SellerCourierLCS;
use App\Models\Courier\SellerCourierLCSMerchant;
use App\Models\Courier\SellerCourierLCSUAE;
use App\Models\Courier\SellerCourierMNP;
use App\Models\Courier\SellerCourierMoveX;
use App\Models\Courier\SellerCourierPandaGo;
use App\Models\Courier\SellerCourierPostEx;
use App\Models\Courier\SellerCourierPostExPartner;
use App\Models\Courier\SellerCourierQuiqup;
use App\Models\Courier\SellerCourierRider;
use App\Models\Courier\SellerCourierSmsa;
use App\Models\Courier\SellerCourierSwyft;
use App\Models\Courier\SellerCourierTCSNew;
use App\Models\Courier\SellerCourierTcsUae;
use App\Models\Courier\SellerCourierTimeExpress;
use App\Models\Courier\SellerCourierTraxNew;
use App\Models\CourierService;
use App\Models\FulfillmentOrder;
use App\Models\FulfillmentOrderItem;
use App\Models\LogEntry;
use App\Models\Order;
use App\Models\OrderComments;
use App\Models\OrderItem;
use App\Models\OrderTag;
use App\Models\PackageOverageTier;
use App\Models\PaymentInfoOption;
use App\Models\Seller;
use App\Models\Courier\SellerCourierJLD;
use App\Models\Courier\SellerCourierMoveo;
use App\Models\SellerCourierTQS;
use App\Models\SellerLocation;
use App\Models\SellerPackage;
use App\Models\SellerPaymentInfoOption;
use App\Models\SellerReturnAddressMapping;
use App\Models\Setting;
use App\Models\Shipment;
use App\Models\ShipmentQuotation;
use App\Models\Tag;
use App\Models\WalletTransaction;
use App\Service\CodeReuse\OrderBookingQueueTemp;
use App\Service\SellerCouriers\Stallion;
use App\Service\Validation\OrderValidationService;
use App\Traits\CallRequestTrait;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class BookingService
{
    use CallRequestTrait;

    protected $custom_status_code = 420;
    protected $custom_retry_status_code = 421;

    private
        $params,
        $process_name = 'Order Booking Process',
        $seller_id,

        $order_data,
        $booking_data,
        $auto_shipping_batch_log_data,
        $wallet_data,

        $order_added_in_queue = false,

        $destination_city_id,

        $free_shipment,
        $add_on_wallet,
        
        $tracking_number,
        $shipment_id,
        $extras;


    private $required_indexes = [
        'order_id',
        'courier_id',
        'fo_id',
        'booking_type',
        'location_id',
    ];

    public function __construct($params)
    {
        $this->params = $params;
        $this->params['log_message'] = config('enum.booking_trigger_types_reverse')[$this->params['booking_type']].' '.$this->process_name;
        $this->params['auto_shipping_batch_log_id'] = 0;
        $this->params['after_booking_tag'] = null;

        $this->booking_data['weight'] = 0;
        $this->booking_data['cod'] = 0;
        $this->booking_data['quantity'] = 0;
        $this->booking_data['description'] = '';
        $this->booking_data['seller_return_location_phone'] = Null;
        $this->booking_data['seller_return_location_address'] = Null;
        $this->booking_data['seller_return_location_city'] = Null;
        $this->booking_data['seller_return_location_id'] = Null;
        $this->booking_data['seller_return_location_city_code'] = Null;
            
    }

    public function execute()
    {
        try {
            return $this->callRequest('initiateSuccessProcess');

        } catch (Exception $e) {
            return $this->callRequest('initiateFailureProcess', [ 'code' => $e->getCode(), 'message' => $e->getMessage(), 'trace' => $e->getTraceAsString()]);
        }
    }




    /// ----- Initiate Main Process ----- \\\
    private function initiateSuccessProcess()
    {
        /// 1st     Validating parameters and checking and adding in queue so others wont be able to pick this
        $this->checkCancelledAutoShippingLog()->validateParameterData()->addOrderInQueue()->addOrderItemInQueue();
        /// 1st


        /// 2nd     Getting and validating order data for eligibility (also checking courier limit if eligible)
        $this->getOrderData()->checkWalletAmountForUniversal()->checkCourierLimit()->checkBlacklistCustomer()->orderDataValidation();
        /// 2nd


        /// 3rd     Getting all data which required by courier for booking
        $this->prepareOrderDataForBooking();
        /// 3rd


        /// 4th     Charging from wallet and sending request to courier
        $this->chargeWallet()->requestCourier();
        /// 4th


        /// 5th     if shipment created at courier then save data into database
        $this->postBooking();
        /// 5th


        /// In Both Process     Resetting the temporary data and handling success response
        return $this->removeOrderFromQueue()->removeOrderItemFromQueue()->updateAutoShippingLog()->addCommentAndHandleResponse(0, 'Shipment # '.$this->booking_data['tracking_number'].' Created');
        /// In Both Process
    }

    private function initiateFailureProcess($parameters)
    {
        /// 1st     Validating parameters and checking and adding in queue so others wont be able to pick this
        $this->addActivityLog($parameters['code'], $parameters['message'], $parameters['trace']);
        /// 1st


        /// 2nd     Refund wallet transaction if its charged
        $this->refundTransaction();
        /// 2nd


        /// In Both Process     Resetting the temporary data
        $this->removeOrderFromQueue()->removeOrderItemFromQueue()->updateAutoShippingLog($parameters['message']);
        /// In Both Process


        /// 3rd     changing the status code to the retry one on specific errors message if it isn't already
        $parameters['code'] = $this->retryOnSpecificError($parameters['message'], $parameters['code']);
        /// 3rd


        /// In Both Process     Resetting the temporary data and handling error response
        return $this->addCommentAndHandleResponse(1, 'Booking Failed | '.$parameters['message'], $parameters['code']);
        /// In Both Process
    }
    /// ----- Initiate Main Process ----- \\\





    /// ----- initiateSuccessProcess() child function ----- \\\

    /// 1st
    private function checkCancelledAutoShippingLog()
    {
        if ($this->params['auto_shipping_batch_log_id']) {
            $this->auto_shipping_batch_log_data = AutoShippingLog::find($this->params['auto_shipping_batch_log_id']);

            if ($this->auto_shipping_batch_log_data && $this->auto_shipping_batch_log_data->status == config('enum.auto_shipping_log_status')['CANCELLED']) {
                throw new Exception("Auto Shipping Process has been cancelled", $this->custom_status_code);
            }
        }

        return $this;
    }

    private function validateParameterData()
    {
        foreach ($this->required_indexes as $required_index) {

            throw_if(
                !exists_or_null($this->params, $required_index),
                Exception::class,
                ucwords( str_replace('_',' ',$required_index) ).' parameter in missing.'
            );
        }

        return $this;
    }

    private function addOrderInQueue()
    {
        throw_if( OrderBookingQueueTemp::existsInBookingQueue($this->params['fo_id']), Exception::class, "Order Booking is already in queue", $this->custom_retry_status_code );
        throw_if( !OrderBookingQueueTemp::createBookingQueue($this->params['fo_id'], 1), Exception::class, "Order Booking is already in queue", $this->custom_retry_status_code );

        $this->order_added_in_queue = true;
        return $this;
    }

    private function addOrderItemInQueue()
    {
        $updated_items = [];

        /// if order items id not present then getting from FO (if applicable) otherwise all pending order items
        if (!isset($this->params['order_item_ids']) || !$this->params['order_item_ids']) {

            if (isset($this->params['fo_id']) && $this->params['fo_id']) {
                $this->params['order_item_ids'] = FulfillmentOrderItem::where('fulfillment_order_id', $this->params['fo_id'])->pluck('order_item_id');
            } else {
                $this->params['order_item_ids'] = OrderItem::where('order_id', $this->params['order_id'])->where('status',config('enum.item_status')['PENDING'])->pluck('id');
            }
        }

        /// if still order item not found to process
        throw_if( !$this->params['order_item_ids'], Exception::class, "Bookable order items not found", $this->custom_status_code);

        /// updating order item to be picked for booking
        foreach ($this->params['order_item_ids'] as $order_item_id) {

            if (DB::table('order_items')->where('id', $order_item_id)->where('pick_for_booking', 0)->where('status',config('enum.item_status')['PENDING'])->update(['pick_for_booking' => 1])) {
                $updated_items[] = $order_item_id; 
            }
        }

        if (count($updated_items) == count($this->params['order_item_ids'])) {
            return true;
        } else {

            throw_if( !$updated_items, Exception::class, "Some order items are already in booking queue", $this->custom_status_code);
            DB::table('order_items')->whereIn('id', $updated_items)->update(['pick_for_booking' => 0]);
        }
    }
    /// 1st




    /// 2nd
    private function getOrderData()
    {
        throw_if(
            !$this->order_data = Order::whereId($this->params['order_id'])->first(),
            Exception::class,
            "Order not found | order id : ".$this->params['order_id']
        );

        $this->booking_data['order'] = $this->order_data;
        $this->order_data = $this->order_data->toArray();
        $this->seller_id = $this->order_data['seller_id'];
        $this->add_on_wallet = AddOn::wallet($this->seller_id);
        return $this;
    }



    private function checkWalletAmountForUniversal()
    {
        if(
            $this->add_on_wallet
            && SellerCourier::whereSellerId($this->seller_id)->whereCourierId($this->params['courier_id'])->value('is_universal') == 2
            && Courier::universalEnableCheck($this->params['courier_id']))
        {

            $seller_data = Seller::whereId($this->seller_id)->first(['wallet', 'non_cod_minimum']);
            $minimum_bal = ( $seller_data->non_cod_minimum == 0 ? 5000 : 1000 );

            throw_if(
                ($this->order_data['cod_payment'] == 0 || $this->order_data['grand_total'] == 0) && $seller_data->wallet < $minimum_bal,
                Exception::class,
                'Please maintain at least '.( $seller_data->non_cod_minimum == 0 ? "5,000" : "1,000" ).' rupees in your wallet to book a Non-COD shipment'
            );
        }

        return $this;
    }

    private function checkCourierLimit()
    {
        if (
            Setting::courierLimit($this->seller_id)
            && !$this->params['only_cn_assignment']
            && $this->params['booking_type'] != config('enum.booking_trigger_types')['Booking Form']
            && !SellerCourier::where('seller_id', $this->seller_id)->where('courier_id', $this->params['courier_id'])->where('max_booking', '>', DB::raw('current_booking'))->exists()
        ) {

            throw_if( $this->params['auto_shipping_batch_log_id'], Exception::class, "Courier limit is full", $this->custom_status_code );

            if ($default_courier = Setting::whereSellerId($this->seller_id)->where('key', config('enum.settings')['DEFAULTCOURIER'])->value('value')) {                
                $this->params['courier_id'] = $default_courier;

                if ($default_courier_service = Setting::whereSellerId($this->seller_id)->where('key', config('enum.settings')['DEFAULTCOURIERSERVICE'])->value('value')) {
                    $this->params['courier_service_value'] = CourierService::whereId($default_courier_service)->value('value');
                }

            } else {
                throw new Exception("Courier limit, Default Courier not exists", $this->custom_status_code);
            }
        }

        return $this;
    }

    private function checkBlacklistCustomer()
    {
        throw_if(
            $this->params['booking_type'] != config('enum.booking_trigger_types')['Booking Form'] && $this->order_data['customer_number'] && CheckBlacklistOrder::check($this->seller_id, $this->order_data['customer_number']),
            Exception::class,
            "Order Customer Number is in Exception List |-> Customer Number : ".$this->order_data['customer_number'],
            $this->custom_status_code
        );

        return $this;
    }

    private function orderDataValidation()
    {
        if (!$this->params['only_cn_assignment']) {

            $params = [
                'order' => $this->order_data,
                'order_item_ids' => $this->params['order_item_ids'],
                'courier_id' => $this->params['courier_id'],
                'booking_type' => $this->params['booking_type']
            ];
            $order_data_validation_service = new OrderValidationService($this->seller_id, $params);
            $response = $order_data_validation_service->execute();

            throw_if($response['error'], Exception::class, $response['message'], $response['code']);

            $this->destination_city_id = $response['data']['destination_city_id'];
            $this->booking_data['destination_city'] = $response['data']['destination_city_courier_data'];
            
        } else {
            $this->destination_city_id = City::where('name', $this->order_data['destination_city'])->where('country_code', $this->order_data['country'])->value('id');
        }
    }
    /// 2nd




    /// 3rd
    private function prepareOrderDataForBooking()
    {
        $this->getFOItemsIfExists()->setCalculatedData()->setLabelData()->checkStockAvailable()->setOriginData()->setReturnAddress()->setOtherData();

		// For DHL country code
		if (in_array($this->params['courier_id'], [17,26] )) {
            $this->booking_data['seller_city_code'] = [ $this->booking_data['seller_city_code'], $this->params['order_item_ids'] ];
		}

        $this->setPaymentInfo();
    }
    /// 3rd




    /// 4th
    private function chargeWallet()
    {
        if ($this->add_on_wallet) {

            if ( isset($this->params['quotation_id']) && $this->params['quotation_id'] && $this->params['quotation_id'] != "null" && $this->params['quotation_id'] != "undefined") {

                $this->booking_data['qoutation_id'] = $this->params['quotation_id'];
                $wallet_charges = 0;
                $courier_service = CourierService::where('courier_id', $this->params['courier_id'])->where('value', $this->params['courier_service_value'])->first();

                if ($courier_service) {
                    $quotation = ShipmentQuotation::where('courier_id',$this->params['courier_id'])->where('qoutation_id', $this->params['quotation_id'])->where('courier_service',$courier_service->id)->first();
                } else {
                    $quotation = ShipmentQuotation::where('courier_id',$this->params['courier_id'])->where('qoutation_id', $this->params['quotation_id'])->first();
                }

                /// If quotation not found
                throw_if(!$quotation, Exception::class, 'Invalid quotation');

                $wallet_charges += $quotation->total_seller_charges_with_tax;

                if (!$quotation->platform_charges) {

                    if (!DB::update('update seller_packages set free_shipment_count = free_shipment_count - 1 where seller_id = '.$this->seller_id.' and free_shipment_count > 0')) {
                        $package = SellerPackage::where("seller_id",$this->seller_id)->first(['package_id']);

                        /// If package not found
                        throw_if(!$package, Exception::class, 'Your account doesn\'t have any package , please contact support for this');

                        $platform_charges = PackageOverageTier::where("package_id",$package->package_id)->value('rate');

                        /// If package found but platform_charges not
                        throw_if(!$package, Exception::class, 'Platform charges not found , please contact support for this');

                        $wallet_charges += $platform_charges*1.13;

                    } else {
                        $this->free_shipment = true;
                    }
                }

                if ($wallet_charges) {

                    $this->wallet_data['wallet_charges'] = $wallet_charges;
                    $this->wallet_data['transaction'] = WalletTransaction::charged($wallet_charges, $this->params['order_id'], $this->seller_id);

                    /// If wallet transaction not successfully completed
                    throw_if($this->wallet_data['transaction']['error'], Exception::class, $this->wallet_data['transaction']['message']);
                }

            } else {

                $response = WalletTransaction::getTariffAndCharged(  
                    $this->seller_id,
                    $this->params['courier_id'],
                    $this->booking_data['origin'],
                    $this->order_data['destination_city'],
                    $this->booking_data['weight'],
                    $this->booking_data['cod'],
                    $this->params['courier_service_value'],
                    $this->params['order_id']
                );
    
                throw_if($response['error'], Exception::class, $response['error'], $this->custom_status_code);
        
                $this->wallet_data['transaction'] = $response['transaction'];
                $this->wallet_data['wallet_charges'] = $response['wallet_charges'];
                $this->booking_data['qoutation_id'] = $response['qoutation_id'];
                $this->free_shipment = $response['free_shipment'];
            }

        }

        return $this;
    }

    private function requestCourier()
    {
        if ($this->params['only_cn_assignment']) {
            $response['message'] = $this->params['tracking_number'];

        } else {

            switch ($this->params['courier_id']) {

                case 1:
                    $response = CustomCourier::generateTrackingNumber($this->seller_id, $this->params['order_id'], $this->order_data['marketplace_reference_id'], (isset($this->params['tracking_number']) ? $this->params['tracking_number'] : NULL) );
                    throw_if(Shipment::where('tracking_number',$response['message'])->where('seller_id',$this->seller_id)->exists(), Exception::class, 'Tracking Number already exists in your account');
                    break;

                case 4:
                    $response = SellerCourierMNP::shipped($this->booking_data);
                    break;

                case 5:
                    $response = SellerCourierLCS::shipped($this->booking_data);
                    break;

                case 7:
                    $response = SellerCourierCallCourier::shipped($this->booking_data);
                    break;

                case 8:
                    $response = SellerCourierTraxNew::shipped($this->booking_data);
                    break;

                case 9:
                    $response['message'] = exists_or_null($this->params, 'tracking_number', str_random(15));
                    throw_if(Shipment::where('tracking_number',$response['message'])->where('seller_id',$this->seller_id)->exists(), Exception::class, 'Tracking Number already exists in your account');
                    break;

                case 10:
                    $response = SellerCourierBlueEx::shipped($this->booking_data);
                    break;

                case 11:
                    $response = SellerCourierRider::shipped($this->booking_data, exists_or_null($this->params, 'area'));
                    break;

                case 12:
                    $response = SellerCourierDeliveryExpress::shipped($this->booking_data);
                    break;

                case 13:
                    $response = SellerCourierTCSNew::shipped($this->booking_data);
                    break;

                case 14:
                    $response = SellerCourierLCSUAE::shipped($this->booking_data);
                    break;

                case 15:
                    $response = SellerCourierSwyft::shipped($this->booking_data);
                    break;

                case $this->params['courier_id'] == 17 && $this->seller_id == 120:
                    $response = SellerCourierDHL::shippedOtherSellers($this->booking_data);
                    break;

                case $this->params['courier_id'] == 17 && $this->seller_id != 120:
                    $response = SellerCourierDHL::shipped($this->booking_data);
                    break;

                case 18:
                    $response = SellerCourierCourierX::shipped($this->booking_data);
                    break;

                case 19:
                    $response = SellerCourierMoveX::shipped($this->booking_data);
                    break;

                case 20:
                    $response = SellerCourierBykea::shipped($this->booking_data);
                    break;

                case 21:
                    $response = SellerCourierCarson::shipped($this->booking_data);
                    break;

                case 22:
                    $response = SellerCourierDelybell::shipped($this->booking_data);
                    break;

                case 23:
                    $response = SellerCourierQuiqup::shipped($this->booking_data);
                    break;

                case 24:
                    $response = SellerCourierPandaGo::shipped($this->booking_data);
                    break;

                case 27:
                    $response = SellerCourierForrun::shipped($this->booking_data);
                    break;

                case 28:
                    $response = SellerCourierPostEx::shipped($this->booking_data);
                    break;

                case 29:
                    $response = (new Stallion($this->seller_id))->booking($this->booking_data);
                    break;

                case 30:
                    $response = SellerCourierTimeExpress::shipped($this->booking_data);
                    break;

                case 31:
                    $response = SellerCourierInsta::shipped($this->booking_data);
                    break;

                case 33:
                    $response = SellerCourierTcsUae::shipped($this->booking_data);
                    break;

                case 34:
                    $response = SellerCourierDaewoo::shipped($this->booking_data);
                    break;

                case 35:
                    $response = SellerCourierLCSMerchant::shipped($this->booking_data);
                    break;
                
                case 36:
                    $response = SellerCourierFlyCourier::shipped($this->booking_data);
                    break;

                case 37:
                    $response = SellerCourierSmsa::shipped($this->booking_data);
                    break;

                case 38:
                    $response = SellerCourierPostExPartner::shipped($this->booking_data);
                    break;                
                
                case 39:
                    $response = SellerCourierTQS::shipped($this->booking_data);
                    break;
                
                case 40:
                    $response = SellerCourierDEX::shipped($this->booking_data);
                    break;
                
                case 41:
                    $response = SellerCourierCityMail::shipped($this->booking_data);
                    break;

                case 42:
                    $response = SellerCourierJLD::shipped($this->booking_data);
                    break;
                case 43:
                    $response = SellerCourierMoveo::shipped($this->booking_data);
                    break;
                
                default:
                    throw new Exception("Selected courier booking request not found", $this->custom_status_code);
            }

        }

        $this->saveBookingCallResponseData($response);
    }
    /// 4th




    /// 5th
    private function postBooking()
    {
        $this->applyTagIfNecessary()->chargedFbrFee()->applyAfterBookingTag()->createShipment()->addTrackingNumberOnTransaction();
    }
    /// 5th

    /// ----- initiateSuccessProcess() child function ----- \\\








    /// ----- initiateFailedProcess() child function ----- \\\

    /// 1st
    private function addActivityLog($code, $message, $trace)
    {
        if ($code != 420) {

            activity()
            ->causedBy($this->seller_id)
            ->withProperties(compact('message', 'trace'))
            ->log($this->params['log_message']);
        }
    }
    /// 1st


    /// 2nd
    private function refundTransaction()
    {
        if ($this->add_on_wallet && isset($this->wallet_data['transaction']['transaction_id'])) {
            WalletTransaction::refund($this->wallet_data['wallet_charges'], $this->params['order_id'], $this->seller_id);
        }

        //reversing used free shipment count on un-successful shipment
        if($this->free_shipment) {
            DB::update('update seller_packages set free_shipment_count = free_shipment_count + 1 where seller_id = '.$this->seller_id);
        }
    }
    /// 2nd


    /// 3rd
    private function retryOnSpecificError($code, $message)
    {
        if ($this->custom_retry_status_code != $code) {

            if (!$this->params['auto_shipping_batch_log_id']) {
                if (stripos($message,'Fail to save order!') !== false || stripos($message,'Cannot find table 0') !== false) {
                    return $this->custom_retry_status_code;
                }
            }
        }

        return $code;
    }
    /// 3rd

    /// ----- initiateFailedProcess() child function ----- \\\







    /// ----- Initiate In Both Process() child function ----- \\\
    private function removeOrderFromQueue()
    {
        if ($this->order_added_in_queue) {
            OrderBookingQueueTemp::deleteBookingQueue($this->params['fo_id']);
        }

        return $this;
    }

    private function removeOrderItemFromQueue()
    {
        if ($this->params['order_item_ids']) {
            DB::table('order_items')->whereIn('id', $this->params['order_item_ids'])->update(['pick_for_booking' => 0]);
        }

        return $this;
    }

    private function updateAutoShippingLog($message = 'Success')
    {
        if ($this->params['auto_shipping_batch_log_id']) {
            $logEntry = LogEntry::where('log_id', $this->params['auto_shipping_batch_log_id'])->where('order_id', $this->params['order_id'])->where('key', config('enum.log_entry_keys')['AUTOSHIPPING'])->first();

            if ($this->tracking_number == 1) {
                
                AutoShippingLog::whereId($this->params['auto_shipping_batch_log_id'])->increment('success');
                $logEntry->status = config('enum.log_entry_status')['COMPELETED'];
                $logEntry->shipment_id = $this->shipment_id;

            } else {

                AutoShippingLog::whereId($this->params['auto_shipping_batch_log_id'])->increment('failed');
                $logEntry->status = config('enum.log_entry_status')['FAILED'];
                $logEntry->shipment_id = 0;
            }

            $logEntry->message = $message;
            $logEntry->save();

            $logEntries = LogEntry::where('log_id', $this->params['auto_shipping_batch_log_id'])->where('key', config('enum.log_entry_keys')['AUTOSHIPPING'])->where('status', '!=', config('enum.log_entry_status')['PENDING'])->count();

            if ($logEntries == $this->auto_shipping_batch_log_data->total && $this->auto_shipping_batch_log_data->status == config('enum.auto_shipping_log_status')['RUNNING']) {
                $this->auto_shipping_batch_log_data->status = config('enum.auto_shipping_log_status')['COMPELETED'];
            }
        }

        return $this;
    }

    private function addCommentAndHandleResponse($error, $message, $code = 200)
    {
        $message .= ' | Trigger By : '.config('enum.booking_trigger_types_reverse')[$this->params['booking_type']];
        OrderComments::add($this->params['order_id'], $this->process_name, $message, ($error ? 'Failed' : 'Success'), Null);
        return ['error' => $error, 'message' => $message, 'data' => $this->shipment_id, 'retry' => ($code == $this->custom_retry_status_code ? true : false)];
    }
    /// ----- Initiate In Both Process() child function ----- \\\




















    /// ----- prepareOrderDataForBooking() child function ----- \\\
    private function getFOItemsIfExists()
    {
        $this->booking_data['shipper_order_id'] = $this->order_data['marketplace_reference_id'];


        
        
        
        if (isset($this->params['fo_id']) && $this->params['fo_id']) {
            
            $fulfillment_order = FulfillmentOrder::openFulfillmentOrder()->whereId($this->params['fo_id'])->first(['reference_id', 'shipping_fee']);
            
            throw_if(
                !$fulfillment_order,
                Exception::class,
                'Fulfillment order not found OR not in open state'
            );

            if($res = Setting::orderIDConfig($this->seller_id)){
            
                if($res == config('enum.settings')['ORDER_ID_CONF_OPTION_2']) {
                    $this->booking_data['shipper_order_id'] = $fulfillment_order->reference_id;
                }
            }


            /// Temporary CODE
            if (!$fulfillment_order->shipping_fee && $temp_shipping_fee = Order::whereId($this->params['order_id'])->where('shipping_fee','>',0)->where('shipping_fee_charged', 0)->value('shipping_fee')) {
                
                if (FulfillmentOrder::whereId($this->params['fo_id'])->where('shipping_fee',0)->where('status',0)->whereNull('shipment_id')->update(['shipping_fee' => $temp_shipping_fee ])) {

                    Order::whereId($this->params['order_id'])->where('shipping_fee', '>', '0')->where('shipping_fee_charged', 0)->update(['shipping_fee_charged' => $this->params['fo_id']]);                
                    $this->booking_data['shipping_fee'] = $temp_shipping_fee;

                } else {
                    $this->booking_data['shipping_fee'] = $fulfillment_order->shipping_fee;
                }

            } else {
                $this->booking_data['shipping_fee'] = $fulfillment_order->shipping_fee;
            }
            



            $this->params['order_item_ids'] = FulfillmentOrderItem::where('fulfillment_order_id', $this->params['fo_id'])->pluck('order_item_id');
        }

        return $this;
        
    }

    private function setCalculatedData()
    {
        $order_items = OrderItem::whereIn('id', $this->params['order_item_ids'])->where('status', '=', config('enum.item_status')['PENDING'])->get();
        
        if (isset($this->params['fo_id']) && $this->params['fo_id']) {
            
            throw_if(
                $order_items->count() != count($this->params['order_item_ids']),
                Exception::class,
                'Not all Fulfillment order items are in Pending state'
            );
        }

        $this->params['order_item_ids'] = $order_items->pluck('id');

		foreach($order_items as $order_item) {

			$this->booking_data['weight'] += $order_item->weight;
            $this->booking_data['cod'] += $order_item->sub_total + $order_item->tax;
			$this->booking_data['quantity'] += $order_item->quantity;
			$this->booking_data['description'] .=  $order_item->SKU." - ".$order_item->product_name." [".$order_item->quantity."] - ";
		}

		if (AddOn::fbr($this->seller_id) && Setting::fbr($this->seller_id) && $this->order_data['fbr_tax'] > 0 && !$this->order_data['fbr_fee_charged']) {
            $this->booking_data['cod'] += $this->order_data['fbr_tax'];
		}

        $this->booking_data['cod'] += + $this->booking_data['shipping_fee'];
        $this->booking_data['cod_amount'] = $this->booking_data['cod'];

		if (!$this->order_data['cod_payment']) {
			$this->booking_data['cod'] = NULL;
		}

		if ($this->params['manual_weight']) {
			$this->booking_data['weight'] = $this->params['manual_weight'];
		}

		if ($this->params['courier_id'] == 13 && $this->booking_data['weight'] < 0.5) {
			$this->booking_data['weight'] = 0.5;
		} elseif ($this->booking_data['weight'] < 0.1) {
			$this->booking_data['weight'] = 0.1;
		}

        return $this;
    }

    private function setLabelData()
    {
		$setting = new Setting;
		
        /// Description/Product Detail value in Settings ///
		$this->booking_data['description'] = $setting->shipmentDetail($this->seller_id, $this->booking_data['description']);

		/// Remarks value in Settings ///
		$this->booking_data['remarks'] = $setting->shipmentRemark($this->seller_id);

        return $this;
    }

    private function checkStockAvailable()
    {
        $auto_shipped_controller = new AutoShippedController;

        if ($this->params['location_id'] && AddOn::location($this->seller_id) && Setting::location($this->seller_id)) {
            throw_if($auto_shipped_controller->warehouse_check($this->params['order_id'], $this->params['location_id'], $this->params['order_item_ids']) == 'Default', Exception::class, "Booking failed due to inventory not exists", $this->custom_status_code);
		}

        return $this;
    }

    private function setOriginData()
    {
        $seller_location = SellerLocation::find($this->params['location_id']);
        throw_if( !$seller_location, Exception::class, "Location Not Exits or Maybe Deleted", $this->custom_status_code);
        
        $this->booking_data['default_pickup_address'] = [
            'contact_person_name' => $seller_location->location_name, 
            'contact_person_phone_number' => $seller_location->phone,
            'address' => $seller_location->address,
            'postal_code' => $seller_location->postal_code,
            'lat' => $seller_location->latitude,
            'lng' => $seller_location->longitude
        ];

        $this->booking_data['seller_city'] = CourierCity::where('courier_id', $this->params['courier_id'])->where('city_id', $seller_location->city)->value('courier_city_name');   
        $this->booking_data['seller_city_code'] = CourierCity::where('courier_id', $this->params['courier_id'])->where('city_id', $seller_location->city)->value('courier_city_code'); 

        $origin = City::where('id',$seller_location->city)->first();

        $this->booking_data['origin'] = $origin->name;
        $this->booking_data['seller_country_code'] = $origin->country_code;

        $this->pickupAvailableAtOrigin($seller_location->city);

        return $this;
    }

    private function setReturnAddress()
    {
        if(
            AddOn::omniReturn($this->seller_id) &&
            $seller_return_location_mapping = SellerReturnAddressMapping::where('city_id', $this->destination_city_id)->where('seller_id',$this->seller_id)->first()
        ) {

            if ($seller_return_location = SellerLocation::where('seller_id',$this->seller_id)->where('id',$seller_return_location_mapping->location_id)->first()) {

                $this->booking_data['seller_return_location_phone'] = $seller_return_location->phone;
                $this->booking_data['seller_return_location_address'] = $seller_return_location->address;
                $this->booking_data['seller_return_location_city'] = City::where('id', $seller_return_location->city)->value('name');
                $this->booking_data['seller_return_location_id'] = $seller_return_location_mapping->location_id;
                $this->booking_data['seller_return_location_city_code'] = CourierCity::where('courier_id', $this->params['courier_id'])->where('city_id', $seller_return_location->city)->value('courier_city_code');
            }
		}

        return $this;
    }

    private function setOtherData()
    {
        $this->booking_data['warehouse'] = $this->params['location_id'];
        $this->booking_data['courier_id'] = $this->params['courier_id'];
        $this->booking_data['service'] = $this->params['courier_service_value'];
        $this->booking_data['country_code'] = $this->order_data['country'];
        $this->booking_data['customer_email'] = (filter_var($this->order_data['customer_email'], FILTER_VALIDATE_EMAIL) ? $this->order_data['customer_email'] : Seller::whereId($this->seller_id)->value('email'));
    }

    private function setPaymentInfo()
    {
        if (isset($this->params['payment_info_option']) && $this->params['payment_info_option']) {
            $this->booking_data['payment_info_option'] = $this->params['payment_info_option'];

        } else {
            if($payment_info = SellerPaymentInfoOption::whereSellerId($this->seller_id)->where('active',1)->where('default',1)->with(['paymentInfoOption'])->first()) {
                $this->booking_data['payment_info_option'] = PaymentInfoOption::whereId($payment_info->payment_info_options_id)->value('value');
            } else {
                $this->booking_data['payment_info_option'] = 'DAP';
            }
        }

        if(isset($this->params['mps']) && $this->params['mps']){
            $this->booking_data['mps'] = $this->params['mps'];
        }
    }




    /// ----- setOriginData() child function ----- \\\
    private function pickupAvailableAtOrigin($origin_city)
    {
        if (in_array($this->params['courier_id'], [8,11] )) {
			
            throw_if(
                !CourierCity::where('courier_id', $this->params['courier_id'])->where('city_id', $origin_city)->where('origin',1)->exists(),
                Exception::class,
                'Selected Courier does not pickup from '.$this->booking_data['origin'],
                $this->custom_status_code
            );
		}
    }



    /// ----- requestCourier() child function ----- \\\
    private function saveBookingCallResponseData($response)
    {
        throw_if(
            isset($response['error']) && $response['error'],
            Exception::class,
            $response['message'],
            (isset($response['retry']) && $response['retry'] ? $this->custom_retry_status_code : $this->custom_status_code)
        );
        
        $this->tracking_number = $response['message'];
        
        $this->booking_data['tracking_number'] = $response['message'];
        $this->booking_data['user_id'] = ( session()->has('user') ? session('user')->id : ( auth()->check() ? NULL : 1 ) );
        
        $this->extras = (isset($response['label_image']) ? $response['label_image'] :  (isset($response['label_link']) ? $response['label_link'] : Null) );
        $this->extras = (isset($response['commercial_invoice']) ? $response['commercial_invoice'] : Null);
        $this->extras = (isset($response['batch_booking_id']) ? $response['batch_booking_id'] : Null);
        $this->booking_data['package_code'] = (isset($response['package_code']) ? $response['package_code'] : Null);

    }



    /// ----- postBooking() child function ----- \\\
    private function applyTagIfNecessary()
    {
        if(
            $this->params['courier_id'] == 24 && 
            Setting::where('seller_id', $this->seller_id)->where('key',config('enum.settings')['ONDEMANDFAILED'])->where('value',1)->exists() &&
            $tag = Tag::whereSellerId($this->seller_id)->whereValue('Ondemand Failed')->first()
        ) {

            $order_tag = new OrderTag();
            $order_tag->updateOrCreate(['order_id' => $this->params['order_id']], ['tag_id' => $tag->id]);
            
            OrderComments::add($this->params['order_id'], 'Order Tag Assignment Process', '<span class="label '.$tag->color.'">'.$tag->value.'</span> Tag has been assigned', 'Success', 1);
            OrderTag::tag_check($this->params['order_id'], $tag);
        }

        return $this;
    }

    private function chargedFbrFee()
    {
        if (!$this->order_data['fbr_fee_charged']) {

            Order::whereId($this->params['order_id'])->update([
                'fbr_fee_charged' => (!$this->order_data['fbr_fee_charged'] ? $this->tracking_number : $this->order_data['fbr_fee_charged'])
            ]);
        }

        return $this;
    }

    private function applyAfterBookingTag()
    {
        if ($this->params['after_booking_tag']) {
            $order_tag = new OrderTag;
            $order_tag->updateOrCreate(['order_id' => $this->params['order_id']], ['tag_id' => $this->params['after_booking_tag']]);
        }

        return $this;
    }

    private function createShipment()
    {
        $this->booking_data['user_id'] = (session()->has('user') ? session('user')->id : 0);

        $shipment = Shipment::dispatch($this->booking_data, $this->params['order_item_ids'], ($this->params['only_cn_assignment'] ? true : false), $this->extras);
        $this->shipment_id = $shipment->id;

        return $this;
    }

    private function addTrackingNumberOnTransaction()
    {
        if (isset($this->wallet_data['transaction']['transaction_id']) && $this->add_on_wallet) {

            WalletTransaction::updateCN($this->wallet_data['transaction']['transaction_id'], $this->booking_data['tracking_number']);
            $this->wallet_data = [];
        }
    }


}
