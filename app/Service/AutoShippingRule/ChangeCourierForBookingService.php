<?php 

namespace App\Service\AutoShippingRule;

use App\Events\OrderShippingEvent;
use App\Helpers\CheckBlacklistOrder;
use App\Models\AutoShipped;
use App\Models\Courier\City;
use App\Models\Courier\Courier;
use App\Models\Courier\CourierCity;
use App\Models\Courier\SellerCourier;
use App\Models\CourierPerformance;
use App\Models\CourierPerformanceInCities;
use App\Models\CourierService;
use App\Models\DistributedEntity;
use App\Models\FulfillmentOrder;
use App\Models\FulfillmentOrderItem;
use App\Models\Order;
use App\Models\OrderComments;
use App\Models\OrderItem;
use App\Models\OrderTag;
use App\Models\SellerCourierService;
use App\Models\SellerLocation;
use App\Service\Booking\BookingService;
use App\Traits\CallFunctionTrait;
use App\Traits\CallRequestTrait;
use Carbon\Carbon;
use Exception;

class ChangeCourierForBookingService
{
    use CallRequestTrait, CallFunctionTrait;

    protected $custom_status_code = 420;
    protected $custom_retry_status_code = 421;

    private
        $params,
        $process_name = 'Auto Shipping Re-Booking Process',
        $seller_id,
        $rule_id,

        $rule_data,
        $order_data,
        $booking_data,
        $ignore_able_rules = [],
        $ignore_able_couriers = [],

        $response_message = '';


    private $required_indexes = [
        'order_id',
        'rule_id'
    ];

    public function __construct($params)
    {
        $this->params = $params;
    }

    public function execute()
    {
        try {
            return $this->callRequest('initiateSuccessProcess');

        } catch (Exception $e) {
            return $this->callRequest('initiateFailureProcess', [ 'code' => $e->getCode(), 'message' => $e->getMessage(), 'trace' => $e->getTraceAsString()]);
        }
    }




    /// ----- Initiate Main Process ----- \\\
    private function initiateSuccessProcess()
    {
        /// Validating parameters and checking and adding in queue so others wont be able to pick this
        $this->validateParameterData()->extractDataFromParameter()->getOrderData()->getRuleData()->validateCities();
        $this->checkingAgainstRule()->validateCourierPickupAndDelivery();
        $this->sendToReBooking();

        /// In Both Process     Resetting the temporary data and handling success response
        return $this->addCommentAndHandleResponse(0, 'Success');
        /// In Both Process
    }

    private function initiateFailureProcess($parameters)
    {
        /// Validating parameters and checking and adding in queue so others wont be able to pick this
        $this->addActivityLog($parameters['code'], $parameters['message'], $parameters['trace']);


        /// In Both Process     Resetting the temporary data and handling error response
        return $this->addCommentAndHandleResponse(1, $parameters['message'], $parameters['code']);
        /// In Both Process
    }
    /// ----- Initiate Main Process ----- \\\












    /// ----- initiateSuccessProcess() child function ----- \\\

    private function validateParameterData()
    {
        throw_if(
            !$this->params,
            Exception::class,
            'Something is missing, Please try again'
        );

        foreach ($this->required_indexes as $required_index) {

            throw_if(
                !exists_or_null($this->params, $required_index),
                Exception::class,
                ucwords( str_replace('_',' ',$required_index) ).' parameter in missing.'
            );
        }

        return $this;
    }
  
    private function extractDataFromParameter()
    {
        $this->rule_id = $this->params['rule_id'];
        $this->ignore_able_couriers = $this->params['ignore_able_couriers'];
        $this->ignore_able_rules = isset($this->params['ignore_able_rules']) ? $this->params['ignore_able_rules'] : [];

        return $this;
    }
  
    private function getOrderData()
    {
        throw_if(
            !$this->order_data = Order::whereId($this->params['order_id'])->first()->toArray(),
            Exception::class,
            "Order not found | order id : ".$this->params['order_id']
        );

        $this->seller_id = $this->order_data['seller_id'];

        /// saving pick up city name to order data array for general used
        $temp_location_data = SellerLocation::whereId($this->params['location_id'])->first();
        $this->order_data['pickup_city'] = $temp_location_data->city_name ? $temp_location_data->city_name->name : 'null';


        return $this;
    }

    private function getRuleData()
    {
        /// if rule not found
        throw_if(
            !$this->rule_data = AutoShipped::where('seller_id',$this->seller_id)->where('id', $this->rule_id)->where('status',1)->orderBy('priority','asc')->first(),
            Exception::class,
            "Rule (".$this->rule_id.") not found for this seller (".$this->seller_id.").",
            $this->custom_status_code
        );

        return $this;
    }


    private function validateCities()
    {
        throw_if(
            !$this->order_data['pickup_city_id'] = SellerLocation::whereId($this->params['location_id'])->value('city'),
            Exception::class,
            "Pickup City not found in Unity Cities List.",
            $this->custom_status_code
        );

        throw_if(
            !$this->order_data['destination_city_id'] = City::where('name',$this->order_data['destination_city'])->value('id'),
            Exception::class,
            "Destination City not found in Unity Cities List.",
            $this->custom_status_code
        );
    }


    private function checkingAgainstRule()
    {
        $rule = (object) $this->rule_data;

        // $this->checkDistributionIsEnable();

        $this->booking_data['courier_id'] = null;
        $this->booking_data['service_id'] = null;

        $this->ignore_able_couriers[] = $this->params['courier_id'];

        if ($rule->booking_partner == 5) {   /// FOR COURIER PRIORITY

            foreach (json_decode($rule->courier_priority_value) as $key => $value) {

                if (in_array($value->id, $this->ignore_able_couriers)) {
                    continue;
                }
                
                if (SellerCourier::whereSellerId($rule->seller_id)->whereCourierId($value->id)->value('status') == 1 && CourierCity::whereCityId($this->order_data['destination_city_id'])->whereCourierId($value->id)->exists() && CourierCity::whereCityId($this->order_data['pickup_city_id'])->whereCourierId($value->id)->whereOrigin(1)->exists()) {
                    
                    $this->booking_data['courier_id'] = $value->id;
                    $this->response_message .= '<br><b>Courier Validated : </b> Courier <b>'.Courier::whereId($value->id)->value('name').'</b> is selected as a courier priority';
                    break;
                }
            }

        } else {

            if (!in_array($rule->courier_id, $this->ignore_able_couriers)) {
                
                $this->booking_data['courier_id'] = $rule->courier_id;
                $this->booking_data['service_id'] = $rule->service['value'] ?? null;
                $this->response_message .= '<br><b>Courier Validated : </b> Courier <b>'.Courier::whereId($rule->courier_id)->value('name').'</b> is selected as a define courier';
            }

        }

        throw_if(
            !$this->booking_data['courier_id'],
            Exception::class,
            "None courier is selected from this rule (".$rule->name.")",
            $this->custom_status_code
        );

        if(SellerCourier::whereSellerId($rule->seller_id)->whereCourierId($this->booking_data['courier_id'])->value('is_universal') == 2 && Courier::universalEnableCheck($this->booking_data['courier_id'])) {

            throw_if(
                !$universal_default_service = CourierService::where('courier_id', $this->booking_data['courier_id'])->where('is_universal',1)->value('value'),
                Exception::class,
                "Courier default service not found",
                $this->custom_status_code
            );

            $this->booking_data['service_id'] = $universal_default_service;
            $this->response_message .= '<br><b>Service Validated : </b> Courier Universal Service <b>'.$universal_default_service.'</b> is selected as a default service of universal courier';

        } else if (!$this->booking_data['service_id'] && $default_service = $this->getDefaultService($rule->seller_id, $this->booking_data['courier_id'])) {
                
            $this->booking_data['service_id'] = $default_service;
            $this->response_message .= '<br><b>Service Validated : </b> Courier Service <b>'.$default_service.'</b> is selected as a default service';
        }
        
        return $this;
    }


    private function validateCourierPickupAndDelivery()
    {
        /// if selected courier does not have pickup service in location pickup city
        throw_if(
            !CourierCity::where('courier_id', $this->booking_data['courier_id'])->where('city_id', $this->order_data['pickup_city_id'])->where('origin',1)->exists(),
            Exception::class,
            "Selected courier (".$this->booking_data['courier_id'].") doest not have pickup services in ".$this->order_data['pickup_city'],
            $this->custom_status_code
        );

        /// if selected courier does not have delivery service in location destination city
        throw_if(
            !CourierCity::where('courier_id', $this->booking_data['courier_id'])->where('city_id', $this->order_data['destination_city_id'])->exists(),
            Exception::class,
            "Selected courier (".$this->booking_data['courier_id'].") doest not have delivery services in ".$this->order_data['destination_city'],
            $this->custom_status_code
        );

        return ['error' => 0, 'message' => 'success'];
    }



    private function sendToReBooking()
    {
        if (isset($this->params['runtime_booking']) && $this->params['runtime_booking']) {

            $params = [
                'order_id' => $this->params['order_id'],
                'courier_id' => $this->booking_data['courier_id'],
                'booking_type' => $this->params['booking_type'],
                'location_id' => $this->params['location_id'],
                'courier_service_value' => $this->booking_data['service_id'],
                'only_cn_assignment' => $this->params['only_cn_assignment'],
                'order_item_ids' => $this->params['order_item_ids'],
                'shipping_fee_already_charged' => Null,
                'manual_weight' => Null,
                'quotation_id' => NULL,
                'auto_shipping_batch_log_id' => $this->params['auto_shipping_batch_log_id'],
                'after_booking_tag' => $this->params['after_booking_tag'],
                'fo_id' => $this->params['fo_id'],
            ];
    
            $booking_service = new BookingService($params);
            $result = $booking_service->execute();

            /// Check if the booking was successful , if not then Trigger Re-Booking Service
            if ($result['error']) {

                $params['ignore_able_couriers'] = $this->ignore_able_couriers;
                $params['runtime_booking'] = true;
                $params['rule_id'] = $this->params['rule_id'];
    
                $change_courier_service = new Self($params);
                $result = $change_courier_service->execute();

                throw_if(
                    $result['error'],
                    Exception::class,
                    $result['message'],
                    $this->custom_status_code
                );
            }

        } else {

            event(new OrderShippingEvent(
                $this->params['location_id'],
                $this->params['order_id'],
                $this->booking_data['courier_id'],
                $this->booking_data['service_id'],
                $this->params['after_booking_tag'],
                $this->params['auto_shipping_batch_log_id'],
                false,
                $this->params['only_cn_assignment'],
                $this->rule_data->id,
                $this->params['order_item_ids'],
                $this->params['fo_id'],
                $this->ignore_able_couriers
            ));
        }
    }

    
    /// ----- initiateSuccessProcess() child function ----- \\\









    /// ----- initiateFailedProcess() child function ----- \\\

    private function addActivityLog($code, $message, $trace)
    {
        if ($code != 420) {

            activity()
            ->causedBy($this->seller_id)
            ->withProperties(compact('message', 'trace'))
            ->log($this->process_name);
        }
    }

    /// ----- initiateFailedProcess() child function ----- \\\







    /// ----- Initiate In Both Process() child function ----- \\\

    private function addCommentAndHandleResponse($error, $message, $code = 200)
    {
        if (isset($this->params['runtime_booking']) && $this->params['runtime_booking']) {
            $message .= ' | Re-booking failed.';
        } else {
            $message .= ' | Re-booking the order with another courier because the selected previous couriers booking was failed.';
        }

        if ($this->params && isset($this->params['order_id']) && $error) {
            OrderComments::add($this->params['order_id'], $this->process_name, $message, ($error ? 'Failed' : 'Success'), Null);
        }

        return ['error' => $error, 'message' => $message, 'retry' => ($code == 421 ? true : false)];
    }

    /// ----- Initiate In Both Process() child function ----- \\\

















    /// ----- checkingAgainstRule() child function ----- \\\

    private function checkDistributionIsEnable()
    {
        throw_if(
            $this->rule_data['distributed_entity_id'] && !DistributedEntity::isEnable($this->seller_id),
            Exception::class,
            "Distribution Addon OR Setting is not enable",
            $this->custom_status_code
        );

        return $this;
    }



    public function getDefaultService($seller_id, $courier_id)
    {
        $courier_services = CourierService::where('courier_id', $courier_id)->pluck('id');
        $default_service = SellerCourierService::where('default', 1)->whereIn('courier_services_id', $courier_services)->where('seller_id', $seller_id)->value('courier_services_id');

        if (!$default_service) {
            $default_service = SellerCourierService::whereIn('courier_services_id', $courier_services)->where('seller_id', $seller_id)->value('courier_services_id');
        }

        if ($default_service) {
            $default_service = CourierService::whereId($default_service)->value('value');
        }

        return $default_service;
    }

    /// ----- checkingAgainstRule() child function ----- \\\


}