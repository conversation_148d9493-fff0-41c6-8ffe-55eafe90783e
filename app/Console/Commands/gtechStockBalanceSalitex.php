<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Helpers\GTech;
use App\Models\AddOn;
use App\Models\Setting;

class gtechStockBalanceSalitex extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:get-gtech-stock-balance-salitex';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get gtech stock balance for salitex';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $seller_id = 5199;
        $api_key = Setting::where('seller_id',$seller_id)->where('key', config('enum.api_keys')['GTECH_SYSTEM_API_KEY'])->first();
        $url = Setting::where('seller_id',$seller_id)->where('key', config('enum.api_keys')['GTECH_SYSTEM_API_URL'])->first();
        if($api_key && $url){
            $gtech = new GTech($api_key->value,$url->value);
            $gtech->stockBalance($seller_id);
        }

    }
}
