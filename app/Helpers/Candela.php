<?php 

namespace App\Helpers;

use App\Models\Inventory;
use App\Models\Product;
use App\Models\Courier\City;
use App\Models\SellerLocation;
use Exception;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use App\Models\OrderTag;
use App\Models\Tag;
use App\Models\Setting;
use App\Models\FulfillmentOrder;
use App\Models\Order;
use App\Models\OrderItem;
use Carbon\Carbon;
use App\Models\SellerPaymentMethod;
use App\Models\OrderComments;
use App\Models\Shipment;
use App\Models\ShopifyApp;

class Candela
{
    protected $api_key;
    protected $url;
    protected $unity_pocs;
    protected $api_id;

    public function __construct($api_key, $api_id ,$url) {
        $this->api_key = $api_key;
        $this->api_id = $api_id;
        $this->url = $url;
        $this->unity_pocs = '<EMAIL>';
    }

  
    public function stockBalance($seller_id)
    {
        $sellerLocations = SellerLocation::where('seller_id', $seller_id)->where('omni_inventory_sync', 1)->where('is_ffc_enabled', 0)->get();
        if($sellerLocations->count() > 0){

            
            foreach($sellerLocations as $location){

                hold_data_for_internal_logger(
                    $seller_id,
                    'Auto',
                    1,
                    'System',
                    'Ledger Inventory',
                    'stock, uncommitted_stock',
                    'Warehouse Location',
                    $location->id,
                    $location->location_name,
                    'Candela Override',
                    'Sync inventory from Candela, one time in a day'
                );

                Log::info("Candela :: Seller ".$seller_id." and location  ".$location->location_name);

                if(isset($location->seller_reference_id) && $location->seller_reference_id != null){
                    Log::info("Candela :: Seller ".$seller_id." and location as reference id ".$location->seller_reference_id);

                        //Bulk Inventory update for the location
                        // Inventory::where('seller_location_id',$location->id )->update(['stock' => 0 , 'uncommitted_stock' => 0]);
                        
                        $curl = curl_init();

                        curl_setopt_array($curl, array(
                        CURLOPT_URL => $this->url.'/api/Inventory/ShopInventory?AppId='.$this->api_id.'&AppKey='.$this->api_key.'&ShopId='.$location->seller_reference_id,
                        CURLOPT_RETURNTRANSFER => true,
                        CURLOPT_ENCODING => '',
                        CURLOPT_MAXREDIRS => 10,
                        CURLOPT_TIMEOUT => 0,
                        CURLOPT_FOLLOWLOCATION => true,
                        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                        CURLOPT_CUSTOMREQUEST => 'GET',
                        CURLOPT_HTTPHEADER => array(
                            'AppId: asdasds'
                        ),
                        ));
    
                        $response = curl_exec($curl);
                        $products_in_candela = [];
                        if($response){
                            curl_close($curl);
                            Log::info("Candela stockBalance seller_id :: ".$seller_id."  The API Response received ");
                            // Log::info($response);
                            $response = json_decode($response);
                            if(isset($response->data) && count($response->data) > 0){
                                Log::info("Candela stockBalance :: Count of Response".count($response->data));
                                foreach($response->data as $res){
                                     $product = Product::where('seller_id',$seller_id)->where('barcode',$res->Product_code)->first();
                                     Log::info("Seller id - Product - Quantity ".$seller_id." / ".$res->Product_code." / ".$res->quantity);
                                     if($product){
                                        Log::info("stockBalance :: Product / QTY- - ".$product->id." / ".$res->quantity);
                                        $quantity = $res->quantity;
                                        if ($buffer_size = Setting::retailInventoryBufferSize($seller_id)) {
                                            $quantity = $res->quantity -  $buffer_size;
                                            if($quantity < 1){
                                                $quantity = 0;
                                            }
                                            Log::info("Candela After buffer addition :: ".$quantity);
                                        }
                                        Inventory::overideStock($product->id, $location->id, $quantity, false);
                                        $products_in_candela[] = $product->id;
                                     }else{
                                        Log::info("Candela Product against the barcode ".$res->Product_code." -- seller id -- ".$seller_id);
                                     }
                                }
                            }
                            Log::info("stockBalance :: Products in candela :: count :: ".count($products_in_candela)." :: location id :: ".$location->id);

                            Inventory::syncLocationInventory($seller_id, [$location->id]);
                        }

                }
            }
        }
    }

    public function postOrder($fulfiment_order,$event)
    {
        try {
            $curl = curl_init();
            $api_url = $this->url.'/api/orders/PostOrder';
            \Log::info($api_url);
            $order = Order::find($fulfiment_order->order_id);
            $unity = $this->unity_pocs;
            $discount_per_item = $order->discount / count($fulfiment_order->items);
            $items_array = [];
            $shopify = new ShopifyApp();
            $seller_location = SellerLocation::find($fulfiment_order->seller_location_id);

            if(count($fulfiment_order->items) > 1){
                $count = 0;
                foreach($fulfiment_order->items as $item){
                    $order_item = OrderItem::find($item->order_item_id);
                    $product = Product::find($order_item->product_id);
                    $result = $shopify->getCompareAtPrice($product->marketplace_product_id, $product->marketplace_variant_id, $order->seller_id);
                  
                    Log::info("compare at price response :: ".$fulfiment_order->reference_id);
                    Log::info($result);

                    if($result['error'] == 0){
                        $unit_price = $result['compare_at_price'];
                    }else{
                        Log::info("Error response during compare at price :: ".$fulfiment_order->reference_id);
                        Log::info($result);
                        $unit_price = $order_item->actual_price;
                    }

                    $compare_at_price_discount = 0;
                    if($unit_price > $order_item->actual_price){
                        $compare_at_price_discount = $unit_price - $order_item->actual_price;
                    }
                    Log::info("compare_at_price_discount :: ".$fulfiment_order->reference_id);
                    Log::info($compare_at_price_discount);

                    Log::info("unit_price :: ".$fulfiment_order->reference_id);
                    Log::info($unit_price);

                    $items_array[$count]['ProductCode'] = $order_item->barcode;
                    $items_array[$count]['Qty'] = $order_item->quantity;
                    $items_array[$count]['DiscountPerc'] =  ( ($compare_at_price_discount + $discount_per_item) / $unit_price) * 100;
                    $items_array[$count]['ItemAmount'] =  $unit_price;
                    $items_array[$count]['ItemTotal'] =  $unit_price * $order_item->quantity;
                    $count++;
                }
                
            }else{
                $order_item = OrderItem::find($fulfiment_order->items[0]->order_item_id);
                $product = Product::find($order_item->product_id);
                $result = $shopify->getCompareAtPrice($product->marketplace_product_id, $product->marketplace_variant_id, $order->seller_id);

                Log::info("compare at price response :: ".$fulfiment_order->reference_id);
                Log::info($result);
                
                if($result['error'] == 0){
                    $unit_price = $result['compare_at_price'];
                }else{
                    Log::info("Error response during compare at price :: ".$fulfiment_order->reference_id);
                    Log::info($result);
                    $unit_price = $order_item->actual_price;
                }

                $compare_at_price_discount = 0;
                if($unit_price > $order_item->actual_price){
                    $compare_at_price_discount = $unit_price - $order_item->actual_price;
                }

                Log::info("compare_at_price_discount :: ".$fulfiment_order->reference_id);
                Log::info($compare_at_price_discount);

                Log::info("unit_price :: ".$fulfiment_order->reference_id);
                Log::info($unit_price);

                $items_array[0]['ProductCode'] = $order_item->barcode;
                $items_array[0]['Qty'] = $order_item->quantity;
                $items_array[0]['DiscountPerc'] = ( ($compare_at_price_discount + $discount_per_item) / $unit_price) * 100;
                $items_array[0]['ItemAmount'] =  $unit_price;
                $items_array[0]['ItemTotal'] =  $unit_price * $order_item->quantity;
            }

            $data['AppId']  = $this->api_id;
            $data['AppKey']  =  $this->api_key;
            $data['OrderId']  =  $fulfiment_order->reference_id;
            $data['shopId']  =  ( isset($seller_location->seller_reference_id) && $seller_location->seller_reference_id != null ? $seller_location->seller_reference_id : "" );
            $data['OrderDate']  =  $order->created_date->format('m-d-Y H:i:s');
            $data['FirstName']  =  $order->customer_name;
            $data['CustomerEmai']  =  $order->customer_email;
            $data['Address']  =  ( $order->shipping_address == null ? "" : $order->shipping_address);
            $data['City']  =  $order->destination_city;
            $data['Country']  =   $order->country;
            $data['State']  =  "";
            $data['Telephone']  =  $order->customer_number;
            $data['Status']  = "Processing";
            $data['CustomerNo']  =  "";
            $data['comments']  =  "";
            $data['CourierCompany']  =  $event->courier_name;
            $data['CourierNumber']  =  $event->tracking_number;
            $data['ReedemedPoints']  =  "";
            $data['ProvisionalPoints']  =  "";
            $data['Weight']  =  $order->items->sum('weight');
            $data['Locality']  =  "Local";

            \Log::info("Post Order");
            Log::info($fulfiment_order->id."======".$order->shipping_fee_charged);

            $data['ShippingCost'] = "0";

            if($fulfiment_order->id == $order->shipping_fee_charged){
                $data['ShippingCost'] =  (string) $order->shipping_fee;
            }
            $data['Products'] = $items_array;
            
            \Log::info($data);
            \Log::critical($data);


            $curl = curl_init();

            curl_setopt_array($curl, array(
                CURLOPT_URL => 'http://***************:96/api/orders/PostOrder',
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_POSTFIELDS => json_encode($data),
                CURLOPT_HTTPHEADER => array(
                  'Content-Type: application/json'
                ),
              ));


            $response = curl_exec($curl);
            \Log::critical("The Response of Candela postOrder is ::");
            \Log::critical(json_encode($response));
            curl_close($curl);

            $response = json_decode($response,true);


            if(isset($response['code']) && $response['code'] == "11" && isset($response['msg']) && $response['msg'] == "Saved Successfully"){          
            
                \Log::info("Candela postOrder :  Saved Successfully");
                OrderComments::add($order->id, 'Candela Post Order Process', $response['msg'] .' - '.$fulfiment_order->reference_id, 'Success', 1);

            }else{
                $error_message = "Review Error in Log File";
                if(isset($response['msg'])){          
                    OrderComments::add($order->id, 'Candela Post Order Process', $response['msg'], 'Failed', 1);
                    $error_message = $response['msg'];
                }
                \Log::info("Candela postOrder : Failed ". $fulfiment_order->id);
                $subject = "Candela <".$fulfiment_order->reference_id."> - <postOrder> - Sync Failed after shipment Booked";
                Mail::raw($error_message.' | Candela postOrder after Booking : Failed', function ($m)  use($unity,$subject) {
                    $m->to($unity)
                        ->bcc($unity)
                        ->subject($subject);
                });
            }
        } catch (\Throwable $th) {
            \Log::info("Candela postOrder : Try Catch  ". $fulfiment_order->id);
            \Log::info($th);
            $subject = "Candela <".$fulfiment_order->reference_id."> - <postOrder> - Sync Failed after shipment Booked - Catch Block";
            Mail::raw($th.' | Candela postOrder after Booking : Catch', function ($m)  use($unity,$subject) {
                $m->to($unity)
                ->subject($subject);
            });
        }
        
    }

}