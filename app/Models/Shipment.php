<?php

namespace App\Models;

use App\Events\CreateTransferOrderInAghaNoorPkEvent;
use App\Events\ShipmentBookingEvent;
use App\Helpers\FBR;
use App\Helpers\FBRFulfillmentWise;
use App\Jobs\MasterAccountCreateTransferOrderJob;
use App\Mail\CourierError;
use App\Models\AddOn;
use App\Models\Courier\City;
use App\Models\Courier\Courier;
use App\Models\Courier\CourierCity;
use App\Models\Courier\CourierLoadsheetShipment;
use App\Models\Courier\CustomCourier;
use App\Models\Courier\SellerCourier;
use App\Models\Courier\SellerCourierBlueEx;
use App\Models\Courier\SellerCourierBykea;
use App\Models\Courier\SellerCourierCallCourier;
use App\Models\Courier\SellerCourierCourierX;
use App\Models\Courier\SellerCourierLCS;
use App\Models\Courier\SellerCourierMNP;
use App\Models\Courier\SellerCourierMoveX;
use App\Models\Courier\SellerCourierRider;
use App\Models\Courier\SellerCourierSwyft;
use App\Models\Courier\SellerCourierTraxNew;
use App\Models\Inventory;
use App\Models\OrderItem;
use App\Models\Product;
use App\Models\RMAItems;
use App\Models\Setting;
use App\ShipmentCourierHistory;
use Carbon\Carbon;
use Exception;
use Error;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use App\Events\ShipmentStatusChangeEvent;
use App\Events\ShipmentStorefrontTaggingEvent;
use App\Helpers\SellerCouriers\StallionHelper;
use App\Jobs\CancelFulfillment;
use App\Jobs\Warehouse\ThreePLJob;
use App\Jobs\JdotERPJob;
use App\Models\Courier\SellerCourierForrun;
use App\Models\Courier\SellerCourierInsta;
use App\Models\ShipmentQuotation;
use App\Models\Courier\SellerCourierPandaGo;
use App\Models\Courier\SellerCourierPostEx;
use App\Models\Courier\SellerCourierQuiqup;
use App\Models\Courier\SellerCourierTCSNew;
use App\Service\CodeReuse\CustomerIntimationService;
use App\Service\SellerCouriers\Stallion;
use App\Traits\ExceptionTrait;
use App\Traits\HasLocalDates;
use App\Events\GTechPosOnlineInsertEvent;
use App\Events\CheckInventoryAndCreateOrderInGtechEvent;
use App\Events\CreateOrderInGtechForCambOrZeenEvent;
use App\Events\CreateOrderInGtechForMantoEvent;
use App\Events\CreateOrderInGtechForMtjEvent;

use App\Events\CreateOrderInCandelaEvent;
use App\Events\CreateTransferOrderInMantoPkEvent;
use App\Jobs\DinersERPJob;
use App\Jobs\TechnosysERPJob;
use App\Jobs\ZubaidasERPJob;
use App\Models\Courier\SellerCourierCityMail;
use App\Models\Courier\SellerCourierDEX;
use App\Models\Courier\SellerCourierFlyCourier;
use App\Models\Courier\SellerCourierLCSMerchant;

class Shipment extends Model
{
    use ExceptionTrait, HasLocalDates;

    protected $fillable = ['delivered_at', 'status','return_received_at_location'];

    public function items()
    {
        return $this->hasMany(\App\Models\ShipmentItem::class);
    }

    public function order()
    {
        return $this->belongsTo(\App\Models\Order::class);
    }

    public function orderMarketplaceReferenceId()
    {
        return $this->order()->select('id', 'marketplace_reference_id');
    }

    public function multi_package_shipments()
    {
        return $this->hasMany(\App\Models\MultiPackageShipment::class);
    }

    public function shipment_reason()
    {
        return $this->hasOne(\App\Models\ShipmentReason::class)->select('shipment_id', 'reason_id')->with('reason');
    }

    public function courier()
    {
        return $this->belongsTo(\App\Models\Courier\Courier::class, 'courier_id', 'id')->select('id', 'name');
    }

    public function quotation()
    {
        return $this->belongsTo(\App\Models\ShipmentQuotation::class, 'qoutation_id', 'id');
    }

    public function courierWithImage()
    {
        return $this->belongsTo(\App\Models\Courier\Courier::class, 'courier_id', 'id')->select('id', 'name', 'image');
    }

    public function shipment_histories()
    {
        return $this->hasMany(\App\Models\ShipmentHistory::class, 'shipment_id', 'id');
    }

    public function shipment_booking_courier() {
        return $this->hasOne('App\Models\ShipmentExtra')->select('id','shipment_id','key','value')->where('key','booking_courier')->withDefault(['value' => 0]);
    }
    public function shipment_courier_histories() {
        return $this->hasMany('App\ShipmentCourierHistory', 'shipment_id', 'id');
    }

    public function shipper_advices()
    {
        return $this->hasMany(\App\Models\ShipperAdvice::class, 'shipment_id', 'id');
    }

    public function booked() {
        return $this->belongsTo(\App\Models\SellerUser::class, 'booked_by', 'id')->select('id','full_name')->withDefault(['full_name' => auth()->user()->full_name]);
    }

    public function rmaRequest()
    {
        return $this->hasOne(\App\Models\RMARequest::class, 'shipment_id', 'id');
    }

    public function location()
    {
        return $this->belongsTo(\App\Models\SellerLocation::class, 'seller_location_id', 'id')->select('id', 'location_name', 'is_default')->withDefault(['location_name' => 'Location not found', 'is_default' => 0]);
    }

    public function location_with_all_columns()
    {
        return $this->belongsTo(\App\Models\SellerLocation::class, 'seller_location_id', 'id');
    }

    public function codPaymentShipment()
    {
        return $this->hasOne(\App\Models\CODPaymentShipment::class, 'shipment_id');
    }

    public function extra_column()
    {
        return $this->morphOne(\App\Models\ExtraColumn::class, 'keyable');
    }

    public function loadsheet()
    {
        return $this->hasOne(CourierLoadsheetShipment::class, 'shipment_id', 'id');
    }

    // public function getCreatedAtAttribute()
    // {
    // 	return ( $this->attributes['created_at'] ? Carbon::parse($this->attributes['created_at'])->toDateString() : $this->attributes['created_at']);
    // }

    // public function getDeliveredAtAttribute()
    // {
    // 	return ( $this->attributes['delivered_at'] ? Carbon::parse($this->attributes['delivered_at'])->toDayDateTimeString() : $this->attributes['delivered_at']);
    // }

    public static function seller_all()
    {
        return self::where('shipments.seller_id', Auth::id())->where('shipments.status', config('enum.shipment_status')['DISPATCHED'])->with('courier')->with('order')->orderBy('id', 'desc')->get();
    }

    public static function is_dispatched()
    {
        return self::take(10)->get();
        // return Shipment::selectRaw('shipment_histories.status')->join('shipment_histories','shipments.id','shipment_histories.shipment_id')->where('shipment_histories.status',config('enum.shipment_status')['DISPATCHED'])->first();
    }

    public static function courier_service()
    {
        $shipments = self::where('shipments.seller_id', Auth::id())->with(['courier', 'order' => function ($query) {
            $query->select('id', 'marketplace_reference_id', 'destination_city');
        }, 'booked'])
        ->select('shipments.tracking_number', 'shipments.destination_city', 'shipments.pickup_city', 'shipments.courier_id', 'shipments.order_id', 'shipments.booked_by', 'shipments.id', 'shipments.created_at', 'shipments.weight', 'shipments.cod', 'shipments.status as shipments.status', 'shipments.manual');

        if (session()->has('user') && session('user')->city) {
            $cities = City::whereIn('id', explode(',', session('user')->city))->pluck('name')->all();

            return $shipments->whereIn('shipments.destination_city', $cities);
        } else {
            return $shipments;
        }
    }

    public static function all_shipment()
    {
        $shipments = Shipment::where('shipments.seller_id', Auth::id())->with(['shipment_booking_courier','courier','location','order' => function($query) {
            $query->select('id','marketplace_reference_id','country','destination_city','placement_date');
        },'booked'])
        ->select('shipments.tracking_number','shipments.destination_city','shipments.seller_location_id','shipments.pickup_city','shipments.courier_id','shipments.order_id','shipments.booked_by','shipments.id','shipments.created_at','shipments.weight','shipments.cod','shipments.multi_package_shipment_status','shipments.status as shipments.status','shipments.manual','shipments.picklist_generated','shipments.invoice_generated');

        if (session()->has('user')) {
            if (Addon::location(Auth::id())) {
                if (session('user')->location) {
                    return $shipments->whereIn('shipments.seller_location_id', explode(',', session('user')->location));
                }
            } elseif (session('user')->city) {
                $cities = City::whereIn('id', explode(',', session('user')->city))->pluck('name')->all();

                return $shipments->whereIn('shipments.destination_city', $cities);
            }
        }

        return $shipments;
    }

    public static function all_shipper_advice_shipment()
    {
        $shipments = Shipment::where('shipments.seller_id', Auth::id())->with(['shipment_booking_courier','courier','location','order' => function($query) {
            $query->select('id','marketplace_reference_id','destination_city','placement_date');
        },'booked'])
        ->select('shipments.tracking_number','shipments.destination_city','shipments.seller_location_id','shipments.pickup_city','shipments.courier_id','shipments.order_id','shipments.booked_by','shipments.id','shipments.created_at','shipments.weight','shipments.cod','shipments.multi_package_shipment_status','shipments.status as shipments.status','shipments.manual');

        if (session()->has('user')) {
            if (Addon::location(Auth::id())) {
                if (session('user')->location) {
                    return $shipments->whereIn('shipments.seller_location_id', explode(',', session('user')->location));
                }
            } elseif (session('user')->city) {
                $cities = City::whereIn('id', explode(',', session('user')->city))->pluck('name')->all();

                return $shipments->whereIn('shipments.destination_city', $cities);
            }
        }

        return $shipments;
    }

    public static function seller_all_dispatched()
    {
        $status = [config('enum.shipment_status')['DELIVERED'], config('enum.shipment_status')['CANCELLED'], config('enum.shipment_status')['RETURNED'], config('enum.shipment_status')['LOST'], config('enum.shipment_status')['PENDING_RETURN'], config('enum.shipment_status')['RETURNED_RECEIVED']];

        return self::all_shipment()->whereNotIn('shipments.status', $status);
    }

    public static function seller_all_fulfilled()
    {
        return self::all_shipment()->where('shipments.status', config('enum.shipment_status')['DELIVERED'])->addSelect('shipments.delivered_at');
    }

    public static function seller_all_cancelled()
    {
        return self::all_shipment()
        ->whereIn('shipments.status', [config('enum.shipment_status')['CANCELLED'], config('enum.shipment_status')['RETURNED'], config('enum.shipment_status')['RETURNED_RECEIVED'], config('enum.shipment_status')['LOST'], config('enum.shipment_status')['PENDING_RETURN']])
        ->with('shipment_reason')
        ->addSelect('shipments.updated_at');
    }

    // shipping advice /////
    public static function seller_all_shipping_advice_pending()
    {
        return self::all_shipper_advice_shipment()->join('shipper_advice','shipments.id','shipper_advice.shipment_id')->where('shipper_advice.status', config('enum.shipper_advice_status')['Pending'])->addSelect('shipper_advice.courier_status','shipper_advice.courier_feedback');
    }
    public static function seller_all_shipping_advice_reattempt()
    {
        return self::all_shipper_advice_shipment()->join('shipper_advice','shipments.id','shipper_advice.shipment_id')->where('shipper_advice.status', config('enum.shipper_advice_status')['Reattempt'])->addSelect('shipper_advice.seller_advice_date', 'shipper_advice.remarks','shipper_advice.courier_status','shipper_advice.courier_feedback');
    }
    public static function seller_all_shipping_advice_return()
    {
        return self::all_shipper_advice_shipment()->join('shipper_advice','shipments.id','shipper_advice.shipment_id')->where('shipper_advice.status', config('enum.shipper_advice_status')['Return'])->addSelect('shipper_advice.seller_advice_date', 'shipper_advice.remarks','shipper_advice.courier_status','shipper_advice.courier_feedback');
    }
    ///////////////////////

    public static function cod_receiveable()
    {
        return Shipment::where('shipments.seller_id', Auth::id())->where('type',null)->where('shipments.status', config('enum.shipment_status')['DELIVERED'])
        ->where('cod', '>=', 1)->where('cod_received', 0)->with(['shipment_booking_courier','courier','items:id,shipment_id','order:id,marketplace_reference_id','loadsheet.courier_loadsheet'])
        ->select('tracking_number','shipments.id','courier_id','order_id','shipments.created_at','cod','delivered_at');
    }

    public static function seller_loadsheet_all($courier_id)
    {
        return self::where('seller_id', Auth::id())->where('courier_id', $courier_id)->where('loadsheet_generated', 0)->with('order')->orderBy('id', 'desc')->get();
    }

    public static function dispatch($data, $items, $manual, $extras = [])
    {
        $order = $data['order'];
        $warehouse = $data['warehouse'];
        Log::info("Warehouse when creating shipment".$warehouse);
        $shipment = new self();
        $shipment->seller_id = $order->seller_id;
        $shipment->order_id = $order->id;
        $shipment->weight = $data['weight'];
        $shipment->cod = $data['cod'];
        $shipment->description = $data['description'];
        $shipment->remarks = $data['remarks'];
        $shipment->courier_id = $data['courier_id'];
        $shipment->courier_service_value = $data['service'];
        $shipment->tracking_number = $data['tracking_number'];
        $shipment->manual = (isset($manual) ? ($manual ? 1 : 0) : 0);
        $shipment->status = config('enum.shipment_status')['BOOKED'];
        $shipment->booked_by = $data['user_id'];
        $shipment->label_image = (isset($extras['label_image']) ? $extras['label_image'] : Null);
        $shipment->commercial_invoice = (isset($extras['commercial_invoice']) ? $extras['commercial_invoice'] : Null);
        if(isset($data['qoutation_id'])){
            $courier_service = CourierService::where('courier_id', $data['courier_id'])->where('value', $data['service'])->first();
            if($courier_service){
                $qoutations = ShipmentQuotation::where('courier_id',$data['courier_id'])->where('qoutation_id',$data['qoutation_id'])->where('courier_service',$courier_service->id)->first();
            }else{
                $qoutations = ShipmentQuotation::where('courier_id',$data['courier_id'])->where('qoutation_id',$data['qoutation_id'])->first();
            }
            if($qoutations){
                $shipment->qoutation_id = $qoutations->id;
            }
        }


        $destination_city = $order->destination_city;
        $temp_city = City::where('name', $order->destination_city)->value('name');
        if ($temp_city) {
            $destination_city = $temp_city;
        }

        if (isset($warehouse) && $warehouse != 'Default') {
            $seller_location = SellerLocation::find($warehouse);
            if(isset($data['seller_return_location_id']) && $data['seller_return_location_id'] != null){
                $shipment->seller_return_location_id = $data['seller_return_location_id'];
            }
            $shipment->seller_location_id = $warehouse;

            $shipment->destination_city = $destination_city;
            $shipment->destination_address = $order->shipping_address;
            $shipment->pickup_city = City::where('id', $seller_location->city)->value('name');
            Log::info("Just before inserting in shipment the order id is ".$order->id);

            Log::info("Just before inserting in shipment".$warehouse);

            $shipment->pickup_address = $seller_location->address;
            $shipment->customer_name = $order->customer_name;
            $shipment->customer_number = $order->customer_number;
        } else {
            return 'Location is default';
        }

        if(isset($extras['mps'])){
            $shipment->multi_package_shipment_status = 1;
        } 

        $shipment->save();

        if(isset($extras['mps'])){
            
            foreach ($extras['child_cns'] as $value) {
                $s = new MultiPackageShipment();
                $s->shipment_id = $shipment->id;
                $s->package_tracking_numbers = $value;
                $s->save();
            }
            
        } 

        if(SellerCourier::whereSellerId($order->seller_id)->whereCourierId($data['courier_id'])->value('is_universal') == 2 && Courier::universalEnableCheck($data['courier_id'])) {
            $courier_account = 'universal';
        } else {
            $courier_account = 0;
        }



        $shipmentExtra = new ShipmentExtra();
        $shipmentExtra->shipment_id = $shipment->id;
        $shipmentExtra->key = 'booking_courier';
        $shipmentExtra->value = $courier_account;
        $shipmentExtra->save();

        if(isset($data['package_code'])){
            $shipmentExtra = new ShipmentExtra();
            $shipmentExtra->shipment_id = $shipment->id;
            $shipmentExtra->key = 'package_code';
            $shipmentExtra->value = $data['package_code'];
            $shipmentExtra->save();
        } 

        if ($shipment->courier_id == 20) {
            $extra_column = new ExtraColumn();
            $extra_column->value = (isset($extras['batch_booking_id']) ? $extras['batch_booking_id'] : null);
            $extra_column->message = 'Batch booking ID only for Bykea Shipments | '.(isset($extras['batch_booking_id']) ? $extras['batch_booking_id'] : null);
            $shipment->extra_column()->save($extra_column);
        }

        try {
            $order->assignLocationToOrderAndOrderItems($warehouse, 'Pickup location assigned to order.',1, $items, true);
        } catch (Exception $e) {
            Log::critical($e->getTraceAsString());
        }

        if (Auth::id()) {
            $orderComments = new OrderComments();
            $orderComments->order_id = $order->id;
            $orderComments->key = 'Shipment Booking Process';
            $orderComments->value = 'Shipment # '.$data['tracking_number'].' Created! ';
            $orderComments->status = 'Success';
            $orderComments->run_by = (session()->has('user') ? session('user')->id : null);
            $orderComments->save();
        }


        SellerCourier::where('seller_id',$order->seller_id)->where('courier_id',$data['courier_id'])->increment('current_booking');
        

        foreach ($items as $order_items_id) {
            $order_item = $order->items->where('id', $order_items_id)->first();

            $shipment_items = new ShipmentItem();
            $shipment_items->shipment_id = $shipment->id;
            $shipment_items->order_items_id = $order_items_id;
            $shipment_items->qty = $order_item->quantity;
            $shipment_items->save();

            $order_item->status = config('enum.item_status')['DISPATCHED'];
            $order_item->save();

            /////// Adding Order Comments if anything is failed ///////
            // $orderComments = new OrderComments;
            // $orderComments->order_id = $order->id;
            // $orderComments->key = 'Dispatch';
            // $orderComments->status = 'Failed';
            // $orderComments->run_by = '1';

            // if (isset($warehouse) && $warehouse != 'Default' && AddOn::inventory($order->seller_id) && Setting::inventory($order->seller_id)) {
            //     $product = Product::where('SKU', $order_item->SKU)->first();
            //     if ($product) {
            //         $inventory = Inventory::where('seller_location_id', $warehouse)->where('product_id', $product->id)->first();
            //         if ($inventory && $inventory->stock >= $order_item->quantity) {
            //             $inventory->stock = $inventory->stock - $order_item->quantity;
            //             $inventory->save();
            //         } else {
            //             $orderComments->value = 'Order Item ('.$order_item->SKU.') is not exists in Inventory or Inventory Stock is less than the order item quantity ';
            //             $orderComments->save();
            //         }
            //     } else {
            //         $orderComments->value = 'Order Item ('.$order_item->SKU.') SKU is not in the product list';
            //         $orderComments->save();
            //     }
            // }
        }

        $order->status=config('enum.order_status')['PROCESSING'];
        $order->save();

        try {
            FulfillmentOrder::assignedCNByItems($items, $shipment->id);
        } catch (Exception $e) {
            Log::critical("assigned CN error | Shipment ID : ".$shipment->id);
            Log::critical($shipment);
        } catch (Error $e) {
            Log::critical("assigned CN error | Shipment ID :: ".$shipment->id);
            Log::critical($shipment);
        }

        $shipment->shipment_history(config('enum.shipment_status')['BOOKED'], Carbon::now()->toDateTimeString());
        $shipment->after_save_events();

        return $shipment;
    }

    public function after_save_events()
    {
       

        /// Fulfillment (set status to dispatch) ///
        try {
            $loadsheetsetting = Setting::where('seller_id', $this->seller_id)->where('key', 'fulfilment')->first();
            $message = 'Shipment has been dispatched at the time of booking';
            if (isset($loadsheetsetting) && $loadsheetsetting->value == 'shipment_booking') {
                $this->fulfillment($message);
            } elseif (! isset($loadsheetsetting)) {
                $this->fulfillment($message);
            }
        } catch (Exception $e) {
            $activity_id = activity()
            ->performedOn($this)
            ->causedBy($this->seller_id)
            ->withProperties(['message' => $e->getMessage(), 'dump' => $e->getTraceAsString()])
            ->log('Dispatched Status');
        }

        /// 2 way Sync (Tag shipment to storefront) ///
        try {
            if (Setting::where('seller_id', $this->seller_id)->where('key', config('enum.settings')['TWOWAYSYNC'])->where('value', 1)->exists()) {
                $twoWaySyncEventSetting = Setting::where('seller_id', $this->seller_id)->where('key', config('enum.settings')['TWOWAYSYNCEVENT'])->first();

                if (isset($twoWaySyncEventSetting) && $twoWaySyncEventSetting->value == 'shipment_booking') {
                    $this->storefront_tagging();
                } elseif (! isset($twoWaySyncEventSetting)) {
                    $this->storefront_tagging();
                }
            }
        } catch (Exception $e) {
            $activity_id = activity()
            ->performedOn($this)
            ->causedBy($this->seller_id)
            ->withProperties(['message' => $e->getMessage(), 'dump' => $e->getTraceAsString()])
            ->log('2 way Sync');
        }


        /// FBR Request (Send Order data to FBR POS) ///
        try {
            if (AddOn::fbr($this->seller_id) && Setting::fbr($this->seller_id)) {
                if($this->seller_id == 119){ //continuing Khaadi with order wise FBR to ensure nothing much is changed for them
                    FBR::send($this->order);
                }else{
                    if (AddOn::gtech($this->seller_id)) {
                        $fulfilment_order = FulfillmentOrder::where('shipment_id',$this->id)->select('id','reference_id','seller_location_id')->first();
                        if($fulfilment_order){
                            $sellerLocation = SellerLocation::find($fulfilment_order->seller_location_id);
                            if($sellerLocation && $sellerLocation->is_ffc_enabled == 1){
                                FBRFulfillmentWise::send($this);
                            }
                        }
                    }else{
                        FBRFulfillmentWise::send($this);
                    }
                }
            }
        } catch (Exception $e) {
            $activity_id = activity()
            ->performedOn($this)
            ->causedBy($this->seller_id)
            ->withProperties(['message' => $e->getMessage(), 'dump' => $e->getTraceAsString()])
            ->log('FBR Request');
        }


        /// 3PL Request (Send Order and shipment data to 3PL POS) ///
        try {
            if (AddOn::threePL($this->seller_id)) {

                $seller_location = SellerLocation::whereId($this->seller_location_id)->value('seller_reference_id');

                if (!$seller_location) {
                    throw new Exception('Location Reference Id not found');
                }

                $params = [
                    'order_id' => $this->order_id,
                    'shipment_id' => $this->id,
                    'seller_id' => $this->seller_id,
                    'tracking_number' => $this->tracking_number,
                    'cod' => $this->cod,
                    'marketplace_reference_id' => $this->order->marketplace_reference_id,
                    'order_creation_date' => $this->order->created_date->toDateTimeString(),
                    
                    'customer_name' => $this->order->customer_name,
                    'customer_address' => $this->order->shipping_address,
                    'customer_email' => $this->order->customer_email,
                    'customer_contact' => $this->order->customer_number,
                    
                    'destination_city' => $this->order->destination_city,
                    'location_reference_id' => $seller_location,

                    'order_items' => $this->items()->with('order_item')->get()->toArray(),
                ];
                ThreePLJob::dispatch('createOrderWithAttachment', $params);
            }
        } catch (Exception $e) {

            OrderComments::add($this->order_id, '3PL Create Order Process', $e->getMessage().'<br><b>Tracking Number</b> : '.$this->tracking_number, 'Failed', 1);

            activity()
            ->performedOn($this)
            ->causedBy($this->seller_id)
            ->withProperties(['message' => $e->getMessage(), 'dump' => $e->getTraceAsString()])
            ->log('3PL Request');
        }

         /// Jdot D365 (Send Order Invoice in D365) ///
         try {
            if (AddOn::erpJdot($this->seller_id)) {
                
                $internal_params = [ 'order_id' => $this->order->id, 'seller_id' => $this->seller_id];

                $params = [ 'shipment' => $this, 'internal_params' => $internal_params ];

                Log::info("Dispatching job");

                JdotERPJob::dispatch('createSalesOrderService', $params);
            }
        } catch (Exception $e) {

            OrderComments::add($this->order_id, 'D365 Create Invoice Process', $e->getMessage().'<br><b>Tracking Number</b> : '.$this->tracking_number, 'Failed', 1);

            activity()
            ->performedOn($this)
            ->causedBy($this->seller_id)
            ->withProperties(['message' => $e->getMessage(), 'dump' => $e->getTraceAsString()])
            ->log('D365 Jdot Request');
        }

        /// GTech Pos Online Insert or Create Draft Order///
        try {
            if (AddOn::gtech($this->seller_id)) {
                Log::info("Inside GTech Post Online / create draft order Job condition");
                Log::info($this->id);
                $fulfilment_order = FulfillmentOrder::where('shipment_id',$this->id)->select('id','reference_id','seller_location_id')->first();
				if($fulfilment_order){
                    $sellerLocation = SellerLocation::find($fulfilment_order->seller_location_id);
                    if($sellerLocation && $sellerLocation->is_ffc_enabled == 1){
    					// event(new GTechPosOnlineInsertEvent($fulfilment_order,$this));
                        // Map different courier name for Salitex seller
                        if ($this->seller_id == env('SALITEX_SELLER_ID')) {
                            $courier_name = config('enum.gtech_erp_courier_name_salitex')[$this->courier_id] ?? config('enum.gtech_erp_courier_name')[$this->courier_id];
                        } else {
                            $courier_name = config('enum.gtech_erp_courier_name')[$this->courier_id];
                        }
                        if($this->seller_id == env('CAMB_SELLER_ID') || $this->seller_id == env('ZEEN_SELLER_ID')){
                            \Log::info("Inside Camb / Zeen Gtech Condition Shipment ID :: ".$this->id);
                            event(new CreateOrderInGtechForCambOrZeenEvent($fulfilment_order->id,$fulfilment_order->reference_id,$this->seller_id,$this->tracking_number,$courier_name,$this->cod,$this));
                        } else if($this->seller_id == env('MTJ_SELLER_ID')){
                            \Log::info("MTJ Gtech Condition Shipment ID :: ".$this->id);
                            event(new CreateOrderInGtechForMtjEvent($fulfilment_order->id,$fulfilment_order->reference_id,$this->seller_id,$this->tracking_number,$courier_name,$this->cod,$this));
                        } else if($this->seller_id == env('MANTO_PK_SELLER_ID')){
                            \Log::info("Manto Gtech Condition Shipment ID :: ".$this->id);
                            event(new CreateOrderInGtechForMantoEvent($fulfilment_order->id,$fulfilment_order->reference_id,$this->seller_id,$this->tracking_number,$courier_name,$this->cod,$this));
                        } else {
                            \Log::info("Else Gtech Condition Shipment ID :: ".$this->id);
                            event(new CheckInventoryAndCreateOrderInGtechEvent($fulfilment_order->id,$fulfilment_order->reference_id,$this->seller_id,$this->tracking_number,$courier_name,$this->cod,$this));
                        }

                    }
                    if($sellerLocation && $sellerLocation->is_ffc_enabled == 0){
                        // Map different courier name for Salitex seller
                        if ($this->seller_id == env('SALITEX_SELLER_ID')) {
                            $courier_name = config('enum.gtech_erp_courier_name_salitex')[$this->courier_id] ?? config('enum.gtech_erp_courier_name')[$this->courier_id];
                        } else {
                            $courier_name = config('enum.gtech_erp_courier_name')[$this->courier_id];
                        }
                        if($this->seller_id == env('CAMB_SELLER_ID') || $this->seller_id == env('ZEEN_SELLER_ID')){
                            \Log::info("Inside Camb / Zeen Gtech Condition Shipment ID :: ".$this->id);
                            event(new CreateOrderInGtechForCambOrZeenEvent($fulfilment_order->id,$fulfilment_order->reference_id,$this->seller_id,$this->tracking_number,$courier_name,$this->cod,$this));
                        } else if($this->seller_id == env('MTJ_SELLER_ID')){
                            \Log::info("MTJ Gtech Condition Shipment ID :: ".$this->id);
                            event(new CreateOrderInGtechForMtjEvent($fulfilment_order->id,$fulfilment_order->reference_id,$this->seller_id,$this->tracking_number,$courier_name,$this->cod,$this));
                        } else if($this->seller_id == env('MANTO_PK_SELLER_ID')){
                            \Log::info("Manto Gtech Condition Shipment ID :: ".$this->id);
                            event(new CreateOrderInGtechForMantoEvent($fulfilment_order->id,$fulfilment_order->reference_id,$this->seller_id,$this->tracking_number,$courier_name,$this->cod,$this));
                        } else {
                            \Log::info("Else Gtech Condition Shipment ID :: ".$this->id);
                            event(new CheckInventoryAndCreateOrderInGtechEvent($fulfilment_order->id,$fulfilment_order->reference_id,$this->seller_id,$this->tracking_number,$courier_name,$this->cod,$this));
                        }
                    }
				}
            }
        } catch (Exception $e) {

            OrderComments::add($this->order_id, 'GTech POS Online', $e->getMessage().'<br><b>Tracking Number</b> : '.$this->tracking_number, 'Failed', 1);

            activity()
            ->performedOn($this)
            ->causedBy($this->seller_id)
            ->withProperties(['message' => $e->getMessage(), 'dump' => $e->getTraceAsString()])
            ->log('GTech POS Online Request');
        }

          /// Candela Create Draft Order///
          try {
            if (AddOn::candela($this->seller_id)) {
                Log::info("Inside Candela create draft order Job condition");
                Log::info($this->id);
                $fulfilment_order = FulfillmentOrder::where('shipment_id',$this->id)->select('id','reference_id','seller_location_id')->first();
				if($fulfilment_order){
                    $courier_name =  config('enum.candela_erp_courier_name')[$this->courier_id];
                    event(new CreateOrderInCandelaEvent($fulfilment_order->id,$fulfilment_order->reference_id,$this->seller_id,$this->tracking_number,$courier_name,$this->cod,$this));
				}
            }
        } catch (Exception $e) {

            OrderComments::add($this->order_id, 'Candela Post Order Process', $e->getMessage().'<br><b>Tracking Number</b> : '.$this->tracking_number, 'Failed', 1);

            activity()
            ->performedOn($this)
            ->causedBy($this->seller_id)
            ->withProperties(['message' => $e->getMessage(), 'dump' => $e->getTraceAsString()])
            ->log('Candela Post Order Request');
        }

        /// Master Account Create Transfer Order \\\
        try {
            if (AddOn::masterAccountCreateTransferOrder($this->seller_id)) {
                $fulfillment_order = FulfillmentOrder::where('shipment_id',$this->id)->first();
				
                if($fulfillment_order && strpos(Setting::masterInternationalAccountLocationIds($this->seller_id), $fulfillment_order->seller_location_id) !== false ){
                    MasterAccountCreateTransferOrderJob::dispatch($fulfillment_order);
				}
            }
        } catch (Exception $e) {

            OrderComments::add($this->order_id, 'Transfer Order Creation Process', $e->getMessage().'<br><b>Tracking Number</b> : '.$this->tracking_number, 'Failed', 1);

            activity()
            ->performedOn($this)
            ->causedBy($this->seller_id)
            ->withProperties(['message' => $e->getMessage(), 'dump' => $e->getTraceAsString()])
            ->log('Transfer Order Creation Request');
        }

         /// Diners D365 (Send Order Invoice in D365) ///
         try {
            if (AddOn::erpDiners($this->seller_id)) {
                $internal_params = [ 'order_id' => $this->order->id, 'seller_id' => $this->seller_id];
                $params = [ 'shipment' => $this, 'internal_params' => $internal_params ];
                Log::info("Dispatching Diners ERP job");
                DinersERPJob::dispatch('createSalesOrder',$params);
            }
        } catch (Exception $e) {

            OrderComments::add($this->order_id, 'D365 Create Invoice Process', $e->getMessage().'<br><b>Tracking Number</b> : '.$this->tracking_number, 'Failed', 1);

            activity()
            ->performedOn($this)
            ->causedBy($this->seller_id)
            ->withProperties(['message' => $e->getMessage(), 'dump' => $e->getTraceAsString()])
            ->log('D365 Jdot Request');
        }
        
         /// Diners D365 (Send Order Invoice in D365) ///
         try {
            if (AddOn::erpZubaidas($this->seller_id)) {
                $internal_params = [ 'order_id' => $this->order->id, 'seller_id' => $this->seller_id];
                $params = [ 'shipment' => $this, 'internal_params' => $internal_params ];

                Log::info("Dispatching Zubaidas ERP job");
                ZubaidasERPJob::dispatch('createSalesOrder',$params);
            }
        } catch (Exception $e) {

            OrderComments::add($this->order_id, 'ERP Create Invoice Process', $e->getMessage().'<br><b>Tracking Number</b> : '.$this->tracking_number, 'Failed', 1);

            activity()
            ->performedOn($this)
            ->causedBy($this->seller_id)
            ->withProperties(['message' => $e->getMessage(), 'dump' => $e->getTraceAsString()])
            ->log('ERP Zubaidas Request');
        }

    }

    public function fulfillment($message, $courier_loadsheet_id = null, $is_api = false)
    {
        $this->status = config('enum.shipment_status')['DISPATCHED'];
        $this->save();

        $date = Carbon::now()->toDateTimeString();
        $this->shipment_history(config('enum.shipment_status')['DISPATCHED'], $date);
        $this->shipment_courier_history(config('enum.shipment_status')['DISPATCHED'], $date, $message.' | Unity Retail');

        if ($courier_loadsheet_id) {
            OrderComments::add($this->order_id, 'Shipment Loadsheet Process', 'Shipment #'.$this->tracking_number.' <b>Loadsheet</b> has been generated '.($is_api ? ' through <b>API</b>. ' : '.').'<br> <a target="_blank" href="/seller/loadsheet/log_entries/'.$courier_loadsheet_id.'">Click to see details</a>', 'Success', null );
        }

        FulfillmentOrder::dispatchedByShipment($this->id);
    }

    public function storefront_tagging()
    {
        event(new ShipmentStorefrontTaggingEvent($this));
        $this->storefront_sync = 1;
        $this->save();
        return array('error' => 0, 'message' =>  'Success');
    }

    public function shipment_history($status, $date)
    {
        $result = ShipmentHistory::where('shipment_id', $this->id)->where('status', $status);
        if (! $result->exists()) {
            $shipmentHistory = new ShipmentHistory;
            $shipmentHistory->shipment_id = $this->id;
            $shipmentHistory->status = $status;
            $shipmentHistory->status_at = $date;
            $shipmentHistory->save();

            if ($this->type == null) {
                $message = 'Shipment #'.$this->tracking_number.' is now has a <b>'.$status.'</b> status';
                OrderComments::add($this->order_id, 'Shipment Tracking Process', $message, 'Success', null );
                
                CustomerIntimationService::execute($this->seller_id, [
                    'event' => 'shipment_status',
                    'status' => $status,
                    'marketplace_reference_id' => $this->order->marketplace_reference_id,
                    'tracking_number' => $this->tracking_number,
                    'order_id' => $this->order_id,
                    'courier_id' => $this->courier_id,
                    'customer_email' => $this->order->customer_email,
                    'customer_name' => $this->order->customer_name,
                    'customer_number' => $this->order->customer_number,
                    'shipment_id' => $this->id
                ]);


                if ($status == config('enum.shipment_status')['CANCELLED']) {
                    CancelFulfillment::dispatch($this->id)->onQueue('cancel_fulfillment');

                } elseif($status == config('enum.shipment_status')['RETURNED'] && !Setting::returnReceivingMode($this->seller_id)) {

                    $this->status = config('enum.shipment_status')['RETURNED_RECEIVED'];
                    $this->save();
                    $this->shipment_history(config('enum.shipment_status')['RETURNED_RECEIVED'], now());
                    if (AddOn::fbr($this->seller_id) && Setting::fbr($this->seller_id)) {
                        FBR::sendReturnPosting($this);
                    }
                }

                $this->removeStock($status, $this->items, $this->seller_id);
            }

            if ( !in_array($status, [config('enum.shipment_status')['RETURNED_RECEIVED']])) {
                if(env('JDOT_MAGENTO_SELLER_ID') != $this->seller_id ){
                    event(new ShipmentStatusChangeEvent($this->id,$status,$shipmentHistory->id));
                    ShipmentHistory::where('id', $shipmentHistory->id)->update(['storefront_sync' => 1]);
                }
            }

            if ( ($status == config('enum.shipment_status')['READY_FOR_DISPATCH'] ) && env('JDOT_MAGENTO_SELLER_ID') == $this->seller_id ) {
                if (AddOn::erpJdotTemp($this->seller_id)) {
                    $internal_params = [ 'order_id' => $this->order->id, 'seller_id' => $this->seller_id];
                    $params = [ 'shipment' => $this, 'internal_params' => $internal_params ];
                    Log::info("Jdot Dispatching job");
                    JdotERPJob::dispatch('createSalesOrderService', $params);
                }
            }

            // Technosys ERP integration
            if ( $status == config('enum.shipment_status')['READY_FOR_DISPATCH'] ) {
                try {
                    if (AddOn::erpTechnosys($this->seller_id)) {
                        $internal_params = ['order_id' => $this->order->id, 'seller_id' => $this->seller_id];
                        $params = ['shipment' => $this, 'internal_params' => $internal_params];
                        Log::info("Dispatching Technosys ERP job");
                        TechnosysERPJob::dispatch('createSalesOrder', $params);
                    }
                } catch (Exception $e) {
                    OrderComments::add($this->order_id, 'Technosys ERP Sync Process', $e->getMessage() . '<br><b>Tracking Number</b>: ' . $this->tracking_number, 'Failed', 1);
                    activity()
                        ->performedOn($this)
                        ->causedBy($this->seller_id)
                        ->withProperties(['message' => $e->getMessage(), 'dump' => $e->getTraceAsString()])
                        ->log('Technosys ERP Request');
                }
            }

        }
    }

    public function shipment_courier_history($status, $date, $message = '-')
    {
        $result = ShipmentCourierHistory::where('shipment_id', $this->id)->where('status', $status);
        if (! $result->exists()) {
            $shipmentHistory = new ShipmentCourierHistory;
            $shipmentHistory->shipment_id = $this->id;
            $shipmentHistory->status = $status;
            $shipmentHistory->message = $message;
            $shipmentHistory->status_at = $date;
            $shipmentHistory->save();
        }
    }

    public function cancel()
    {
        FulfillmentOrder::closedByShipment($this->id);
        FulfillmentOrder::revertShippingFeeIfApplicableByShipment($this->id);

        $message = '';
        $error = 0;
        $status = [config('enum.shipment_status')['DELIVERED'], config('enum.shipment_status')['CANCELLED'], config('enum.shipment_status')['RETURNED']];

        if (in_array($this->status, $status)) {
            $error = 1;
            $message = 'Shipment #'.$this->tracking_number.' can\'t be cancelled';
        } else {
            $courier_portal_message = $this->courier_portal_cancel();  // First cancel on courier portal

            // Setting shipment status to cancelled
            $this->status = config('enum.shipment_status')['CANCELLED'];
            $this->save();

            if ($this->type == null) {

                if ($this->order->shipping_fee_charged == $this->tracking_number) {
                    $this->order->shipping_fee_charged = 0;
                }

                /////// Resetting location
                $this->order->seller_location_id = 1;
                $this->order->save();
        
                // Resetting all order item status to Pending
                $sku_count = 0;
                foreach ($this->items as $value) {
                    $value->order_item->status = config('enum.item_status')['PENDING'];
                    $value->order_item->save();
                    $sku_count++;
                }

                // changing order status
                $this->order->update_status_according_to_items();

                $message = 'Shipment #'.$this->tracking_number.' successfully cancelled in <b> unity-retail </b> having '.$sku_count.' SKU ';
                $message .= $courier_portal_message;  // concatenate courier portal return message
            } else{

                $sku_count = 0;
                foreach ($this->items as $value) {
                  
                    $sku_count++;
                }
                $message = 'Reverse Shipment #'.$this->tracking_number.' successfully cancelled in <b> unity-retail </b> having '.$sku_count.' SKU ';
                $message .= $courier_portal_message;  // concatenate courier portal return message
            }
        }

        if ($this->type == null) {
            $orderComments = new OrderComments;
            $orderComments->order_id = $this->order->id;
            $orderComments->key = 'Shipment Cancellation Process';
            $orderComments->value = $message;
            $orderComments->run_by = (session()->has('user') ? session('user')->id : null);
            $orderComments->status = ($error == 0 ? 'Success' : 'Failed');
            $orderComments->save();
        } else{
            $orderComments = new OrderComments;
            $orderComments->order_id = $this->order->id;
            $orderComments->key = 'Reverse Shipment Cancellation Process';
            $orderComments->value = $message;
            $orderComments->run_by = (session()->has('user') ? session('user')->id : null);
            $orderComments->status = ($error == 0 ? 'Success' : 'Failed');
            $orderComments->save();
        }


        if (AddOn::wallet($this->seller_id)) {

            /// if shipment is already dispatched then don't refund into wallet 
            $temp_statuses = array_except( config('enum.shipment_status'), ['BOOKED', 'READY_FOR_DISPATCH']);
            
            if (! ShipmentHistory::where('shipment_id', $this->id)->whereIn('status', $temp_statuses )->exists()) {
                $transaction = WalletTransaction::where('cn',$this->tracking_number)->whereOrderId($this->order_id)->whereSellerId($this->seller_id)->first();
    
                if ($transaction && $transaction->amount_charged) {
                    
                    WalletTransaction::refund($transaction->amount_charged, $this->order_id, $this->seller_id, $this->tracking_number);
                    $message .= ' | Booking Charges <b> REFUNDED</b>';
                }

            } else {
                $message .= ' | Booking Charges <b> <span style="color:red"> NOT </span></b> REFUNDED, shipment is already Dispatched';
            }
        }

        $this->shipment_history(config('enum.shipment_status')['CANCELLED'], Carbon::now()->toDateTimeString());

        return ['error' => $error, 'message' => $message];
    }

    private function courier_portal_cancel()
    {
        if ($this->courier_id == 1) {
            return ' | <span style="color:red"> Seller own courier doesn\'t have any courier portal </span>';
        } elseif ($this->courier_id == 2) {
            return ' | <span style="color:red"> Courier Portal cancellation process is not available for TCS courier </span>';

        }  elseif ($this->courier_id == 4) {

            if($this->shipment_booking_courier->value === "universal") {
                $username = AdminSetting::where('key','mnp_username')->value('value');
                $password = AdminSetting::where('key','mnp_password')->value('value');
    
                if ($username && $password) {
                    $mnp = new SellerCourierMNP;
                    $mnp->username = $username;
                    $mnp->password = $password;
                    $mnp->seller_id = $this->seller_id;
                    $mnp->default_pickup_address_id = null;
                } else {
                    return ' | <span style="color:red"> Failed to cancel on MnP courier portal, MnP Universal Account Credentials not found , please contact unity team for this</span>';
                } 
            } else {
                $mnp = SellerCourierMNP::where('seller_id', $this->seller_id)->where('courier_id', $this->courier_id)->first();
            }

            if ($mnp) {

                if ($this->seller_location_id) {
                    $location_id = SellerCourierLocation::where('seller_location_id',$this->seller_location_id)->where('courier_id',$this->courier_id)->value('courier_reference_id');
                    if (!$location_id) {
                        return ' | <span style="color:red"> Failed to cancel on MnP courier portal, MnP Location ID not found in Seller Location</span>';
                    }
                } else {
                    $location_id = $mnp->default_pickup_address_id;
                    if (! $location_id) {
                        return ' | <span style="color:red"> Failed to cancel on MnP courier portal, MnP Location ID not found in Default Location</span>';
                    }
                }

                $response = $mnp->cancel($this->tracking_number, $location_id);

                if ($response['error'] == 1) {
                    return ' | <span style="color:red"> Failed to cancel on MnP courier portal, '.$response['message'].'</span>';
                } else {
                    return ' | <b> Successfully cancel on MnP courier portal </b>';
                }
            } else {
                return ' | <span style="color:red"> Failed to cancel on MnP courier portal, Credentials not found in couriers setting</span>';
            }
        } elseif ($this->courier_id == 5) {

            if($this->shipment_booking_courier->value === "universal") {
                $api_key = AdminSetting::where('key','lcs_api_key')->value('value');
                $api_password = AdminSetting::where('key','lcs_api_password')->value('value');
    
                if ($api_key && $api_password) {
                    $lcs = new SellerCourierLCS;
                    $lcs->api_key = $api_key;
                    $lcs->api_password = $api_password;
                    $lcs->seller_id = $this->seller_id;
                } else {
                    return ' | <span style="color:red"> Failed to cancel on LCS courier portal, LCS Universal Account Credentials not found , please contact unity team for this</span>';
                } 
            } else {
                $lcs = SellerCourierLCS::whereSellerId($this->seller_id)->first();
            }

            if ($lcs) {

                $response = $lcs->cancel($this->tracking_number);

                if ($response['error'] == 1) {
                    return ' | <span style="color:red"> Failed to cancel on LCS courier portal, '.$response['message'].'</span>';
                } else {
                    return ' | <b> Successfully cancel on LCS courier portal </b>';
                }
            } else {
                return ' | <span style="color:red"> Failed to cancel on LCS courier portal, Credentials not found in couriers setting</span>';
            }
        } elseif ($this->courier_id == 7) {

            if($this->shipment_booking_courier->value === "universal") {
                $username = AdminSetting::where('key','call_courier_username')->value('value');

                if ($username) {
                    $seller = Seller::where('id',$this->seller_id)->first(['company_name','full_name']);
                    $account_title = ( $seller->company_name ? $seller->company_name : $seller->full_name );
                    $callcourier = new SellerCourierCallCourier;
                    $callcourier->username = $username;
                    $callcourier->account_title = $account_title;
                } else {
                    return ' | <span style="color:red"> Failed to cancel on Call Courier courier portal, Call Courier Universal Account Credentials not found , please contact unity team for this</span>';
                } 
            } else {
                $callcourier = SellerCourierCallCourier::where('seller_id', $this->seller_id)->first();
            }


            if ($callcourier) {

                $response = $callcourier->cancel($this->tracking_number);

                if ($response['error'] == 1) {
                    return ' | <span style="color:red"> Failed to cancel on Call Courier courier portal, '.$response['message'].'</span>';
                } else {
                    return ' | <b> Successfully cancel on Call Courier courier portal </b>';
                }
            } else {
                return ' | <span style="color:red"> Failed to cancel on  Call Courier courier portal, Credentials not found in couriers setting</span>';
            }


        } elseif ($this->courier_id == 8) {

            if($this->shipment_booking_courier->value === "universal") {
                $authorization_key = AdminSetting::where('key','trax_authorization_key')->value('value');
    
                if ($authorization_key) {
                    $trax_new = new SellerCourierTraxNew;
                    $trax_new->authorization_key = $authorization_key;
                    $trax_new->seller_id = $this->seller_id;
                } else {
                    return ' | <span style="color:red"> Failed to cancel on Trax courier portal, Trax Universal Account Credentials not found , please contact unity team for this</span>';
                } 
            } else {
                $trax_new = SellerCourierTraxNew::whereSellerId($this->seller_id)->first();
            }

            if ($trax_new) {

                $response = $trax_new->cancel($this->tracking_number);

                if ($response['error'] == 1) {
                    return ' | <span style="color:red"> Failed to cancel on Trax courier portal, '.$response['message'].'</span>';
                } else {
                    return ' | <b> Successfully cancel on Trax courier portal </b>';
                }
            } else {
                return ' | <span style="color:red"> Failed to cancel on Trax courier portal, Credentials not found in couriers setting</span>';
            }
        } elseif ($this->courier_id == 9) {
            return ' | <span style="color:red"> Courier Portal cancellation process is not available for Pakistan Post Courier </span>';
        } elseif ($this->courier_id == 10) {

            if($this->shipment_booking_courier->value === "universal") {
                $account_no = AdminSetting::where('key','blue_ex_account_no')->value('value');
                $user_id = AdminSetting::where('key','blue_ex_user_id')->value('value');
                $password = AdminSetting::where('key','blue_ex_password')->value('value');
    
                if ($account_no && $user_id && $password) {
                    $blueEx = new SellerCourierBlueEx;
                    $blueEx->account_no = $account_no;
                    $blueEx->user_id = $user_id;
                    $blueEx->password = $password;
                    $blueEx->seller_id = $this->seller_id;
                } else {
                    return ' | <span style="color:red"> Failed to cancel on Blue Ex courier portal, Blue Ex Universal Account Credentials not found , please contact unity team for this</span>';
                } 
            } else {
                $blueEx = SellerCourierBlueEx::whereSellerId($this->seller_id)->first();
            }

            if ($blueEx) {

                $response = $blueEx->cancel($this->tracking_number);

                if ($response['error'] == 1) {
                    return ' | <span style="color:red"> Failed to cancel on Blue-Ex courier portal, '.$response['message'].'</span>';
                } else {
                    return ' | <b> Successfully cancel on Blue-Ex courier portal </b>';
                }
            } else {
                return ' | <span style="color:red"> Failed to cancel on Blue-Ex courier portal, Credentials not found in couriers setting</span>';
            }
        } elseif ($this->courier_id == 11) {

            if($this->shipment_booking_courier->value === "universal") {
                $login_id = AdminSetting::where('key','rider_login_id')->value('value');
                $api_key = AdminSetting::where('key','rider_api_key')->value('value');
    
                if ($login_id && $api_key) {
                    $rider = new SellerCourierRider;
                    $rider->login_id = $login_id;
                    $rider->api_key = $api_key;
                    $rider->seller_id = $this->seller_id;
                } else {
                    return ' | <span style="color:red"> Failed to cancel on Rider courier portal, Rider Universal Account Credentials not found , please contact unity team for this</span>';
                } 
            } else {
                $rider = SellerCourierRider::whereSellerId($this->seller_id)->first();
            }

            if ($rider) {

                $response = $rider->cancel($this->tracking_number);

                if ($response['error'] == 1) {
                    return ' | <span style="color:red"> Failed to cancel on Rider courier portal, '.$response['message'].'</span>';
                } else {
                    return ' | <b> Successfully cancel on Rider courier portal </b>';
                }
            } else {
                return ' | <span style="color:red"> Failed to cancel on Rider courier portal, Credentials not found in couriers setting</span>';
            }
        } elseif ($this->courier_id == 12) {

            return ' | <span style="color:red"> Courier Portal cancellation process is not available for TM Delivery Express courier </span>';

        } elseif ($this->courier_id == 13) {
            return ' | <span style="color:red"> Courier Portal cancellation process is not available for TCS courier </span>';
        } elseif ($this->courier_id == 14) {
            return ' | <span style="color:red"> Courier Portal cancellation process is not available for LCS-UAE courier </span>';

        }  elseif ($this->courier_id == 15) {

            if($this->shipment_booking_courier->value === "universal") {
                $vendor_id = AdminSetting::where('key','swyft_vendor_id')->value('value');
                $vendor_secret = AdminSetting::where('key','swyft_vendor_secret')->value('value');
    
                if ($vendor_id && $vendor_secret) {
                    $swyft = new SellerCourierSwyft;
                    $swyft->vendor_id = $vendor_id;
                    $swyft->vendor_secret = $vendor_secret;
                    $swyft->seller_id = $this->seller_id;
                } else {
                    return ' | <span style="color:red"> Failed to cancel on Swyft courier portal, Swyft Universal Account Credentials not found , please contact unity team for this</span>';
                } 
            } else {
                $swyft = SellerCourierSwyft::whereSellerId($this->seller_id)->first();
            }
            

            if ($swyft) {

                $response = $swyft->cancel($this->tracking_number);

                if ($response['error'] == 1) {
                    return ' | <span style="color:red"> Failed to cancel on swyft courier portal, <b>'.$response['message'].'</b></span>';
                } else {
                    return ' | '.$response['message'];
                }
            } else {
                return ' | <span style="color:red"> Failed to cancel on swyft courier portal, Credentials not found in couriers setting</span>';
            }
        } elseif ($this->courier_id == 17) {
            return ' | <span style="color:red"> Courier Portal cancellation process is not available for DHL Express courier </span>';
        } elseif ($this->courier_id == 18) {
            $courierx = SellerCourierCourierX::whereSellerId($this->seller_id);

            if ($courierx->exists()) {
                $courierx = $courierx->first();

                $response = $courierx->cancel($this->tracking_number);

                if ($response['error'] == 1) {
                    return ' | <span style="color:red"> Failed to cancel on CourierX portal, '.$response['message'].'</span>';
                } else {
                    return ' | <b> Successfully cancelled on CourierX portal </b>';
                }
            } else {
                return ' | <span style="color:red"> Failed to cancel on CourierX courier portal, Credentials not found in couriers setting</span>';
            }
        } elseif ($this->courier_id == 19) {

            if($this->shipment_booking_courier->value === "universal") {
                $api_key = AdminSetting::where('key','movex_api_key')->value('value');
    
                if ($api_key) {
                    $movex = new SellerCourierMoveX;
                    $movex->api_key = $api_key;
                    $movex->seller_id = $this->seller_id;
                } else {
                    return ' | <span style="color:red"> Failed to cancel on Movex courier portal, Movex Universal Account Credentials not found , please contact unity team for this</span>';
                } 
            } else {
                $movex = SellerCourierMoveX::whereSellerId($this->seller_id)->first();
            }

            if ($movex) {

                $response = $movex->cancel($this->tracking_number);

                if ($response['error'] == 1) {
                    return ' | <span style="color:red"> Failed to cancel on MoveX portal, '.$response['message'].'</span>';
                } else {
                    return ' | <b> Successfully cancelled on MoveX portal </b>';
                }
            } else {
                return ' | <span style="color:red"> Failed to cancel on MoveX courier portal, Credentials not found in couriers setting</span>';
            }
        } elseif ($this->courier_id == 20) {

            if($this->shipment_booking_courier->value === "universal") {
                $username = AdminSetting::where('key','bykea_username')->value('value');
                $password = AdminSetting::where('key','bykea_password')->value('value');
    
                if ($username && $password) {
                    $bykea = new SellerCourierBykea;
                    $bykea->username = $username;
                    $bykea->password = $password;
                    $bykea->seller_id = $this->seller_id;
                } else {
                    return ' | <span style="color:red"> Failed to cancel on Bykea courier portal, Bykea Universal Account Credentials not found , please contact unity team for this</span>';
                } 
            } else {
                $bykea = SellerCourierBykea::whereSellerId($this->seller_id)->first();
            }


            if ($bykea) {

                $response = $bykea->cancel($this);

                if ($response['error'] == 1) {
                    return ' | <span style="color:red"> Failed to cancel on Bykea courier portal, '.$response['message'].'</span>';
                } else {
                    return ' | <b> Successfully cancel on Bykea courier portal </b>';
                }
            } else {
                return ' | <span style="color:red"> Failed to cancel on Bykea courier portal, Credentials not found in couriers setting</span>';
            }
            
        } elseif ($this->courier_id == 24) {

            $pandago = SellerCourierPandaGo::whereSellerId($this->seller_id);

            if ($pandago->exists()) {
                $pandago = $pandago->first();

                $response = $pandago->cancel($this->tracking_number);
                
                if ($response['error'] == 1) {
                    return ' | <span style="color:red"> Failed to cancel on PandaGo courier portal, '.$response['message'].'</span>';
                } else {
                    return ' | <b> Successfully cancel on PandaGo courier portal </b>';
                }

            } else {
                return ' | <span style="color:red"> Failed to cancel on PandaGo courier portal, Credentials not found in couriers setting</span>';
            }
            
        } elseif ($this->courier_id == 27) {

            if($this->shipment_booking_courier->value === "universal") {
                $account_id = AdminSetting::where('key','forrun_account_id')->value('value');
                $api_token = AdminSetting::where('key','forrun_api_token')->value('value');
    
                if ($account_id) {
                    $forrun = new SellerCourierForrun;
                    $forrun->account_id = $account_id;
                    $forrun->api_token = $api_token;
                    $forrun->seller_id = $this->seller_id;
                } else {
                    return ' | <span style="color:red"> Failed to cancel on Forrun courier portal, Forrun Universal Account Credentials not found , please contact unity team for this</span>';
                } 
            } else {
                $forrun = SellerCourierForrun::whereSellerId($this->seller_id)->first();
            }

            if ($forrun) {

                $response = $forrun->cancel($this->tracking_number);

                if ($response['error'] == 1) {
                    return ' | <span style="color:red"> Failed to cancel on Forrun courier portal, '.$response['message'].'</span>';
                } else {
                    return ' | <b> Successfully cancel on Forrun courier portal </b>';
                }
            } else {
                return ' | <span style="color:red"> Failed to cancel on Forrun courier portal, Credentials not found in couriers setting</span>';
            }
        }  elseif ($this->courier_id == 28) {

            if($this->shipment_booking_courier->value === "universal") {
                $api_token = AdminSetting::where('key','postex_api_token')->value('value');
                $authorization = AdminSetting::where('key','postex_api_authorization')->value('value');
                if ($authorization) {
                    $postex = new SellerCourierPostEx;
                    $postex->token = $api_token;
                    $postex->authorization = $authorization;
                    $postex->seller_id = $this->seller_id;
                } else {
                    return ' | <span style="color:red"> Failed to cancel on PostEx courier portal, PostEx Universal Account Credentials not found , please contact unity team for this</span>';
                } 
            } else {
                $postex = SellerCourierPostEx::whereSellerId($this->seller_id)->first();
            }

            if ($postex) {

                $response = $postex->cancel($this->tracking_number);

                if ($response['error'] == 1) {
                    return ' | <span style="color:red"> Failed to cancel on PostEx courier portal, '.$response['message'].'</span>';
                } else {
                    return ' | <b> Successfully cancel on PostEx courier portal </b>';
                }
            } else {
                return ' | <span style="color:red"> Failed to cancel on PostEx courier portal, Credentials not found in couriers setting</span>';
            }
        } elseif($this->courier_id == 29){
            $stallion_creds = null;
            $stallion_creds = StallionHelper::getCreds($this->seller_id);
            if(isset($stallion_creds)){
                $response = (new Stallion($this->seller_id))->cancellation($this->tracking_number);

                if ($response['error'] == 1) {
                    return ' | <span style="color:red"> Failed to cancel on Stallion courier portal, '.$response['message'].'</span>';
                } else {
                    return ' | <b> Successfully cancel on Stallion courier portal </b>';
                }
            } else {
                return ' | <span style="color:red"> Failed to cancel on Stallion courier portal, Credentials not found in couriers setting</span>';
            }
            

        } elseif ($this->courier_id == 31) {

            if($this->shipment_booking_courier->value === "universal") {
                $api_key = AdminSetting::where('key','insta_api_key')->value('value');
    
                if ($api_key) {
                    $insta = new SellerCourierInsta;
                    $insta->api_key = $api_key;
                    $insta->seller_id = $this->seller_id;
                } else {
                    return ' | <span style="color:red"> Failed to cancel on Insta courier portal, Insta Universal Account Credentials not found , please contact unity team for this</span>';
                } 
            } else {
                $insta = SellerCourierInsta::whereSellerId($this->seller_id)->first();
            }

            if ($insta) {

                $response = $insta->cancel($this->tracking_number);

                if ($response['error'] == 1) {
                    return ' | <span style="color:red"> Failed to cancel on Insta courier portal, '.$response['message'].'</span>';
                } else {
                    return ' | <b> Successfully cancel on Insta courier portal </b>';
                }
            } else {
                return ' | <span style="color:red"> Failed to cancel on Insta courier portal, Credentials not found in couriers setting</span>';
            }
        }  elseif ($this->courier_id == 35) {

            if($this->shipment_booking_courier->value === "universal") {
                $api_key = AdminSetting::where('key','lcs_merchant_api_key')->value('value');
                $api_password = AdminSetting::where('key','lcs_merchant_api_password')->value('value');
    
                if ($api_key && $api_password) {
                    $lcs = new SellerCourierLCSMerchant();
                    $lcs->api_key = $api_key;
                    $lcs->api_password = $api_password;
                    $lcs->seller_id = $this->seller_id;
                } else {
                    return ' | <span style="color:red"> Failed to cancel on LCS Merchant courier portal, LCS Merchant Universal Account Credentials not found , please contact unity team for this</span>';
                } 
            } else {
                $lcs = SellerCourierLCSMerchant::whereSellerId($this->seller_id)->first();
            }

            if ($lcs) {

                $response = $lcs->cancel($this->tracking_number);

                if ($response['error'] == 1) {
                    return ' | <span style="color:red"> Failed to cancel on LCS Merchant courier portal, '.$response['message'].'</span>';
                } else {
                    return ' | <b> Successfully cancel on LCS Merchant courier portal </b>';
                }
            } else {
                return ' | <span style="color:red"> Failed to cancel on LCS Merchant courier portal, Credentials not found in couriers setting</span>';
            }
        } elseif ($this->courier_id == 36) {

            $fly_courier = SellerCourierFlyCourier::whereSellerId($this->seller_id)->first();

            if ($fly_courier) {

                $response = $fly_courier->cancel($this->tracking_number);

                if ($response['error'] == 1) {
                    return ' | <span style="color:red"> Failed to cancel on Fly Courier portal, '.$response['message'].'</span>';
                } else {
                    return ' | <b> Successfully cancel on Fly Courier portal </b>';
                }
            } else {
                return ' | <span style="color:red"> Failed to cancel on Fly Courier portal, Credentials not found in couriers setting</span>';
            }

        } elseif ($this->courier_id == 39) {

            if($this->shipment_booking_courier->value === "universal") {
                $auth_key = AdminSetting::where('key','tqs_auth_key')->value('value');
    
                if ($auth_key) {

                    $tqs = new SellerCourierTQS();
                    $tqs->auth_key = $auth_key;
                    $tqs->seller_id = $this->seller_id;

                } else {
                    return ' | <span style="color:red"> Failed to cancel on The Quick Service (TQS) courier portal, The Quick Service (TQS) Universal Account Credentials not found , please contact unity team for this</span>';
                } 

            } else {
                $tqs = SellerCourierTQS::whereSellerId($this->seller_id)->first();
            }

            if ($tqs) {
                $response = $tqs->cancel($this->tracking_number);

                if ($response['error'] == 1) {
                    return ' | <span style="color:red"> Failed to cancel on The Quick Service (TQS) courier portal, '.$response['message'].'</span>';
                } else {
                    return ' | <b> Successfully cancel on The Quick Service (TQS) courier portal </b>';
                }
            } else {
                return ' | <span style="color:red"> Failed to cancel on The Quick Service (TQS) courier portal, Credentials not found in couriers setting</span>';
            }

        } elseif ($this->courier_id == 40) {

            $dex = SellerCourierDEX::where('seller_id', $this->seller_id)->first();

            if ($dex) {
                $response = $dex->cancel($this);
                if ($response['error'] == 1) {
                    return ' | <span style="color:red"> Failed to cancel on DEX portal, '.$response['message'].'</span>';
                } else {
                    return ' | <b> Successfully cancelled on DEX portal </b>';
                }
            } else {
                return ' | <span style="color:red"> Failed to cancel on DEX portal, Credentials not found in couriers setting</span>';
            }

        } elseif ($this->courier_id == 41) {

            $city_mail = SellerCourierCityMail::whereSellerId($this->seller_id)->first();

            if ($city_mail) {

                $response = $city_mail->cancel($this->tracking_number);

                if ($response['error'] == 1) {
                    return ' | <span style="color:red"> Failed to cancel on City Mail portal, '.$response['message'].'</span>';
                } else {
                    return ' | <b> Successfully cancel on City Mail portal </b>';
                }
            } else {
                return ' | <span style="color:red"> Failed to cancel on City Mail portal, Credentials not found in couriers setting</span>';
            }

        } else {
            return ' | <span style="color:red"> Courier not found #'.$this->courier_id.'</span>';
        }
    }

    public static function reverse($temp, $seller_id)
    {
        /////////// Location is Enable ? in Settings //////////////
        $location = Setting::location($seller_id);

        /////////// Checking that this seller has permission from unity team to use this feature ///////////
        $add_on_location = AddOn::location($seller_id);

        $rma_shipment = RMARequest::whereShipmentId($temp['shipment_id'])->first();

        // $rma_items = explode(',',$rma_shipment->order_items_id);

        $rma_items = RMAItems::where('rma_requests_id', $rma_shipment->id)->get();
        // dd($rma_items);

        $order_item = [];

        $weight = 0;
        $quantity = 0;

        // return $rma_items;

        foreach ($rma_items as $item) {
            $quantity += intval($item->quantity);
            $weight += OrderItem::whereId($item->order_items_id)->value('weight');
            array_push($order_item, $item->order_items_id);
        }

        if($weight < 0.1){
            $weight = 0.1;
        }
        
        $warehouse = $temp['warehouse'];
        $st = Setting::whereSellerId($seller_id)->where('key',config('enum.settings')['REVERSECOURIER'])->first();
        if(isset($st) && $st->value != 0){
            $courier_id = $st->value;
            $temp['courier_id'] = $st->value;
        } else{
            $courier_id = $temp['courier_id'];
        }
        
        $courier_service = '';
        $courier_service_name = '';
        if ($courier_id == 4) {
            $courier_service = 'Reversal COD';
            $courier_service_name = 'Reversal COD';
        } elseif ($courier_id == 5) {
            $courier_service = 'Overnight';
            $courier_service_name = 'Overnight';
        } elseif($courier_id == 7){
            $courier_service = 28;
            $courier_service_name = 'REVERSE PICKUP';
        } elseif ($courier_id == 8) {
            $courier_service = 1;
            $courier_service_name = 'Reverse';
        }
        elseif($courier_id == 11)
        {
            $courier_service = '3';
            $courier_service_name = 'Reverse Pickup';
        }
        elseif($courier_id == 19)
        {
            $courier_service = 'reverse';
            $courier_service_name = 'Reverse Pickup';
        }

        ////// Destination City ////

        $city_id = City::where('name', $rma_shipment->shipment->destination_city)->value('id');

        $destination_city = CourierCity::where('courier_id', $courier_id)->where('city_id', $city_id);
        if (! $destination_city->exists()) {
            return ['error' => 1, 'message' => ' Selected Courier Not Provide their services in selected city'];
        }
        $origin_city = $destination_city;


        if ($warehouse && $warehouse != 'Default' && $add_on_location && $location) {
            $add_on_omni_return = AddOn::omniReturn($seller_id);
            if($add_on_omni_return){
                $seller_location = SellerLocation::where('seller_id',$seller_id)->where('id',$rma_shipment->shipment->seller_return_location_id)->first();
                if(isset($seller_location)){
                    $temp['warehouse'] = $rma_shipment->shipment->seller_return_location_id;
                } else{
                    $seller_location = SellerLocation::find($warehouse);
                }
            } else{
                $seller_location = SellerLocation::find($warehouse);
            }


            if (!$seller_location) {
                $warehouse = 'Default';
            } else {
                $default_pickup_address = ['contact_person_name' => $seller_location->location_name, 'contact_person_phone_number' => $seller_location->phone, 'address' => $seller_location->address, 'postal_code' => $seller_location->postal_code];

                $seller_city = CourierCity::where('courier_id', $courier_id)->where('city_id', $seller_location->city)->value('courier_city_name');

                $seller_city_code = CourierCity::where('courier_id', $courier_id)->where('city_id', $seller_location->city)->value('courier_city_code');
            }


        } else{
            $warehouse = 'Default';
        }
        

        

        if ($warehouse == 'Default') {
            $seller_location = SellerLocation::where('seller_id', $seller_id)->where('is_default', 1);
            if ($seller_location->exists()) {
                $seller_location = $seller_location->first();
            } else {
                $seller_location = SellerLocation::where('seller_id', $seller_id)->first();
            }
            $warehouse = $seller_location->id;

            $default_pickup_address = ['contact_person_name' => $seller_location->location_name, 'contact_person_phone_number' => $seller_location->phone, 'address' => $seller_location->address, 'postal_code' => $seller_location->postal_code];


            
            // $add_on_omni_return = AddOn::omniReturn($seller_id);
            // if($add_on_omni_return){
                
            //     $seller_return_location = SellerLocation::where('seller_id',$seller_id)->where('id',$$rma_shipment->shipment->seller_return_location_id)->first();
            //     if($seller_return_location){

            //         $default_pickup_address = ['contact_person_name' => $seller_return_location->location_name, 'contact_person_phone_number' => $seller_return_location->phone, 'address' => $seller_return_location->address];

            //     } else{
            //         $default_pickup_address = ['contact_person_name' => $seller_location->location_name, 'contact_person_phone_number' => $seller_location->phone, 'address' => $seller_location->address];
            //     }
                
                
            // } else{
            //     $default_pickup_address = ['contact_person_name' => $seller_location->location_name, 'contact_person_phone_number' => $seller_location->phone, 'address' => $seller_location->address];
            // }



            $seller_city = CourierCity::where('courier_id', $courier_id)->where('city_id', $seller_location->city)->value('courier_city_name');
            $seller_city_code = CourierCity::where('courier_id', $courier_id)->where('city_id', $seller_location->city)->value('courier_city_code');
        }

        if ($courier_id == 8 || $courier_id == 11 || $courier_id == 4 || $courier_id == 23 || $courier_id == 19 || $courier_id == 5) {
        
            $order = (object) array('customer_name' => $rma_shipment->shipment->order->customer_name,
                                    'shipping_address' => $rma_shipment->shipment->order->shipping_address,
                                    'customer_number' => $rma_shipment->shipment->order->customer_number,
                                    'seller_id' => $seller_id,
                                    'special_instruction' => $rma_shipment->shipment->order->special_instruction,
                                    'order_id' => $rma_shipment->shipment->order->id,
                                    'customer_lat' => $rma_shipment->shipment->order->customer_lat,
                                    'customer_long' => $rma_shipment->shipment->order->customer_long
                                    );

            if($courier_id == 11 || $courier_id == 4){
                $default_pickup_address = array('contact_person_name' => $default_pickup_address['contact_person_name'],
                                                'contact_person_phone_number' => $default_pickup_address['contact_person_phone_number'],
                                                'address' =>  $default_pickup_address['address']);
            }

            $data = array( 
                'shipper_order_id' => $rma_shipment->shipment->order->marketplace_reference_id,
                'quantity' => $quantity,
                'weight' => $weight,
                'cod' => null,
                'default_pickup_address' => $default_pickup_address,
                'description' => $rma_shipment->shipment->description,
                'customer_email' => $rma_shipment->shipment->order->customer_email,
                'destination_city' => $destination_city->value('courier_city_code') ,
                'destination_city_name' => $destination_city->value('courier_city_name'),
                'seller_city' => $seller_city ,
                'seller_city_code' => $seller_city_code ,
                'order' => $order,
                'courier_id' => $temp['courier_id'],
                'service' => $courier_service,
                'warehouse' => $temp['warehouse'],
                'remarks' => $rma_shipment->shipment->remarks,
                'old_cn' => $rma_shipment->shipment->tracking_number,
                'reverse' => 1,
            );
        } elseif($courier_id == 10){

            $ship = Shipment::whereId($temp['shipment_id'])->first(['tracking_number','id','seller_id']);
            $ship_data = (object) array(
                'cn' => $ship->tracking_number,
                'id' => $ship->id,
                'seller_id' => $ship->seller_id
            );

            $order = (object) array('customer_name' => $rma_shipment->shipment->order->customer_name,
                                    'shipping_address' => $rma_shipment->shipment->order->shipping_address,
                                    'customer_number' => $rma_shipment->shipment->order->customer_number,
                                    'seller_id' => $seller_id
                                    );

           
            $default_pickup_address = array('contact_person_name' => $default_pickup_address['contact_person_name'],
                                            'contact_person_phone_number' => $default_pickup_address['contact_person_phone_number'],
                                            'address' =>  $default_pickup_address['address']);
          

            $data = array( 
                'shipper_order_id' => $rma_shipment->shipment->order->marketplace_reference_id,
                'quantity' => $quantity ,
                'weight' => $weight,
                'cod' => Null,
                'default_pickup_address' => $default_pickup_address,
                'description' => $rma_shipment->shipment->description,
                'customer_email' => $rma_shipment->shipment->order->customer_email,
                'destination_city' => $destination_city->value('courier_city_code') ,
                'destination_city_name' => $destination_city->value('courier_city_name'),
                'seller_city' => $seller_city ,
                'seller_city_code' => $seller_city_code ,
                'order' => $order,
                'courier_id' => $temp['courier_id'],
                'service' => $courier_service,
                'warehouse' => $temp['warehouse'],
                'remarks' => $rma_shipment->shipment->remarks,
                'ship' => $ship_data
            );


            
        } else {
            $order = (object) ['customer_name' => $default_pickup_address['contact_person_name'],
                                    'shipping_address' => $default_pickup_address['address'],
                                    'customer_number' => $default_pickup_address['contact_person_phone_number'],
                                    'seller_id' => $seller_id,
                                    ];

            $default_pickup_address = ['contact_person_name' => $rma_shipment->shipment->order->customer_name,
                                        'contact_person_phone_number' => $rma_shipment->shipment->order->customer_number,
                                        'address' => $rma_shipment->shipment->order->shipping_address, 
                                        'email' => $rma_shipment->shipment->order->customer_email
                                    ];

            $data = [
                'shipper_order_id' => $rma_shipment->shipment->order->marketplace_reference_id,
                'quantity' => $quantity,
                'weight' => $weight,
                'cod' => null,
                'default_pickup_address' => $default_pickup_address,
                'description' => $rma_shipment->shipment->description,
                'customer_email' => $rma_shipment->shipment->order->customer_email,
                'destination_city' => $seller_city_code,
                'destination_city_name' => $origin_city->value('courier_city_name'),
                'seller_city' => $seller_city,
                'seller_city_code' =>  $destination_city->value('courier_city_code'),
                'order' => $order,
                'courier_id' => $temp['courier_id'],
                'service' => $courier_service,
                'warehouse' => $temp['warehouse'],
                'remarks' => $rma_shipment->shipment->remarks,
            ];
        }


        if ($courier_id == 1) {
            $tracking_number = "RMA-";

            if ($rma_shipment->shipment->tracking_number) {
                $tracking_number .= $rma_shipment->shipment->tracking_number;
            } else {
                $tracking_number .= str_random(7);
            }

            $tracking_number .= '-'.(Shipment::whereOrderId($rma_shipment->shipment->order->id)->count())+1;
            $tracking = true;

        } elseif ($courier_id == 4) {
            $response = SellerCourierMNP::reverseShipped($data);
            if ($response['error'] == 1) {
                return ['error' => 1, 'message' => $response['message']];
            } else {
                $tracking_number = $response['message'];
                $tracking = true;
            }
        } elseif ($courier_id == 5) {
            $response = SellerCourierLCS::shipped($data);
            if ($response['error'] == 1) {
                return ['error' => 1, 'message' => $response['message']];
            } else {
                $tracking_number = $response['message'];
                $tracking = true;
            }
        } elseif ($courier_id == 7) {
            $response = SellerCourierCallCourier::reverseShipped($data, true);
            if ($response['error'] == 1) {
                return ['error' => 1, 'message' => $response['message']];
            } else {
                $tracking_number = $response['message'];
                $tracking = true;
            }
        } elseif ($courier_id == 8) {
            $response = SellerCourierTraxNew::shipped($data, true);
            if ($response['error'] == 1) {
                return ['error' => 1, 'message' => $response['message']];
            } else {
                $tracking_number = $response['message'];
                $tracking = true;
            }
        } else if ($courier_id == 10) {
            $response = SellerCourierBlueEx::reverseShipped($data, true);
            if($response['error'] == 1) {
                return array('error' => 1, 'message' => $response['message']);
            } else {
                $tracking_number = $response['message'];
                $tracking = TRUE;
            }
        } else if ($courier_id == 11) {
            $response = SellerCourierRider::shipped($data, null);
            if ($response['error'] == 1) {
                return ['error' => 1, 'message' => $response['message']];
            } else {
                $tracking_number = $response['message'];
                $tracking = true;
            }
        } else if ($courier_id == 13) {
            $response = SellerCourierTCSNew::reverseShipped($data, $temp['shipment_id']);
            if ($response['error'] == 1) {
                return ['error' => 1, 'message' => $response['message']];
            } else {
                $tracking_number = $response['message'];
                $tracking = true;
            }
        }  else if ($courier_id == 19) {
            $response = SellerCourierMoveX::shipped($data);
            if ($response['error'] == 1) {
                return ['error' => 1, 'message' => $response['message']];
            } else {
                $tracking_number = $response['message'];
                $tracking = true;
            }
        } else if ($courier_id == 23) {
            $response = SellerCourierQuiqup::reverseShipped($data, null);
            if ($response['error'] == 1) {
                return ['error' => 1, 'message' => $response['message']];
            } else {
                $tracking_number = $response['message'];
                $tracking = true;
            }
        } else {
            return ['error' => 1, 'message' => 'Reverse Shipment Not Available for this Courier'];
        }

        // return array('error' => 1, 'message' => 'no error here again');


        if ($tracking) {
            if (session()->has('user')) {
                $data['user_id'] = session('user')->id;
            } else {
                $data['user_id'] = 0;
            }

            $shipment = new self();
            $shipment->seller_id = $order->seller_id;
            $shipment->order_id = $rma_shipment->shipment->order->id;
            $shipment->order_reference_id = $rma_shipment->shipment->order->marketplace_reference_id;
            $shipment->quantity = $quantity;
            $shipment->weight = $weight;
            $shipment->cod = 0;
            $shipment->description = $rma_shipment->shipment->description;
            $shipment->remarks = $rma_shipment->shipment->remarks;
            $shipment->courier_id = $temp['courier_id'];
            $shipment->courier_service_value = $courier_service_name;
            $shipment->tracking_number = $tracking_number;
            $shipment->manual = 0;
            $shipment->status = config('enum.shipment_status')['BOOKED'];
            $shipment->booked_by = $data['user_id'];
            $shipment->type = 'reverse';
            $shipment->customer_name = $rma_shipment->shipment->order->customer_name;
            $shipment->customer_number = $rma_shipment->shipment->order->customer_number;
            $shipment->pickup_address = $rma_shipment->shipment->order->shipping_address;
            $shipment->pickup_city = City::where('id', $city_id)->value('name');

            if (isset($warehouse) && $warehouse != 'Default') {
                $add_on_omni_return = AddOn::omniReturn($seller_id);
                if($add_on_omni_return){
                    $seller_location = SellerLocation::where('seller_id',$seller_id)->where('id',$rma_shipment->shipment->seller_return_location_id)->first();
                    if(isset($seller_location)){
                        $temp['warehouse'] = $rma_shipment->shipment->seller_return_location_id;
                    } else{
                        $seller_location = SellerLocation::find($warehouse);
                    }
                } else{
                    $seller_location = SellerLocation::find($warehouse);
                }
                // $seller_location = SellerLocation::find($warehouse);

                $shipment->seller_location_id = $seller_location->id;

                $shipment->destination_city = City::where('id', $seller_location->city)->value('name');
                $shipment->destination_address = $seller_location->address;
            } else {
                $seller_location = SellerLocation::where('seller_id', $seller_id)->where('is_default', 1);
                if ($seller_location->exists()) {
                    $seller_location = $seller_location->first();
                } else {
                    $seller_location = SellerLocation::where('seller_id', $seller_id)->first();
                }

                $default_pickup_address = ['contact_person_name' => $seller_location->location_name, 'contact_person_phone_number' => $seller_location->phone, 'address' => $seller_location->address];

                $shipment->seller_location_id = $seller_location->id;
                $shipment->destination_city = City::where('id', $seller_location->city)->value('name');
                $shipment->destination_address = $default_pickup_address['address'];
            }

            $shipment->save();

            $rma_shipment->return_shipment_id = $shipment->id;
            $rma_shipment->save();

            if (count($rma_items) > 0) {
                // $order_items_ids = explode(',',$temp['order_items']);
                // array_pop($order_items_ids);
                foreach ($rma_items as $order_items_id) {
                    // if($order_items_id == ''){
                    //     break;
                    // }
                    // $order_items_id = explode('|',$order_items_id);

                    $order_item = OrderItem::where('id', $order_items_id->order_items_id)->first();

                    $shipment_items = new ShipmentItem();
                    $shipment_items->shipment_id = $shipment->id;
                    $shipment_items->order_items_id = $order_items_id->order_items_id;
                    $shipment_items->qty = $order_items_id->quantity;
                    $shipment_items->save();

                    $order_item->status = config('enum.item_status')['RETURNED'];
                    $order_item->save();

                    /////// Adding Order Comments if anything is failed ///////
                    // $orderComments = new OrderComments;
                    // $orderComments->order_id = $order->id;
                    // $orderComments->key = 'Return Merchandise Authorization';
                    // $orderComments->status = 'Failed';
                    // $orderComments->run_by = '1';
                    // $orderComments->value = 'Order Item ('.$order_item->SKU.') is requested to Return.';
                    // $orderComments->save();
                }
            }

            SellerCourier::where('seller_id', $order->seller_id)->where('courier_id', $data['courier_id'])->increment('current_booking');
            $shipment->shipment_history(config('enum.shipment_status')['BOOKED'], Carbon::now()->toDateTimeString());
            return array('error' => 0, 'message' => 'Reverse Shipment Booked | <b>'.$tracking_number.'</b>', 'cn' => $tracking_number,'shipment' => $shipment);
        }
        else {
            return array('error' => 1, 'message' => 'Shipment Not Booked');
        }
    }

    public function removeStock($status, $shipment_items, $seller_id)
    {
        if (!Setting::inventory($seller_id) || !AddOn::inventory($seller_id)) {
			return false;
		}

        if (in_array($status, [config('enum.shipment_status')['DISPATCHED'], config('enum.shipment_status')['CANCELLED']]) ) {

            $fulfillment_order = FulfillmentOrder::whereShipmentId($this->id)->first(['id', 'reference_id']);

            hold_data_for_internal_logger(
                $seller_id,
                ( auth()->check() ? (session()->has('user') ? 'Sub User' : 'Admin') : 'Auto'),
                ( auth()->check() ? (session()->has('user') ? session('user')->id : $this->seller_id ) : 1),
                ( auth()->check() ? (session()->has('user') ? session('user')->full_name : auth()->user()->full_name) : 'System'),
                'Ledger Inventory',
                'stock',
                'Fulfillment Order',
                $fulfillment_order->id,
                $fulfillment_order->reference_id,
                'Shipment '.$status,
                'Shipment has been '.$status.' and Ledger inventory has been deducted.'
            );
            
            foreach ($shipment_items as $key => $item) {
                Inventory::removeStock($seller_id, $item->order_item->id);
            }

        } else {
            return false;
        }
    }


    public function getItemsProductData()
    {
        $error = 0;
        $message = 'success';
        $data = [];

        foreach ($this->items as $item) {

            $temp_product = Product::whereId($item->order_item->product_id)->whereSellerId($this->seller_id)->first(['id','product_name','sale','SKU','barcode']);

            if ($temp_product || $temp_product = Product::where('SKU', $item->order_item->SKU)->where('barcode', $item->order_item->barcode)->whereSellerId($this->seller_id)->first(['id','product_name','sale','SKU','barcode'])) {
                
                $data[] = [
                    "id" => $temp_product->id,
                    "name" => ($temp_product->SKU ? $temp_product->SKU .' | ' : '').($temp_product->barcode ? $temp_product->barcode .' | ' : '').$temp_product->product_name,
                    "unit_price" => $temp_product->sale,
                    "quantity" => $item->order_item->quantity
                ];

            } else {
                $error = 1;
                $message = 'Stock Order Not Created | Shipment item sku ('.$item->order_item->SKU.') not found in products';
            }
        }

        return compact('error', 'message', 'data');
    }
    public function getItemsProductDataWhileReceivingReturn()
    {
        $error = 0;
        $message = 'success';
        $data = [];

        foreach ($this->items as $item) {

            $temp_product = Product::whereId($item->order_item->product_id)->whereSellerId($this->seller_id)->first(['id','product_name','sale','SKU','barcode']);

            if ($temp_product || $temp_product = Product::where('SKU', $item->order_item->SKU)->where('barcode', $item->order_item->barcode)->whereSellerId($this->seller_id)->first(['id','product_name','sale','SKU','barcode'])) {
                
                $data[] = [
                    "id" => $temp_product->id,
                    "name" => ($temp_product->SKU ? $temp_product->SKU .' | ' : '').($temp_product->barcode ? $temp_product->barcode .' | ' : '').$temp_product->product_name,
                    "unit_price" => $temp_product->sale,
                    "quantity" => $item->order_item->quantity,
                    "quantity_received" => $item->order_item->quantity,
                ];

            } else {
                $error = 1;
                $message = 'Stock Order Not Created | Shipment item sku ('.$item->order_item->SKU.') not found in products';
            }
        }

        return compact('error', 'message', 'data');
    }
}
