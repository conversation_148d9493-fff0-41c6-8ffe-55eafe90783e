<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CartonProduct extends Model
{
    use HasFactory;

    protected $fillable = [
        'carton_id',
        'product_id',
        'quantity',
    ];

    public function carton()
    {
        return $this->belongsTo(Carton::class);
    }

    public function product()
    {
        return $this->belongsTo(Product::class);
    }
}
