<?php

namespace App\Models;

use App\Models\Courier\Courier;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Log;

class ReturnReceive extends Model
{
    use HasFactory;

    public function items()
    {
        return $this->hasMany(ReturnReceiveItem::class);
    }

    public function shipment()
    {
        return $this->belongsTo(Shipment::class);
    }

    public function order()
    {
        return $this->belongsTo(Order::class);
    }

    public function courier()
    {
        return $this->belongsTo(Courier::class);
    }

    public function fulfillmentOrder()
    {
        return $this->belongsTo(FulfillmentOrder::class);
    }

    public function stockOrder()
    {
        return $this->belongsTo(StockOrder::class);
    }

    public function sellerLocation()
    {
        return $this->belongsTo(SellerLocation::class);
    }

    static function getPutawayAndAddEntry($stock_order_id, $shipment, $destination_location_id, $line_items, $products, $fulfillment_order_id)
    {
        try {

            $seller_id = $shipment->seller_id;
            $line_items = collect($line_items);
            $line_items_barcode = $line_items->pluck('barcode')->toArray();
            $received_items_count = 0;
            $message = '';
            $ffc_bins = self::getFfcBin($seller_id, $destination_location_id);


            $return_receive = new ReturnReceive();
            $return_receive->seller_id = $seller_id;
            $return_receive->shipment_id = $shipment->id;
            $return_receive->courier_id = $shipment->courier_id;
            $return_receive->order_id = $shipment->order_id;
            $return_receive->fulfillment_order_id = $fulfillment_order_id;
            $return_receive->stock_order_id = $stock_order_id;
            $return_receive->seller_location_id = $destination_location_id;
            $return_receive->received_by = 'Auto';
            $return_receive->received_by_name = 'System';
            $return_receive->received_by_id = 1;
            $return_receive->save();
            
            if ($stock_order_id) {
                $stock_order = StockOrder::whereId($stock_order_id)->first();
                
                foreach ($stock_order->items as $item) {


                    $before_qty = 0;
                    $before_uncommitted = 0;
                    $reason = 'Missing';
                    $quantity = $item->qty;
                    $seller_ffc_location = $ffc_bins['return_receiving_missing_type_bin'];
        
                    $product_id = $item->product_id;
                    $product = Product::find($product_id);
                    $putaway_qty = 0;


                    if ( in_array($product->barcode, $line_items_barcode) ) {

                        $seller_ffc_location = $ffc_bins['return_receiving_receiving_type_bin'];
                        $reason = 'Received';
                        $received_items_count ++;
                        $received_quantity = $line_items->where('barcode', $product->barcode)->first()['qty'];

                        if ($received_quantity < $quantity) {
                            $temp_seller_ffc_location = $ffc_bins['return_receiving_missing_type_bin'];

                            if ($temp_seller_ffc_location) {
                                $putaway_qty += $quantity - $received_quantity;
                            }

                            self::addInventory($seller_id, $temp_seller_ffc_location, $destination_location_id, $quantity - $received_quantity, 'Missing', $return_receive, $stock_order, $product, $message, $before_qty, $before_uncommitted);

                            $message .= '<br> Product Barcode : <b>'.$product->barcode.' ('.$quantity - $received_quantity.') </b>'.($temp_seller_ffc_location ? '  | Bin : <b>'.$temp_seller_ffc_location->location_name.'</b>' : '').' | Reason : <b>Missing</b>';
                            $quantity = $received_quantity;
                        }
                    }


                    if ($seller_ffc_location) {
                        $putaway_qty += $received_quantity;
                    }

                    self::addInventory($seller_id, $seller_ffc_location, $destination_location_id, $quantity, $reason, $return_receive, $stock_order, $product, $message, $before_qty, $before_uncommitted);

                    $message .= '<br> Product Barcode : <b>'.$product->barcode.' ('.$quantity.') </b>'.($seller_ffc_location ? ' | Bin : <b>'.$seller_ffc_location->location_name.'</b>' : '').' | Reason : <b>'.$reason.'</b>';


                    $item->putaway_qty += $putaway_qty;
                    $item->save();
                }                

            } else {

                foreach ($products as $value) {

                    $reason = 'Missing';
                    $seller_ffc_location = $ffc_bins['return_receiving_missing_type_bin'];
                    $product = Product::find($value->id);
                    $quantity = $value->quantity;
    
                    if ( in_array($product->barcode, $line_items_barcode) ) {
    
                        $seller_ffc_location = $ffc_bins['return_receiving_receiving_type_bin'];
                        $reason = 'Received';
                        $received_items_count ++;
                        $received_quantity = $line_items->where('barcode', $product->barcode)->first()['qty'];
    
                        if ($received_quantity < $quantity) {
    
                            $temp_seller_ffc_location = $ffc_bins['return_receiving_missing_type_bin'];
    
                            $return_receive_item = new ReturnReceiveItem();
                            $return_receive_item->seller_id = $seller_id;
                            $return_receive_item->product_id = $product->id;
                            $return_receive_item->return_receive_id = $return_receive->id;
                            $return_receive_item->barcode = $product->barcode;
                            $return_receive_item->seller_ffc_location_id = ($temp_seller_ffc_location ? $temp_seller_ffc_location->id : null);
                            $return_receive_item->quantity = $quantity - $received_quantity;
                            $return_receive_item->reason = 'Missing';
                            $return_receive_item->save();
    
                            $message .= '<br> Product Barcode : <b>'.$product->barcode.' ('.$quantity - $received_quantity.') </b>'.($temp_seller_ffc_location ? '  | Bin : <b>'.$temp_seller_ffc_location->location_name.'</b>' : '').' | Reason : <b>Missing</b>';
                            $quantity = $received_quantity;
                        }
                    }

                    $return_receive_item = new ReturnReceiveItem();
                    $return_receive_item->seller_id = $seller_id;
                    $return_receive_item->product_id = $product->id;
                    $return_receive_item->return_receive_id = $return_receive->id;
                    $return_receive_item->barcode = $product->barcode;
                    $return_receive_item->seller_ffc_location_id = ($seller_ffc_location ? $seller_ffc_location->id : null);
                    $return_receive_item->quantity = $quantity;
                    $return_receive_item->reason = $reason;
                    $return_receive_item->save();
    
                    $message .= '<br> Product Barcode : <b>'.$product->barcode.' ('.$quantity.') </b>'.($seller_ffc_location ? ' | Bin : <b>'.$seller_ffc_location->location_name.'</b>' : '').' | Reason : <b>'.$reason.'</b>';
                }
            }


            if ($received_items_count != count($products)) {
                $message = '<b>Return Received Partially (ERP) </b>'.$message;
            } else {
                $message = '<b>Return Received Completely (ERP) </b>'.$message;
            }

            OrderComments::add($shipment->order_id, 'Return Received Process', $message , 'Success', '1');

        } catch (\Exception $e) {
            Log::critical('Return received add entry failed for ERP | '.$e->getMessage(), $e->getTrace());
        }
    }


    static function getFfcBin($seller_id, $location_id)
    {
        $data = [];

        if (SellerLocation::whereId($location_id)->where('is_ffc_enabled', 1)->exists()) {

            $data['return_receiving_receiving_type_bin'] = Setting::returnReceivingReceivingType($seller_id);
            $data['return_receiving_missing_type_bin'] = Setting::returnReceivingMissingType($seller_id);
    
            $data['return_receiving_receiving_type_bin'] = $data['return_receiving_receiving_type_bin'] ? SellerFfcLocation::whereSellerId($seller_id)->where('is_leaf', 1)->where('id', $data['return_receiving_receiving_type_bin'])->first(['id', 'location_name', 'location_id', 'location_type']) : Null ;
            $data['return_receiving_missing_type_bin'] = $data['return_receiving_missing_type_bin'] ? SellerFfcLocation::whereSellerId($seller_id)->where('is_leaf', 1)->where('id', $data['return_receiving_missing_type_bin'])->first(['id', 'location_name', 'location_id', 'location_type']) : Null ;

        } else {

            $data['return_receiving_receiving_type_bin'] = Null ;
            $data['return_receiving_missing_type_bin'] = Null ;
        }

        return $data;
    }


    static function addInventory($seller_id, $seller_ffc_location, $destination_location_id, $quantity, $reason, $return_receive, $stock_order, $product, $message, $before_qty, $before_uncommitted)
    {
        $return_receive_item = new ReturnReceiveItem();
        $return_receive_item->seller_id = $seller_id;
        $return_receive_item->product_id = $product->id;
        $return_receive_item->return_receive_id = $return_receive->id;
        $return_receive_item->barcode = $product->barcode;
        $return_receive_item->seller_ffc_location_id = ($seller_ffc_location ? $seller_ffc_location->id : null);
        $return_receive_item->quantity = $quantity;
        $return_receive_item->reason = $reason;
        $return_receive_item->save();
        
        if (!$seller_ffc_location) {
            return;
        }

        $seller_ffc_inventory = SellerFfcInventory::where('seller_ffc_location_id', $seller_ffc_location->id)->where('product_id', $product->id)->where('seller_id', $seller_id)->first();

        if ($seller_ffc_inventory) {

            $before_qty = $seller_ffc_inventory->qty;
            $before_uncommitted = $seller_ffc_inventory->uncommitted_qty;

            $seller_ffc_inventory->qty = $seller_ffc_inventory->qty + $quantity;
            $seller_ffc_inventory->uncommitted_qty = $seller_ffc_inventory->uncommitted_qty + $quantity;

        } else {

            $seller_ffc_inventory = new SellerFfcInventory;
            $seller_ffc_inventory->seller_id = $seller_id;
            $seller_ffc_inventory->seller_location_id = $destination_location_id;
            $seller_ffc_inventory->seller_ffc_location_id = $seller_ffc_location->id;
            $seller_ffc_inventory->product_id = $product->id;
            $seller_ffc_inventory->qty = $quantity;
            $seller_ffc_inventory->uncommitted_qty = $quantity;
        }

        // For FFC Inventory
        hold_data_for_internal_logger(
            $seller_id,
            ( auth()->check() ? (session()->has('user') ? 'Sub User' : 'Admin') : 'Auto'),
            ( auth()->check() ? (session()->has('user') ? session('user')->id : $seller_id ) : 1),
            ( auth()->check() ? (session()->has('user') ? session('user')->full_name : auth()->user()->full_name) : 'System'),
            'FFC Inventory',
            'qty, uncommitted_qty',
            'Stock Order',
            $stock_order->id,
            $stock_order->reference_id,
            'Putaway',
            'Stock order ('.config('enum.stock_order_types_reverse')[$stock_order->type].') putaway has been completed through ERP Return Received process and FFC inventory has been incremented.'
        );

        $seller_ffc_inventory->save();

        // For Ledger Inventory
        hold_data_for_internal_logger(
            $seller_id,
            ( auth()->check() ? (session()->has('user') ? 'Sub User' : 'Admin') : 'Auto'),
            ( auth()->check() ? (session()->has('user') ? session('user')->id : $seller_id ) : 1),
            ( auth()->check() ? (session()->has('user') ? session('user')->full_name : auth()->user()->full_name) : 'System'),
            'Ledger Inventory',
            'stock, uncommitted_stock',
            'Stock Order',
            $stock_order->id,
            $stock_order->reference_id,
            'Putaway',
            'Stock order ('.config('enum.stock_order_types_reverse')[$stock_order->type].') putaway has been completed through ERP Return Received process and Ledger inventory has been incremented.'
        );

        if ($seller_ffc_location->location_type == config('enum.storage_location_types')['Unsellable']) {
    
            Inventory::updateOnlyStock($product->id, $seller_ffc_location->location_id, $quantity);
            Inventory::updateOnlyUnsellableStock($seller_ffc_location->location_id, $product->id, $quantity);

        } else {
            Inventory::updateStock($product->id, $seller_ffc_location->location_id, $quantity);
        }

        PickAndPutawayLog::add(
            $seller_id,
            $product->id,
            $product->barcode,
            config('enum.stock_order_types_reverse')[$stock_order->type],
            'Putaway',
            'Stock Order',
            $stock_order->id,
            1,
            $seller_ffc_location->id,
            $seller_ffc_location->location_name,
            $before_uncommitted,
            $seller_ffc_inventory->uncommitted_qty,
            $before_qty,
            $seller_ffc_inventory->qty
        );
    }

}
