<?php

namespace App\Models\Courier;

use App\Mail\CourierError;
use App\Models\AdminSetting;
use App\Models\Courier\Courier;
use App\Models\Courier\SellerCourier;
use Exception;
use GuzzleHttp\Client;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Log;
use Mail;

class SellerCourierMoveo extends Model
{
    use HasFactory;

    const courier_id = 43;

    protected $table = 'seller_courier_moveo';

    protected $fillable = ['seller_id', 'courier_id', 'courier_account_id', 'api_token','account_title'];

    public static function shipped($data)
    {
        $client = new Client();

        $order = $data['order'];

        if(SellerCourier::whereSellerId($order->seller_id)->whereCourierId(self::courier_id)->value('is_universal') == 2 && Courier::universalEnableCheck(self::courier_id)) {
            $api_token = AdminSetting::where('key','moveo_api_token')->value('value');

            if ($api_token) {
                $moveo = new self;
                $moveo->api_token = $api_token;
            } else {
                return array('error' => 1 , 'message' => 'MOVEO | Universal Account Credentials not found , please contact unity team for this');
            }
        } else {
            $moveo = self::where('seller_id', $order->seller_id)->first();
        }

        if ($moveo) {

            try {

                $request_data = [
                        "shipper_name" => $data['default_pickup_address']['contact_person_name'],
                        "shipper_city" => $data['seller_city'],
                        "pickup_address" => $data['default_pickup_address']['address'].", ".$data['seller_city'],
                        "pickup_lat_long" => $data['default_pickup_address']['lat'].",".$data['default_pickup_address']['lng'],
                        "return_address" => $data['default_pickup_address']['address'].", ".$data['seller_city'],
                        "consignee_name" => $order->customer_name,
                        "consignee_city" => $data['destination_city'],
                        "consignee_phone_number" => $order->customer_number,
                        "delivery_address" => $order->shipping_address.", ".$data['destination_city'],
                        "delivery_lat_long" => $order['customer_lat'].",".$order['customer_long'],
                        "contact_number" => $data['default_pickup_address']['contact_person_phone_number'],
                        "email" => $data['customer_email'],
                        "order_reference_id" => $data['shipper_order_id'],
                        "shipment_description" => (strlen($data['description']) > 1000 ? substr($data['description'], 0, 997).'...' : $data['description']),
                        "shipment_weight" => $data['weight'],
                        "payment_method" => (($data['cod'] == null) ? 'Prepaid' : 'Cash on Delivery'),
                        "cod_amount" => (($data['cod'] == null) ? 0 : $data['cod']),
                        "shipping_service" => $data['service'],
                        "service_id" => $data['service'],
                ];

                \Log::info('MOVEO Booking request Dump ', $request_data);

                $response = $client->post(env('MOVEO_API_URL', 'https://moveo.unityretail.com/').'shipments', [
                    'timeout' => 90, // Response timeout
                    'connect_timeout' => 30, // Connection timeout
                    'headers' => [
                        'Authorization' => 'Bearer '.$moveo->api_token,
                        'content-type' => 'application/json',
                    ],
                    'json' => $request_data,
                ]);

                $response = json_decode($response->getBody()->getContents(), true);

            } catch (Exception $e) {
                return ['error' => 1, 'message' => 'MOVEO | '.$e->getMessage()];
            }

            if ($response['success']) {

                $tracking_number = $response['data']['awb_number'];

                if ($tracking_number) {
                    return ['error' => 0, 'message' => $tracking_number];
                } else {
                    return ['error' => 0, 'message' => $response['message']];
                }

            } else {
                $error = json_encode($response);

                $activity_id = activity()
                    ->performedOn($order)
                    ->causedBy($order->seller_id)
                    ->withProperties(['response' => json_encode($response), 'dump' => json_encode($request_data)])
                    ->log('MOVEO');

                try {
                    \Mail::to('<EMAIL>')->send(new CourierError($activity_id));
                } catch (Exception $exception) {
                    $activity_id = activity()
                        ->performedOn($order)
                        ->causedBy($order->seller_id)
                        ->withProperties([ 'message' => $exception->getMessage(), 'trace' => $exception->getTraceAsString() ])
                        ->log('MAIL FAILED | MOVEO Booking');
                }

                return ['error' => 1, 'message' => 'MOVEO | '.$error];
            }

        } else {
            return ['error' => 1, 'message' => 'MOVEO | Courier Credentials not found'];
        }
    }
}
