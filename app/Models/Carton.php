<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Carton extends Model
{
    use HasFactory;

    protected $fillable = [
        'seller_id',
        'name',
        'barcode',
        'status', // 'active' or 'inactive'
    ];

    function scopeForSeller($query, $seller_id)
    {
        return $query->where('seller_id', $seller_id);
    }

    public function cartonProducts()
    {
        return $this->hasMany(CartonProduct::class);
    }
}
