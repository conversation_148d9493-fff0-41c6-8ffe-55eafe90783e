<?php

namespace App\Http\Controllers;

use App\Models\Courier\City;
use App\Models\Courier\Courier;
use App\Models\Courier\CustomCourier;
use App\Models\Courier\SellerCourier;
use App\Models\Courier\SellerCourierAmazonShipping;
use App\Models\Courier\SellerCourierAramex;
use App\Models\Courier\SellerCourierBlueEx;
use App\Models\Courier\SellerCourierBykea;
use App\Models\Courier\SellerCourierCallCourier;
use App\Models\Courier\SellerCourierCourierX;
use App\Models\Courier\SellerCourierCarson;
use App\Models\Courier\SellerCourierCityMail;
use App\Models\Courier\SellerCourierDaewoo;
use App\Models\Courier\SellerCourierDeliveryExpress;
use App\Models\Courier\SellerCourierDelybell;
use App\Models\Courier\SellerCourierDEX;
use App\Models\Courier\SellerCourierDHL;
use App\Models\Courier\SellerCourierDPD;
use App\Models\Courier\SellerCourierFlyCourier;
use App\Models\Courier\SellerCourierLCS;
use App\Models\Courier\SellerCourierLCSUAE;
use App\Models\Courier\SellerCourierMNP;
use App\Models\Courier\SellerCourierMoveX;
use App\Models\Courier\SellerCourierPandaGo;
use App\Models\Courier\SellerCourierQuiqup;
use App\Models\Courier\SellerCourierRider;
use App\Models\Courier\SellerCourierSwyft;
use App\Models\Courier\SellerCourierTCS;
use App\Models\Courier\SellerCourierTCSNew;
use App\Models\Courier\SellerCourierTrax;
use App\Models\Courier\SellerCourierTraxNew;
use App\Models\Courier\SellerCourierPostEx;
use App\Models\CourierPerformance;
use App\Models\CourierPerformanceInCities;
use App\Models\CourierService;
use App\Models\Seller;
use App\Models\Courier\SellerCourierForrun;
use App\Models\Courier\SellerCourierStallion;
use App\Models\Courier\SellerCourierInsta;
use App\Models\Courier\SellerCourierLCSMerchant;
use App\Models\Courier\SellerCourierSelf;
use App\Models\Courier\SellerCourierSmsa;
use App\Models\Courier\SellerCourierPostExPartner;
use App\Models\Courier\SellerCourierTcsUae;
use App\Models\Courier\SellerCourierTimeExpress;
use App\Models\PaymentInfoOption;
use App\Models\Courier\SellerCourierJLD;
use App\Models\SellerCourierService;
use App\Models\SellerCourierTQS;
use App\Models\SellerLocation;
use App\Models\SellerPaymentInfoOption;
use App\Models\Setting;
use GuzzleHttp\Client;
use App\Models\Shipment;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redirect;
use Firebase\JWT\JWT;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;

class CourierController extends Controller {
		private $client;

		public function __construct() {
			$this->client = new Client();
		}

		public function settings_view() {

			$data['page_title'] = 'Courier Settings';
			$data['couriers'] = Courier::where('status', 1)->orderBy('is_universal','desc')->with('seller_couriers')->get();

			$data['enabledCouriers'] = SellerCourier::where('seller_id', Auth::id())->where('status', '1')->pluck('courier_id')->toArray();
			$data['disabledCouriers'] = SellerCourier::where('seller_id', Auth::id())->where('status', '0')->pluck('courier_id')->toArray();
			$data['all'] = count($data['couriers']);
			$data['enabled'] = count($data['enabledCouriers']);
			$data['disabled'] = count($data['disabledCouriers']);

			$data['custom_courier'] = null;
			$customCourier = CustomCourier::where('seller_id', Auth::id());
			if ($customCourier->exists()) {
				$data['custom_courier'] = $customCourier->value('image');
			}

			return view('seller.settings.couriers.home')->with($data);
		}

		public function settings_courier_view($id) {
			$courier_status = SellerCourier::where('seller_id', Auth::id())->where('courier_id', $id);

			if ($id == 1) {

				$self = CustomCourier::where('seller_id', Auth::id());
				$data['self'] = ($self->exists()) ? $self->first() : NULL;
			}
			else if ($id == 2) {
				$tcs = SellerCourierTCS::where('seller_id', Auth::id())->where('courier_id', $id);

				$data['tcs'] = ($tcs->exists()) ? $tcs->first() : NULL;
			}
			else if ($id == 3) {
				$trax = SellerCourierTrax::where('seller_id', Auth::id())->where('courier_id', $id);

				$data['trax'] = ($trax->exists()) ? $trax->first() : NULL;
			}
			else if ($id == 4) {
				$mnp = SellerCourierMNP::where('seller_id', Auth::id())->where('courier_id', $id);

				$data['mnp'] = ($mnp->exists()) ? $mnp->first() : NULL;
			}
			else if ($id == 5) {
				$lcs = SellerCourierLCS::where('seller_id', Auth::id())->where('courier_id', $id);

				$data['lcs'] = ($lcs->exists()) ? $lcs->first() : NULL;
			}
			else if ($id == 7) {
				$call_courier = SellerCourierCallCourier::where('seller_id', Auth::id())->where('courier_id', $id);

				$data['call_courier'] = ($call_courier->exists()) ? $call_courier->first() : NULL;
			}
			else if ($id == 8) {
				$trax_new = SellerCourierTraxNew::where('seller_id', Auth::id())->where('courier_id', $id);

				$data['trax_new'] = ($trax_new->exists()) ? $trax_new->first() : NULL;
			}
			else if ($id == 10) {
				$blueEx = SellerCourierBlueEx::where('seller_id', Auth::id())->where('courier_id', $id);

				$data['blueEx'] = ($blueEx->exists()) ? $blueEx->first() : NULL;
			}
			else if ($id == 11) {
				$rider = SellerCourierRider::where('seller_id', Auth::id())->where('courier_id', $id);

				$data['rider'] = ($rider->exists()) ? $rider->first() : NULL;
			}
			else if ($id == 12) {
				$delivery_express = SellerCourierDeliveryExpress::where('seller_id', Auth::id())->where('courier_id', $id);

				$data['delivery_express'] = ($delivery_express->exists()) ? $delivery_express->first() : NULL;
			}
			else if ($id == 13) {
				$tcs_new = SellerCourierTCSNew::where('seller_id', Auth::id())->where('courier_id', $id);

				$data['tcs_new'] = ($tcs_new->exists()) ? $tcs_new->first() : NULL;
			}
			else if ($id == 14) {
				$lcs_uae = SellerCourierLCSUAE::where('seller_id', Auth::id());

				$data['lcs_uae'] = ($lcs_uae->exists()) ? $lcs_uae->first() : NULL;
			}
			else if ($id == 15) {
				$swyft = SellerCourierSwyft::where('seller_id', Auth::id());

				$data['swyft'] = ($swyft->exists()) ? $swyft->first() : NULL;
			}
			else if ($id == 16) {
				$dpd = SellerCourierDPD::where('seller_id', Auth::id());

				$data['dpd'] = ($dpd->exists()) ? $dpd->first() : NULL;
			}
			else if ($id == 17) {
				$dhl = SellerCourierDHL::where('seller_id', Auth::id());

				$data['dhl'] = ($dhl->exists()) ? $dhl->first() : NULL;
			}
			else if ($id == 18) {
				$courierx = SellerCourierCourierX::where('seller_id', Auth::id());

				$data['courierx'] = ($courierx->exists()) ? $courierx->first() : NULL;
			}
			else if ($id == 19) {
				$movex = SellerCourierMoveX::where('seller_id', Auth::id());

				$data['movex'] = ($movex->exists()) ? $movex->first() : NULL;
			}
			else if ($id == 20) {
				$bykea = SellerCourierBykea::where('seller_id', Auth::id());

				$data['bykea'] = ($bykea->exists()) ? $bykea->first() : NULL;
			}
			else if ($id == 21) {
				$carson = SellerCourierCarson::where('seller_id', Auth::id());

				$data['carson'] = ($carson->exists()) ? $carson->first() : NULL;
			}
			else if ($id == 22) {
				$delybell = SellerCourierDelybell::where('seller_id', Auth::id());

				$data['delybell'] = ($delybell->exists()) ? $delybell->first() : NULL;
			}
			else if ($id == 23) {
				$quiqup = SellerCourierQuiqup::where('seller_id', Auth::id());

				$data['quiqup'] = ($quiqup->exists()) ? $quiqup->first() : NULL;
			}
			else if ($id == 24) {
				$pandago = SellerCourierPandaGo::where('seller_id', Auth::id());
				$locations = SellerLocation::where('seller_id', Auth::id())->get();
				$data['seller_locations'] = $locations;
				$data['seller_locations_counter'] = 1;
				if(Setting::where('seller_id', Auth::id())->where('key',config('enum.settings')['MARKASRETURN'])->where('value',1)->exists()){
					$data['mark_as_return'] = 1;
				}
				if(Setting::where('seller_id', Auth::id())->where('key',config('enum.settings')['ONDEMANDFAILED'])->where('value',1)->exists()){
					$data['on_demand_failed'] = 1;
				}
				$cut_off_timings = Setting::where('seller_id', Auth::id())->where('key',config('enum.settings')['CUTOFFTIMINGS']);
				$data['cutofftimings'] = ($cut_off_timings->exists()) ? $cut_off_timings->first() : 0;
				$data['pandago'] = ($pandago->exists()) ? $pandago->first() : NULL;
				// $data['pandago'] = null;
			}
			else if ($id == 25) {
				$amazon_shipping = SellerCourierAmazonShipping::where('seller_id', Auth::id());

				$data['amazon_shipping'] = ($amazon_shipping->exists()) ? $amazon_shipping->first() : NULL;
			}
			else if ($id == 26) {
				$aramex = SellerCourierAramex::where('seller_id', Auth::id());

				$data['aramex'] = ($aramex->exists()) ? $aramex->first() : NULL;
			}
			else if ($id == 27) {
				$forrun = SellerCourierForrun::where('seller_id', Auth::id());

				$data['forrun'] = ($forrun->exists()) ? $forrun->first() : NULL;
			}
			else if ($id == 28) {
				$postex = SellerCourierPostEx::where('seller_id', Auth::id());

				$data['postex'] = ($postex->exists()) ? $postex->first() : NULL;
			}
			else if ($id == 29) {
				$stallion = SellerCourierStallion::where('seller_id', Auth::id());

				$data['stallion'] = ($stallion->exists()) ? $stallion->first() : NULL;
			}
			else if ($id == 30) {
				$time_express = SellerCourierTimeExpress::where('seller_id', Auth::id());

				$data['time_express'] = ($time_express->exists()) ? $time_express->first() : NULL;
			}
			else if ($id == 31) {
				$insta = SellerCourierInsta::where('seller_id', Auth::id());

				$data['insta'] = ($insta->exists()) ? $insta->first() : NULL;
			}
			else if ($id == 33) {
				$tcs_uae = SellerCourierTcsUae::where('seller_id', Auth::id());

				$data['tcs_uae'] = ($tcs_uae->exists()) ? $tcs_uae->first() : NULL;
			}
			else if ($id == 34) {
				$daewoo = SellerCourierDaewoo::where('seller_id', Auth::id());
				$data['daewoo'] = ($daewoo->exists()) ? $daewoo->first() : NULL;
            }
            else if ($id == 35) {
				$lcs_merchant = SellerCourierLCSMerchant::where('seller_id', Auth::id())->where('courier_id', $id);

				$data['lcs_merchant'] = ($lcs_merchant->exists()) ? $lcs_merchant->first() : NULL;
			}
			else if ($id == 36) {
				$fly_courier = SellerCourierFlyCourier::where('seller_id', Auth::id())->where('courier_id', $id);

				$data['fly_courier'] = ($fly_courier->exists()) ? $fly_courier->first() : NULL;
			}
			else if ($id == 37) {
				$smsa = SellerCourierSmsa::where('seller_id', Auth::id())->where('courier_id', $id);

				$data['smsa'] = ($smsa->exists()) ? $smsa->first() : NULL;
			}
			else if ($id == 38) {
				$postex = SellerCourierPostExPartner::where('seller_id', Auth::id());

				$data['postex'] = ($postex->exists()) ? $postex->first() : NULL;
			}
			else if ($id == 39) {
				$tqs = SellerCourierTQS::where('seller_id', Auth::id());
				$data['tqs'] = ($tqs->exists()) ? $tqs->first() : NULL;
			}
			else if ($id == 40) {
				$dex = SellerCourierDEX::where('seller_id', Auth::id());
				$data['dex'] = ($dex->exists()) ? $dex->first() : NULL;
			}
			else if ($id == 41) {
				$city_mail = SellerCourierCityMail::where('seller_id', Auth::id());
				$data['city_mail'] = ($city_mail->exists()) ? $city_mail->first() : NULL;
			}
			else if ($id == 42) {
				$jld = SellerCourierJLD::where('seller_id', Auth::id());
				$data['jld'] = ($jld->exists()) ? $jld->first() : NULL;
			}
			else if ($id == 43) {
				$moveo = \App\Models\Courier\SellerCourierMoveo::where('seller_id', Auth::id());
				$data['moveo'] = ($moveo->exists()) ? $moveo->first() : NULL;
			}


			$data['courier_status'] = ($courier_status->exists()) ? $courier_status->first() : NULL;

			$data['mps'] = Courier::where('id', $id)->value('multi_package_shipment');
			$data['mps_seller'] = SellerCourier::whereSellerId(Auth::id())->whereCourierId($id)->value('multi_package_shipment');

			if ($data['courier_status'] && ($data['courier_status'])->is_universal == 2 ) {
				// Ignore
			} else {
				$data['services'] = CourierService::where('courier_id', $id)->get();
				$data['default_service'] = SellerCourierService::where('default', 1)->whereIn('courier_services_id', ($data['services'])->pluck('id'))->where('seller_id', auth()->id())->value('courier_services_id');
				$data['seller_services'] = \DB::table('couriers')
				->join('courier_services', 'courier_services.courier_id', '=', 'couriers.id')
				->join('seller_courier_services', 'courier_services_id', '=', 'courier_services.id')
				->select('courier_services.*')->where('seller_courier_services.seller_id', Auth::id())->where('courier_services.courier_id', $id)
				->get();
				
				if($id == 17){
					$data['payment_info_options'] = PaymentInfoOption::whereCourierId(17)->get();
					$data['seller_payment_info_options'] = SellerPaymentInfoOption::whereSellerId(Auth::id())->get();
					$data['selected_payment_info_options'] = SellerPaymentInfoOption::whereSellerId(Auth::id())->where('active',1)->pluck('payment_info_options_id')->toArray();
					// $data['default_payment_info_options'] = SellerPaymentInfoOption::whereSellerId(Auth::id())->where('active',1)->pluck('id')->toArray();

				}

				$data['seller_services']=$data['seller_services']->pluck('id')->toArray();
			}
			

			
			$data['page_title'] = 'Courier Settings';
			$data['courier'] = Courier::find($id);
			// return $data;
			return view('seller.settings.couriers.courier')->with($data);
		}

		public function getPayemntInfoOption()
		{
			if(Auth::id()){
				return SellerPaymentInfoOption::whereSellerId(Auth::id())->where('active',1)->with(['paymentInfoOption'])->get()->toArray();
			} else{
				abort(404);
			}
			
		}

		public function ajaxUniversalRequest(Request $request)
		{
			
			$data['seller'] = Seller::find(auth()->id());

			$data["email"] = "<EMAIL>";
			$data["title"] = "Universal Account Request";
			$data["body"] = "Seller (".auth()->id().")  requested a universal account of Courier ID ".$request->courier_id;
	
			if($data['seller']->entity_type == 'Individual'){
				$files = [
					url($data['seller']->cnic_front),
					url($data['seller']->cnic_back),
					// public_path('files/**********.png'),
				];
			} else{
				$files = [
					url($data['seller']->certificate),
				];
			}
	
			Mail::send('email.universal_account_request', $data, function($message)use($data, $files) {
				$message->to($data["email"], $data["email"])
						->subject($data["title"]);
	
				foreach ($files as $file){
					$message->attach($file);
				}
				
			});
			$courier_status = 1;
			
			// $sellerCourier = SellerCourier::where('seller_id' , Auth::id())->where('courier_id' , $request->courier_id)->first();
			// if(isset($sellerCourier)){
			// 	if($sellerCourier->status == 1){
			// 		$courier_status == 1;
			// 	}
			// }
			SellerCourier::updateOrCreate(['seller_id' => Auth::id(), 'courier_id' => $request->courier_id], ['status' => $courier_status, 'is_universal' => 1 ]);
			return back()->with('success', 'Universal Account Request has been received');
		}

		public function settings_courier_save(Request $request) {
			$message = 'Courier has been updated!';
			$message_status = 'status';
			$request->status = ($request->status) ? $request->status : 0;

			if (isset($request->service_enable) && isset($request->default_service) && $request->status) {
				if (! in_array($request->default_service, array_keys($request->service_enable))) {
					return back()->with('error','Default service is not an enable service');
				}
			}

			if ($request->universal_account && $request->universal_account == 1) {
				// Mail::raw("Seller (".auth()->id().")  requested a universal account of Courier ID ".$request->courier_id, function ($m)  {
				// 	$m->to(['<EMAIL>'])->subject('Universal Account Request');
				// });
				
				$data['seller'] = Seller::find(auth()->id());

				$data["email"] = "<EMAIL>";
				$data["title"] = "Universal Account Request";
				$data["body"] = "Seller (".auth()->id().")  requested a universal account of Courier ID ".$request->courier_id;
		
				if($data['seller']->entity_type == 'Individual'){
					$files = [
						url($data['seller']->cnic_front),
						url($data['seller']->cnic_back),
						// public_path('files/**********.png'),
					];
				} else{
					$files = [
						url($data['seller']->certificate),
					];
				}
		
				Mail::send('email.universal_account_request', $data, function($message)use($data, $files) {
					$message->to($data["email"], $data["email"])
							->subject($data["title"]);
		
					foreach ($files as $file){
						$message->attach($file);
					}
					
				});
			}

			
			$sellerCourier = SellerCourier::where('seller_id' , Auth::id())->where('courier_id' , $request->courier_id)->first();
			SellerCourier::updateOrCreate(['seller_id' => Auth::id(), 'courier_id' => $request->courier_id], ['status' => ( $request->status ? 1 : 0 ), 'is_universal' => (isset($request->universal_account)) ? $request->universal_account : ( ($sellerCourier ? ($sellerCourier->is_universal == 1 ? 1 : 0) : 0) ) ]);
			if (isset($request->universal_account) && in_array($request->universal_account, [1,2]) ) {
				# ignore
				if ($request->courier_id == 24) {
				
					Setting::updateOrCreate(['seller_id' => Auth::id(), 'key' => config('enum.settings')['MARKASRETURN']], ['value' => (isset($request->mark_as_return) ? 1 : 0) ]);
					Setting::updateOrCreate(['seller_id' => Auth::id(), 'key' => config('enum.settings')['ONDEMANDFAILED']], ['value' => (isset($request->on_demand_failed) ? 1 : 0) ]);
					Setting::updateOrCreate(['seller_id' => Auth::id(), 'key' => config('enum.settings')['CUTOFFTIMINGS']], ['value' => (isset($request->cutofftimings) ? $request->cutofftimings : 0) ]);
				}
			} elseif ($sellerCourier && in_array($sellerCourier->is_universal, [1,2])) {
				# ignore
				if ($request->courier_id == 24) {
				
					Setting::updateOrCreate(['seller_id' => Auth::id(), 'key' => config('enum.settings')['MARKASRETURN']], ['value' => (isset($request->mark_as_return) ? 1 : 0) ]);
					Setting::updateOrCreate(['seller_id' => Auth::id(), 'key' => config('enum.settings')['ONDEMANDFAILED']], ['value' => (isset($request->on_demand_failed) ? 1 : 0) ]);
					Setting::updateOrCreate(['seller_id' => Auth::id(), 'key' => config('enum.settings')['CUTOFFTIMINGS']], ['value' => (isset($request->cutofftimings) ? $request->cutofftimings : 0) ]);
				}
			}
			else if ($request->courier_id == 1) {

				if(!$request->prefix){
					return back()->with('error', 'Prefix not found');
				}else if($request->prefix_enable && !$request->radio_button) {
					return back()->with('error', 'Random Tracking No. option is enabled but no option is selected');
				}

				$option = ($request->prefix_enable ? $request->radio_button : null);


				if($request->hasFile('image'))
				{
					$filename = ($request->image)->hashName();
					$file = $request->file('image');
					$file->move(public_path('img/custom_couriers'),$filename);
					CustomCourier::updateOrCreate(['seller_id' => Auth::id()], ['image' => $filename, 'name' => $request->name, 'prefix' => $request->prefix, 'tracking_number_option' => $option]);
				} else {
					CustomCourier::updateOrCreate(['seller_id' => Auth::id()], ['name' => $request->name, 'prefix' => $request->prefix, 'tracking_number_option' => $option]);
				}
			}
			else if ($request->courier_id == 2) {
				// return $request->account_title;
				SellerCourierTCS::updateOrCreate(['seller_id' => Auth::id(), 'courier_id' => $request->courier_id], ['account_title' => $request->account_title, 'username' => $request->username, 'password' => $request->password, 'cost_center_code' => $request->cost_center_code]);
			}
			else if ($request->courier_id == 4) {
				SellerCourierMNP::updateOrCreate(['seller_id' => Auth::id(), 'courier_id' => $request->courier_id], ['username' => $request->username, 'password' => $request->password, 'account_title' => $request->account_title,'account_no' => $request->account_no]);
			}
			else if ($request->courier_id == 5) {
				SellerCourierLCS::updateOrCreate(['seller_id' => Auth::id(), 'courier_id' => $request->courier_id], ['api_key' => $request->api_key, 'api_password' => $request->api_password, 'account_title' => $request->account_title, 'email' => $request->email]);
			}
			else if ($request->courier_id == 7) {
				SellerCourierCallCourier::updateOrCreate(['seller_id' => Auth::id(), 'courier_id' => $request->courier_id], ['username' => $request->username, 'password' => $request->password, 'account_id' => $request->account_id, 'account_title' => $request->account_title]);
				
				$default_location = SellerLocation::where('seller_id', Auth::id())->where('is_default',1);
				if ($default_location->exists() && !$default_location->value('call_courier_area_id')) {
					$message.= ' | Please add Area For Call Courier in Default Location';
					$message_status = 'warning';
				} else {
					if (!SellerLocation::where('seller_id', Auth::id())->value('call_courier_area_id')) {
						$message.= ' | Please add Area For Call Courier in Default Location';
						$message_status = 'warning';
					}
				}
			}
			else if ($request->courier_id == 8) {
				SellerCourierTraxNew::updateOrCreate(['seller_id' => Auth::id(), 'courier_id' => $request->courier_id], ['authorization_key' => $request->authorization_key, 'account_title' => $request->account_title, 'default_picker_address_id' =>($request->default_picker_address_id) ? $request->default_picker_address_id : 0  ]);
			}
			else if ($request->courier_id == 10) {
				SellerCourierBlueEx::updateOrCreate(['seller_id' => Auth::id(), 'courier_id' => $request->courier_id], ['user_id' => $request->user_id, 'password' => $request->password, 'api_key' => $request->api_key, 'account_title' => $request->account_title, 'account_no' => $request->account_no]);
			}
			else if ($request->courier_id == 11) {
				
				SellerCourierRider::updateOrCreate(['seller_id' => Auth::id(), 'courier_id' => $request->courier_id], ['login_id' => $request->login_id, 'account_title' => $request->account_title, 'api_key' => $request->api_key]);
			}
			else if ($request->courier_id == 12) {
				if(($point_id = SellerCourierDeliveryExpress::getPickup($request->api_key)) != 'Error')
				{
					SellerCourierDeliveryExpress::updateOrCreate(['seller_id' => Auth::id(), 'courier_id' => $request->courier_id], ['api_key' => $request->api_key, 'account_title' => $request->account_title, 'pickup_point_id' => $point_id]);
				} else {
					return back()->with('error','Your Delivery Express account doesn\'t have any Pickup point OR Your API key is not correct');
				}
			}
			else if ($request->courier_id == 13) {
				SellerCourierTCSNew::updateOrCreate(['seller_id' => Auth::id(), 'courier_id' => $request->courier_id], ['username' => $request->username, 'password' => $request->password, 'account_title' => $request->account_title, 'cost_center_code' => 0]);
			}
			else if ($request->courier_id == 14) {
				SellerCourierLCSUAE::updateOrCreate(['seller_id' => Auth::id()], ['account_number' => $request->account_number, 'account_title' => $request->account_title]);
			}
			else if ($request->courier_id == 15) {
				if(($point_id = SellerCourierSwyft::getPickup($request->vendor_id, $request->vendor_secret)) != 'Error')
				{
					SellerCourierSwyft::updateOrCreate(['seller_id' => Auth::id(), 'courier_id' => $request->courier_id], ['vendor_id' => $request->vendor_id, 'vendor_secret' => $request->vendor_secret, 'account_title' => $request->account_title, 'default_pickup_address_id' => $point_id]);
				} else {
					return back()->with('error','Your Swyft Vendor account doesn\'t have any Pickup point OR Your Vendor Credentials is not correct');
				}
			}
			else if ($request->courier_id == 16) {
				SellerCourierDPD::updateOrCreate(['seller_id' => Auth::id()], ['username' => $request->username, 'password' => $request->password, 'account_title' => $request->account_title]);
			}
			else if ($request->courier_id == 17) {
				SellerCourierDHL::updateOrCreate(['seller_id' => Auth::id()], ['username' => $request->username, 'account_no' => $request->account_no, 'password' => $request->password, 'account_title' => $request->account_title]);
			}
			else if ($request->courier_id == 18) {
				SellerCourierCourierX::updateOrCreate(['seller_id' => Auth::id(), 'courier_id' => $request->courier_id], ['client_code' => $request->client_code, 'account_title' => $request->account_title]);
			}
			else if ($request->courier_id == 19) {
				SellerCourierMoveX::updateOrCreate(['seller_id' => Auth::id(), 'courier_id' => $request->courier_id], ['api_key' => $request->api_key, 'account_title' => $request->account_title]);
			}
			else if ($request->courier_id == 20) {
				SellerCourierBykea::updateOrCreate(['seller_id' => Auth::id()], ['username' => $request->username, 'password' => $request->password, 'account_title' => $request->account_title]);
			}
			else if ($request->courier_id == 21) {
				SellerCourierCarson::updateOrCreate(['seller_id' => Auth::id(), 'courier_id' => $request->courier_id], ['email' => $request->email, 'password' => $request->password, 'hub' => $request->hub, 'merchant_code' => $request->merchant_code, 'merchant_name' => $request->merchant_name, 'account_title' => $request->account_title]);
			}
			else if ($request->courier_id == 22) {
				SellerCourierDelybell::updateOrCreate(['seller_id' => Auth::id(), 'courier_id' => $request->courier_id], ['api_token' => $request->api_token, 'account_title' => $request->account_title]);
			}
			else if ($request->courier_id == 23) {
				SellerCourierQuiqup::updateOrCreate(['seller_id' => Auth::id(), 'courier_id' => $request->courier_id], ['app_key' => $request->app_key, 'app_secret' => $request->app_secret, 'account_title' => $request->account_title]);
			}
			else if ($request->courier_id == 24) {
				$request->validate([
					'upload_private_key' => 'required_with:upload_public_key|mimes:txt,pem',
					'upload_public_key' => 'required_with:upload_private_key|mimes:txt,pub'
				],
				[
					'upload_private_key.required_with' => 'The private key is required.',
					'upload_public_key.required_with' => 'The public key is required.'
				]
				);
				// $file = $request->file('private_key');
				// return $file->getClientOriginalName();
				if($request->hasFile('upload_private_key')){
					$private_path = $request->file('upload_private_key')->storeAs('pandago_keys/'.auth()->id(),'private_key.pem');
				}
				if($request->hasFile('upload_public_key')){
					$public_path = $request->file('upload_public_key')->storeAs('pandago_keys/'.auth()->id(),'public_key.pub');
				}
			
				if(!$request->hasFile('upload_private_key') && !$request->hasFile('upload_public_key')){
					SellerCourierPandaGo::updateOrCreate(['seller_id' => Auth::id(), 'courier_id' => $request->courier_id], ['client_id' => $request->client_id, 'key_id' => $request->key_id, 'scope' => $request->scope, 'account_title' => $request->account_title]);
				}
				else{
					// Testing User Given Keys
					$test_string = 'yoitsasecretkey';
					$p = file_get_contents(storage_path('app/pandago_keys/'.auth()->id().'/private_key.pem'));
					$pu = file_get_contents(storage_path('app/pandago_keys/'.auth()->id().'/public_key.pub'));

					openssl_public_encrypt($test_string,$enc_data,$pu);
					openssl_private_decrypt($enc_data,$dec_data,$p);
					
					if($dec_data == $test_string){
						SellerCourierPandaGo::updateOrCreate(['seller_id' => Auth::id(), 'courier_id' => $request->courier_id], ['public_key' => $public_path, 'private_key' => $private_path, 'client_id' => $request->client_id, 'key_id' => $request->key_id, 'scope' => $request->scope, 'account_title' => $request->account_title]);
					} else{
						return redirect()->back()->with('error','Key Pairs are not valid');
					}
				}

				if(!SellerCourierPandaGo::whereSellerId(Auth::id())->where('public_key','<>',null)->where('private_key','<>',null)->exists()){
					return redirect()->back()->with('status','Courier details are saved successfully. Now please enter or create Key Pairs');
				}

				Setting::updateOrCreate(['seller_id' => Auth::id(), 'key' => config('enum.settings')['MARKASRETURN']], ['value' => (isset($request->mark_as_return) ? 1 : 0) ]);
				Setting::updateOrCreate(['seller_id' => Auth::id(), 'key' => config('enum.settings')['ONDEMANDFAILED']], ['value' => (isset($request->on_demand_failed) ? 1 : 0) ]);
				Setting::updateOrCreate(['seller_id' => Auth::id(), 'key' => config('enum.settings')['CUTOFFTIMINGS']], ['value' => (isset($request->cutofftimings) ? $request->cutofftimings : 0) ]);

				// return $private_path;
			}
			else if ($request->courier_id == 25) {
				SellerCourierAmazonShipping::updateOrCreate(['seller_id' => Auth::id(), 'courier_id' => $request->courier_id], ['api_key' => $request->api_key, 'account_title' => $request->account_title]);
			}
			else if ($request->courier_id == 26) {
				SellerCourierAramex::updateOrCreate(['seller_id' => Auth::id(), 'courier_id' => $request->courier_id], ['account_entity' => $request->account_entity ,'account_country_code' => $request->account_country_code ,'user_name' => $request->user_name, 'password' => $request->password,'account_number' => $request->account_number,'account_pin' => $request->account_pin,'account_title' => $request->account_title]);
			}	
			else if ($request->courier_id == 27) {
				SellerCourierForrun::updateOrCreate(['seller_id' => Auth::id() ], ['account_id' => $request->account_id, 'api_token' => $request->api_token, 'account_title' => $request->account_title]);
			}
			else if ($request->courier_id == 28) {
				$auth = ($request->authorization ? $request->authorization : "");
				SellerCourierPostEx::updateOrCreate(['seller_id' => Auth::id(),'courier_id' => $request->courier_id ], ['authorization' => $auth, 'token' => $request->token, 'account_title' => $request->account_title]);
			}
			else if ($request->courier_id == 29) {
				SellerCourierStallion::updateOrCreate(['seller_id' => Auth::id(),'courier_id' => $request->courier_id ], ['username' => $request->username, 'password' => $request->password, 'account_title' => $request->account_title]);
			}
			else if ($request->courier_id == 30) {
				SellerCourierTimeExpress::updateOrCreate(['seller_id' => Auth::id(),'courier_id' => $request->courier_id ], ['account_no' => $request->account_no, 'account_title' => $request->account_title]);
			}
			else if ($request->courier_id == 31) {
				SellerCourierInsta::updateOrCreate(['seller_id' => Auth::id(), ], ['api_key' => $request->api_key, 'account_title' => $request->account_title]);
			}
			else if ($request->courier_id == 33) {
				SellerCourierTcsUae::updateOrCreate(['seller_id' => Auth::id(), ], ['api_key' => $request->api_key, 'account_title' => $request->account_title, 'user_id' => $request->user_id, 'account_number' => $request->account_number]);
			}
			else if ($request->courier_id == 34) {
				SellerCourierDaewoo::updateOrCreate(['seller_id' => Auth::id(),'courier_id' => $request->courier_id ], ['api_key' => $request->api_key, 'password' => $request->password, 'user' => $request->user]);
            }
            else if ($request->courier_id == 35) {
				SellerCourierLCSMerchant::updateOrCreate(['seller_id' => Auth::id(), 'courier_id' => $request->courier_id], ['api_key' => $request->api_key, 'api_password' => $request->api_password, 'account_title' => $request->account_title, 'email' => $request->email]);
			}
			else if ($request->courier_id == 36) {
				SellerCourierFlyCourier::updateOrCreate(['seller_id' => Auth::id(), 'courier_id' => $request->courier_id], ['api_key' => $request->api_key, 'account_title' => $request->account_title]);
			}
			else if ($request->courier_id == 37) {
				SellerCourierSmsa::updateOrCreate(['seller_id' => Auth::id(), 'courier_id' => $request->courier_id], ['pass_key' => $request->pass_key, 'account_title' => $request->account_title]);
			}
			else if ($request->courier_id == 38) {
				$auth = ($request->authorization ? $request->authorization : "");
				SellerCourierPostExPartner::updateOrCreate(['seller_id' => Auth::id(),'courier_id' => $request->courier_id ], ['authorization' => $auth, 'token' => $request->token, 'account_title' => $request->account_title]);
			}
			else if ($request->courier_id == 39) {
				SellerCourierTQS::updateOrCreate(['seller_id' => Auth::id()], ['client_code' => $request->client_code, 'profile_id' => $request->profile_id, 'auth_key' => $request->auth_key, 'account_title' => $request->account_title]);
			}
			else if ($request->courier_id == 40) {
				SellerCourierDEX::updateOrCreate(['seller_id' => Auth::id()], ['external_seller_id' => $request->external_seller_id, 'account_title' => $request->account_title]);
			}
			else if ($request->courier_id == 41) {
				SellerCourierCityMail::updateOrCreate(['seller_id' => Auth::id()], ['customer_acno' => $request->customer_acno, 'account_title' => $request->account_title]);
			}
			else if ($request->courier_id == 42) {
				SellerCourierJLD::updateOrCreate(['seller_id' => Auth::id()], ['api_token' => $request->api_token, 'account_title' => $request->account_title]);
			}
			else if ($request->courier_id == 43) {
				\App\Models\Courier\SellerCourierMoveo::updateOrCreate(
					['seller_id' => Auth::id()],
					['api_token' => $request->api_token, 'account_title' => $request->account_title]
				);
			}

			$sellerServices = \DB::table('couriers')
            ->join('courier_services', 'courier_services.courier_id', '=', 'couriers.id')
            ->join('seller_courier_services', 'courier_services_id', '=', 'courier_services.id')
            ->select('courier_services.*')->where('seller_courier_services.seller_id', Auth::id())->where('courier_services.courier_id', $request->courier_id)
            ->get();
            $del = SellerCourierService::where('seller_id', Auth::id())->whereIn('courier_services_id', $sellerServices->pluck('id'))->get();
            SellerCourierService::destroy($del->pluck('id')->toArray());

            if (isset($request->service_enable)) {
				foreach ($request->service_enable as $key=>$val) {
					$default = ( $request->default_service == $key ? 1 : 0 );
                    SellerCourierService::create(['courier_services_id' => $key, 'seller_id' => Auth::id(), 'default' => $default]);
                }
            }
			

			if($request->courier_id == 17){

				// return $request->all();
				$seller_c = SellerCourier::whereSellerId(Auth::id())->whereCourierId($request->courier_id)->first();
				if(isset($request->mps_seller)){
					$seller_c->multi_package_shipment = $request->mps_seller;
					$seller_c->save();

				} else{
					if($seller_c->multi_package_shipment == 1){
						$seller_c->multi_package_shipment = 0;
						$seller_c->save();
					}
				}
				

				$ds = SellerPaymentInfoOption::whereSellerId(Auth::id())->get();
				if($ds){
					foreach ($ds as $d) {
						$d->active = 0;
						$d->default = 0;
						$d->save();
					}
				}
				$default_val = 0;
				if(isset($request->payment_info_option_enable)){
					
					foreach ($request->payment_info_option_enable as $key => $data) {
						$sell = SellerPaymentInfoOption::where('payment_info_options_id',$key)->whereSellerId(Auth::id())->first();
						if($sell){
							Log::info('bbbb');
							$sell->active = 1;
						} else{
							Log::info('cccc');
							$sell = new SellerPaymentInfoOption();
							$sell->payment_info_options_id = $key;
							$sell->seller_id = Auth::id();
							$sell->active = 1;
						}

						if($request->default_payment_info_option == $key || count($request->payment_info_option_enable) == 1){
							$sell->default = 1;
							$default_val++;
						} else{
							$sell->default = 0;
						}
						$sell->save();
					}
				}
				if($default_val == 0){
					$sell = SellerPaymentInfoOption::whereSellerId(Auth::id())->first();
					$sell->default = 1;
					$sell->save();
				}
			}

            return Redirect::route('seller_settings_couriers_view')->with($message_status, $message);
        }

        public function courier_limit()
        {
            DB::table('seller_couriers')->update(['current_booking' => 0]);
        }

        public function courier_limit_view()
        {
            $page_title = 'Daily Courier Limit';
            $default_courier = Setting::whereSellerId(Auth::id())->where('key', config('enum.settings')['DEFAULTCOURIER'])->first();
            $default_courier_service = Setting::whereSellerId(Auth::id())->where('key', config('enum.settings')['DEFAULTCOURIERSERVICE'])->first();

            if ($default_courier) {
                $order_controller = new OrderController;
                $courier_services = ($order_controller->dispatchCourierService($default_courier->value))['courier_services'];
            } else {
                $courier_services = [];
            }

            $couriers = SellerCourier::whereSellerId(Auth::id())->where('status', '1')->with('courier')->get();

            return view('seller.settings.courier_limit.home', compact('couriers', 'page_title', 'default_courier', 'default_courier_service', 'courier_services'));
        }

        public function courier_limit_save(Request $request)
        {
            foreach ($request->except('_token') as $key => $value) {
                SellerCourier::whereId($key)->update(['max_booking' => $value]);
            }

            return redirect('seller/settings/courier_limit')->with('status', 'Couriers Limit updated');
        }

        public function default_courier_save(Request $request)
        {
            Setting::updateOrCreate(['seller_id' => Auth::id(), 'key' => config('enum.settings')['DEFAULTCOURIER']], ['value' => $request->courier]);
            Setting::updateOrCreate(['seller_id' => Auth::id(), 'key' => config('enum.settings')['DEFAULTCOURIERSERVICE']], ['value' => $request->courier_service]);

            return redirect('seller/settings/courier_limit')->with('status', 'Default Couriers updated');
        }

		public function generatePandagoKeys()
		{
			try{
				$privateKey = openssl_pkey_new(array(
					'private_key_bits' => 2048,      // Size of Key.
					'private_key_type' => OPENSSL_KEYTYPE_RSA,
				));

				openssl_pkey_export($privateKey, $privKey);
				$private_key = Storage::put('pandago_keys/'.auth()->id().'/private_key.pem',$privKey);
				// dd(path('pandago_keys/'.auth()->id().'/private_key.pem'));

				$a_key = openssl_pkey_get_details($privateKey);
				$public_key = Storage::put('pandago_keys/'.auth()->id().'/public_key.pub',$a_key['key']);

				SellerCourierPandaGo::updateOrCreate(['seller_id' => Auth::id(), 'courier_id' => 24], ['public_key' => 'pandago_keys/'.auth()->id().'/public_key.pub', 'private_key' => 'pandago_keys/'.auth()->id().'/private_key.pem']);

				return array('message' => 'Keys generated successfully', 'error' => 'false');

			} catch(Exception $e){
				return array('message' => $e->getMessage(), 'error' => 'true');
			}
		}

		/// COURIER PERFORMANCES
		///
		///
		///


		public function performanceInCities()
		{
			ini_set('max_execution_time', '0');

			$query = DB::connection('mysql2')
                ->table('shipment_report_fields')
				->whereNotIn('seller_id',[1,75,119,345,398,414,417])
                ->whereDate('shipment_dispatched_date','>=', now()->subDays(30)->toDateTimeString())
				->select('courier_id', 'courier_service_value', 'destination_city', 'pickup_city',
						DB::raw('AVG(case when `shipment_delivered_date` > `shipment_dispatched_date` then (TIME_TO_SEC(TIMEDIFF(shipment_delivered_date, shipment_dispatched_date))/86400) end) AS result_value '),
						DB::raw('AVG(case when `cod_received_at` > `shipment_delivered_date` then (TIME_TO_SEC(TIMEDIFF(cod_received_at, shipment_delivered_date))/86400) end) AS delivered_to_payment_time '),
						DB::raw('AVG(case when `cod_received_at` > `shipment_dispatched_date` then (TIME_TO_SEC(TIMEDIFF(cod_received_at, shipment_dispatched_date))/86400) end) AS dispatched_to_payment_time '),
						DB::raw("(SUM(case when `shipment_delivered_date` is not NULL then 1 else 0 end)/count(id)) * 100 AS delivery_ratio"))
				->groupBy(['courier_id', 'courier_service_value', 'destination_city', 'pickup_city'])
				->get();

			$cities = City::whereIn('name', $query->pluck('destination_city')->merge($query->pluck('pickup_city')) )->get(['id', 'name'])->keyBy('name');
			$couriers = Courier::get(['id', 'name']);

			CourierPerformanceInCities::truncate();
			$count = 0;

			foreach ($query as $value) {

				$pickup_city = isset($cities[$value->pickup_city]) ? $cities[$value->pickup_city] : Null;
				$destination_city = isset($cities[$value->destination_city]) ? $cities[$value->destination_city] : Null;
				$courier = $couriers->where('id', $value->courier_id)->first();

				if ($pickup_city && $destination_city && $courier) {
					
					$courierPerformanceInCities = new CourierPerformanceInCities;
					$courierPerformanceInCities->pickup_city_id = $pickup_city->id;
					$courierPerformanceInCities->pickup_city_name = $value->pickup_city;
					$courierPerformanceInCities->destination_city_id = $destination_city->id;
					$courierPerformanceInCities->destination_city_name = $value->destination_city;
					$courierPerformanceInCities->courier_id = $value->courier_id;
					$courierPerformanceInCities->courier_name = ( $value->courier_id == 1 ? 'Self' : $courier->name );
					$courierPerformanceInCities->courier_service_value = $value->courier_service_value;
					$courierPerformanceInCities->duration_in_days = number_format($value->result_value,4);
					$courierPerformanceInCities->delivered_to_payment_time = number_format($value->delivered_to_payment_time,4);
					$courierPerformanceInCities->dispatched_to_payment_time = number_format($value->dispatched_to_payment_time,4);
					$courierPerformanceInCities->delivery_ratio = number_format($value->delivery_ratio,4);
					$courierPerformanceInCities->save();
				}
				$count++;
			}
			return $count.' rows added';
		}
		
		public function performanceOverall()
		{
			$query = DB::connection('mysql2')
                ->table('shipment_report_fields')
				->whereNotIn('seller_id',[1,75,119,345,398,414,417])
                ->whereDate('shipment_dispatched_date','>=', now()->subDays(30)->toDateTimeString())
				->select('courier_id','courier_service_value',
						DB::raw('AVG(case when `shipment_delivered_date` > `shipment_dispatched_date` then (TIME_TO_SEC(TIMEDIFF(shipment_delivered_date, shipment_dispatched_date))/86400) end) AS result_value '),
						DB::raw('AVG(case when `cod_received_at` > `shipment_delivered_date` then (TIME_TO_SEC(TIMEDIFF(cod_received_at, shipment_delivered_date))/86400) end) AS delivered_to_payment_time '),
						DB::raw('AVG(case when `cod_received_at` > `shipment_dispatched_date` then (TIME_TO_SEC(TIMEDIFF(cod_received_at, shipment_dispatched_date))/86400) end) AS dispatched_to_payment_time '),
						DB::raw("(SUM(case when `shipment_delivered_date` is not NULL then 1 else 0 end)/count(id)) * 100 AS delivery_ratio"))
				->groupBy(['courier_id','courier_service_value'])
				->get();

			$couriers = Courier::get(['id', 'name']);
			CourierPerformance::truncate();
			$count = 0;

			foreach ($query as $value) {

				$courier = $couriers->where('id', $value->courier_id)->first();

				if ($courier) {
					$courierPerformance = new CourierPerformance;
					$courierPerformance->courier_id = $value->courier_id;
					$courierPerformance->courier_name = $courier->name;
					$courierPerformance->courier_service_value = $value->courier_service_value;
					$courierPerformance->duration_in_days = number_format($value->result_value,4);
					$courierPerformance->delivered_to_payment_time = number_format($value->delivered_to_payment_time,4);
					$courierPerformance->dispatched_to_payment_time = number_format($value->dispatched_to_payment_time,4);
					$courierPerformance->delivery_ratio = number_format($value->delivery_ratio,4);
					$courierPerformance->save();
				}

				$count++;
			}
			return $count.' rows added';
		}

		public function enableUniversal($courier_id)
		{
			
			$data['seller'] = Seller::find(auth()->id());
			//changing email on request of Shoaib
			$data["email"] = "<EMAIL>";
			$data["title"] = "Universal Account Request";
			$data["body"] = "Seller (".auth()->id().")  requested a universal account of Courier ID ".$courier_id;
	
			if($data['seller']->entity_type == 'Individual') {

				$files = [
					url($data['seller']->cnic_front),
					url($data['seller']->cnic_back),
				];

			} else {

				$files = [
					url($data['seller']->certificate),
				];
			}
	
			Mail::send('email.universal_account_request', $data, function($message)use($data, $files) {
				
				$message->to($data["email"], $data["email"])
					->subject($data["title"]);
	
				foreach ($files as $file) {
					$message->attach($file);
				}
				
			});

			SellerCourier::updateOrCreate(['seller_id' => auth()->id(), 'courier_id' => $courier_id], ['status' => 1, 'is_universal' => 1 ]);
			return Redirect::route('seller_settings_couriers_view')->with('status', 'Universal request is under review');
		}

		public function disableUniversal($courier_id)
		{
			SellerCourier::updateOrCreate(['seller_id' => auth()->id(), 'courier_id' => $courier_id], ['is_universal' => 0 ]);
			return Redirect::route('seller_settings_couriers_view')->with('status', 'Universal courier is now de-activated');
		}

    }
