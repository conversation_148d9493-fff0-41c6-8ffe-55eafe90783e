<?php

namespace App\Http\Controllers;

use App\Helpers\StorefrontOrderCancellation;
use App\Models\Courier\SellerCourier;
use App\Models\FulfillmentOrder;
use App\Models\OrderComments;
use App\Models\Reason;
use App\Models\ReturnReceive;
use App\Models\ReturnReceiveItem;
use App\Models\SellerFfcLocation;
use App\Models\Setting;
use App\Models\Shipment;
use App\Models\ShipmentReason;
use App\Models\StockOrder;
use App\Service\FFC\StockOrder\PutawayService;
use Exception;
use Illuminate\Http\Request;
use App\Models\AddOn;
use App\Helpers\FBR;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Product;
use App\Models\SellerLocation;
use App\Models\SellerUser;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class ReturnReceivedController extends Controller
{

    public function index()
    {
        $data['page_title'] = 'Return Receiving';
        $data['couriers'] = SellerCourier::where('seller_id',auth()->id())->where('status',1)->orderBy('courier_id')->with('activeCourier')->has('activeCourier')->get();
        $data['barcodes_arr'] = [];
        $data['order_items'] = [];
        $data['auto_bins'] = [];
        return view('seller.return-received.home')->with($data);
    }

    public function getShipments(Request $request)
    {
        $data['page_title'] = 'Return Receiving';
        $data['reasons'] = Reason::where('seller_id', auth()->id())->where('key', 'shipment')->where('activated', '1')->get();
        $data['couriers'] = SellerCourier::where('seller_id',auth()->id())->where('status',1)->orderBy('courier_id')->with('activeCourier')->has('activeCourier')->get();
        $data['shopify'] = Setting::isShopify(auth()->id());
        $tracking_number = $request->shipment;
        if(Setting::forcefulReceiving(Auth::id())){
            $shipment = Shipment::where('seller_id', auth()->id())->where('tracking_number', $tracking_number)->whereNotIn('status', [config('enum.shipment_status')['RETURNED_RECEIVED']])->with(['items.order_item','orderMarketplaceReferenceId'])->first();
        }else{
            $shipment = Shipment::where('seller_id', auth()->id())->whereIn('status', [config('enum.shipment_status')['RETURNED'],config('enum.shipment_status')['CANCELLED']])->where('tracking_number', $tracking_number)->with(['items.order_item','orderMarketplaceReferenceId'])->first();
        }

        if (!$shipment) {
            $shipment = Shipment::where('seller_id', auth()->id())->whereIn('status', [config('enum.shipment_status')['DELIVERED']])->where('type','reverse')->where('tracking_number', $tracking_number)->with(['items.order_item','orderMarketplaceReferenceId'])->first();

            if (!$shipment) {
                return back()->with('error', 'No return / cancelled shipment found ');
            }
        }

        $counter = 100;
        $order_items_arr = [];
        $barcodes_arr = [];
        foreach($shipment->items as $key => $item){
            $barcodes_arr[$item->order_item->product->barcode]['counter'] = $counter++;
            $barcodes_arr[$item->order_item->product->barcode]['order_item_id'] = $item->order_items_id;
            $order_items_arr[] = $item->order_items_id;;
        }

        $seller_location_id = 0;

        if (AddOn::ffc(auth()->id())) {
            $seller_locations = SellerLocation::where('seller_id',auth()->id())->where('is_ffc_enabled',1)->get();
        } else {
            $seller_locations = SellerLocation::where('seller_id',auth()->id())->get();
        }

        if(session()->has('user')){
            $seller_location_id = SellerUser::whereId(session('user')->id)->value('order_pickup_location');
            if($seller_location_id == "" || $seller_location_id == null){
                return redirect('seller/order')->with('error', 'You don\'t have permission to access that page as you are not assigned with any location. <code>');
            }
        }

        if($seller_locations->count() > 0){
            $seller_location_id = $seller_locations[0]->id;
        }

        $data['shipment'] = $shipment;
        $data['barcodes_arr'] = $barcodes_arr;
        $data['order_items'] =$order_items_arr;
        $data['seller_locations'] =$seller_locations;
        $data['seller_location_id'] =$seller_location_id;
        $data['partial_return_receiving'] = Setting::partialReturnReceiving(Auth::id()) ?? null;
        $data['auto_bins']['Received'] = Setting::returnReceivingReceivingType(Auth::id());
        $data['auto_bins']['Damaged'] = Setting::returnReceivingDamagedType(Auth::id());
        $data['auto_bins']['Missing'] = Setting::returnReceivingMissingType(Auth::id());

        $data['auto_bins']['Received'] = $data['auto_bins']['Received'] ? SellerFfcLocation::whereId($data['auto_bins']['Received'])->first(['id', 'location_name']) : null;
        $data['auto_bins']['Damaged'] = $data['auto_bins']['Damaged'] ? SellerFfcLocation::whereId($data['auto_bins']['Damaged'])->first(['id', 'location_name']) : null;
        $data['auto_bins']['Missing'] = $data['auto_bins']['Missing'] ? SellerFfcLocation::whereId($data['auto_bins']['Missing'])->first(['id', 'location_name']) : null;

        return view('seller.return-received.home')->with($data);
    }



    public function markShipments(Request $request)
    {
        $return_receiving_data = json_decode($request->input('return_receiving_data'), true);
        $tracking_number = $request->shipments_id;
        $stock_order_id = null;
        $shipment = Shipment::find($tracking_number);

        $status_before_update = $shipment->status;

        $shipment->status = config('enum.shipment_status')['RETURNED_RECEIVED'];
        $shipment->return_received_at_location = $request->seller_location_id;
        $shipment->save();

        $shipment->shipment_history(config('enum.shipment_status')['RETURNED_RECEIVED'], now());

        if (AddOn::ffc($shipment->seller_id)) {
            
            if ($status_before_update == 'Cancelled') {
                $type = config('enum.stock_order_types')['CANCELLED-RETURN'];
            } else {
                $type = config('enum.stock_order_types')['RETURN'];
            }
            
            $result = $shipment->getItemsProductDataWhileReceivingReturn();

            if ($result['error']) {
                return redirect()->back()->withInput($request->all())->with('error', 'Stock Order Not Created | '.$result['message']);
            }

            $products = $result['data'];

            if ($products) {
                $products = json_decode(json_encode($products));
            } else {
                return redirect()->back()->withInput($request->all())->with('error', 'Stock Order Not Created | Shipment item sku not found in products');
            }
            $stock_order_id = StockOrder::creationProcess(now(), $shipment->return_received_at_location, 'User', $shipment->order->marketplace_reference_id, $products, $type,'Received');

            $putaway_service = new PutawayService;
            $putaway_service->returnReceivedPutaway($stock_order_id, $return_receiving_data);

        }

        $fulfillment_order_id = FulfillmentOrder::whereShipmentId($shipment->id)->value('id');
        $fulfillment_order_id = !$fulfillment_order_id ? FulfillmentOrder::whereOrderId($shipment->order_id)->value('id') : $fulfillment_order_id;
        $this->addEntry($request->all(), $return_receiving_data, $stock_order_id, $shipment->seller_id, $shipment->id, $shipment->courier_id, $shipment->order_id, $fulfillment_order_id);

        $order = Order::find($shipment->order_id);

        if($order->status != config('enum.order_status')['COMPLETED'] && $status_before_update != 'Cancelled' && Setting::forcefulReceiving(Auth::id())){

            foreach($shipment->items as $item){
                $order_item = OrderItem::find($item->order_items_id);
                $order_item->status = config('enum.item_status')['RETURNED'];
                $order_item->save();
            }

            $order_items = OrderItem::where('order_id',$order->id)->get(['status']);
            $change_status = FALSE;
            $count = 0;
            $done = 0;

            $completed = 0;
            foreach ($order_items as $item) {
            if ( in_array($item->status, [config('enum.item_status')['RETURNED'], config('enum.item_status')['COMPLETED']]) ) {
                    $done++;
                    $completed++;
                    $change_status = TRUE;
                } 
                $count++;
            }

            if ($count == $completed) {
                $order = Order::find($order->id);
                $order->status = config('enum.order_status')['COMPLETED'];
                $order->save();
            }
            
        }

        // if ($request->has('re_stock_shopify')) {
        //     StorefrontOrderCancellation::initiate($shipment->order_id);
        // }

        if (AddOn::fbr($shipment->seller_id) && Setting::fbr($shipment->seller_id)) {
            FBR::sendReturnPosting($shipment);
        }

        return redirect('seller/return-received')->with('status', 'Shipments Mark received successfully');
    }

    function ffcBinAgainstBarcode($barcode)
    {
        $error = 0; $message = null; $data = null;
        $ffc_location = SellerFfcLocation::whereSellerId(auth()->id())->where('barcode', $barcode)->where('is_leaf', 1)->first(['id', 'location_name']);

        if ($ffc_location) {

            $message = 'FFC Bin Found';
            $data = $ffc_location->toArray();
            
        } else {

            $error = 1;
            $message = 'FFC Bin not found against barcode #'.$barcode;
        }

        return compact('error', 'message', 'data');
    }

    function addEntry($data, $return_receiving_data, $stock_order_id, $seller_id, $shipment_id, $courier_id, $order_id, $fulfillment_order_id)
    {
        try {

            $return_receive_items_data = array_merge($return_receiving_data['scannedArray'], $return_receiving_data['unScannedArray']);

            $return_receive = new ReturnReceive();
            $return_receive->seller_id = $seller_id;
            $return_receive->shipment_id = $shipment_id;
            $return_receive->courier_id = $courier_id;
            $return_receive->order_id = $order_id;
            $return_receive->fulfillment_order_id = $fulfillment_order_id;
            $return_receive->stock_order_id = $stock_order_id;
            $return_receive->seller_location_id = $data['seller_location_id'];
            $return_receive->received_by = ( auth()->check() ? (session()->has('user') ? 'Sub User' : 'Admin') : 'Auto');
            $return_receive->received_by_name = ( auth()->check() ? (session()->has('user') ? session('user')->full_name : auth()->user()->full_name) : 'System');
            $return_receive->received_by_id = ( auth()->check() ? (session()->has('user') ? session('user')->id : $seller_id ) : 1);
            $return_receive->save();

            $files_data = [];

            foreach ($data as $key => $value) {
                
                $temp_key = explode('_', $key);

                if (isset($temp_key[1]) && $temp_key[1] == 'fileUpload' && $file_path = $value->store('return-received/'.$seller_id.'/', 'public')) {
                    $files_data[$temp_key[0]] = $file_path;
                }
            }

            $received_items_count = 0;
            $message = '';

            foreach ($return_receive_items_data as $value) {

                if ($value['reason'] == 'Received') {
                    $received_items_count ++;
                }

                $return_receive_item = new ReturnReceiveItem();
                $return_receive_item->seller_id = $seller_id;
                $return_receive_item->product_id = Product::whereSellerId($seller_id)->whereBarcode($value['barcode'])->value('id');
                $return_receive_item->return_receive_id = $return_receive->id;
                $return_receive_item->barcode = $value['barcode'];
                $return_receive_item->seller_ffc_location_id = ( $value['bin'] ? $value['bin']['id'] : null );
                $return_receive_item->quantity = $value['quantity'];
                $return_receive_item->reason = $value['reason'];
                $return_receive_item->file_path = ( $value['reason'] == 'Damaged' && isset($files_data[$value['barcode']]) ? $files_data[$value['barcode']] : null );
                $return_receive_item->save();

                $message .= '<br> Product Barcode : <b>'.$value['barcode'].' ('.$value['quantity'].') </b>'.(isset($value['bin']) ? '  | Bin : <b>'.$value['bin']['location_name'].'</b>' : '').' | Reason : <b>'.$value['reason'].'</b>';
            }

            if ($received_items_count != count($return_receive_items_data)) {
                $message = '<b>Return Received Partially</b>'.$message;
            } else {
                $message = '<b>Return Received Completely</b>'.$message;
            }

            OrderComments::add($order_id, 'Return Received Process', $message , 'Success', '1');

        } catch (Exception $e) {
            Log::critical('Return received add entry failed | '.$e->getMessage(), $e->getTrace());
        }
    }

}
