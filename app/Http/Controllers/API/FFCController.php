<?php
   
namespace App\Http\Controllers\API;
use App\Models\FulfillmentOrderItem;
use App\Models\PickAndPutawayLog;
use App\Models\SellerUserProcessFlag;
use App\Models\StockTransferCommittedInventory;
use App\Models\StockTransferFulfillmentOrder;
use App\Models\StockTransferFulfillmentOrderItem;
use App\Service\FFC\StockOrder\PutawayService;
use Illuminate\Http\Request;
use App\Http\Controllers\API\BaseController as BaseController;
use App\Models\AddOn;
use App\Models\Inventory;
use Illuminate\Support\Facades\Auth;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\StockOrder;
use App\Models\StockOrderItem;
use Illuminate\Support\Facades\Validator;
use App\Models\Product;
use App\Models\SellerFfcLocation;
use App\Models\SellerFfcInventory;
use App\Models\OrderTempLocation;
use App\Models\SellerLocation;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use App\Models\OrderTag;
use App\Models\Tag;
use App\Models\OrderComments;
use App\Models\AutoShipped;
use App\Models\FulfillmentOrder;
use App\Models\FulfillmentOrderItemFFCLocation;
use App\Models\OrderPickList;
use App\Models\Setting;
use Error;
use Exception;
use Illuminate\Support\Facades\Log;
use App\Models\SellerUserPermission;
use App\Models\SellerStockMovement;
use App\Models\GRNPostingReceipt;
use App\Models\PickListInventoryNotAvailableLogs;
use App\Models\SellerUser;
use Illuminate\Support\Facades\Cache;

class FFCController extends BaseController
{

    public function getInfo($id)
    {
        $order = Order::where('id', $id)->first();
        return $this->handleResponse($order, 'Task retrieved.');
    }
    public function getStockOrderRequest(Request $request){
        try{
            $sub_user = Auth::user();

            $permission = SellerUserPermission::where('seller_user_id',$sub_user->id)->value('ffc_app_security');
            if($permission == 0){
                $message = "Permission Denied";
                return $this->handleError('Exception.', ['error'=>$message]);
            }

            $seller_id=$sub_user->seller_id;
            $location_id =$sub_user->order_pickup_location;
             $stock_orders = StockOrder::where('seller_id', $seller_id)->where('location_id',$location_id)->whereNotIn('status',["Received","Transfer Pending",'Cancelled'])->orderBy('id')->get();
             
             if($stock_orders) {
                $barcodes_arr = [];
                $result = [];

                foreach($stock_orders as $stock_order){
                    $barcodes_arr[strtolower($stock_order->reference_id)]['so_id'] = $stock_order->id;    
                }

                $result['stock_orders'] =  $stock_orders;
                $result['barcodes_arr'] =  $barcodes_arr;
                 return $this->handleResponse($result, 'Stock Orders retrieved.');
         
             }else{
                 return $this->handleError('Error.', ['error'=>"No Stock Orders Found"]);
             }
        }catch (\Throwable $e) {
            return $this->handleError('Exception.', ['error'=>$e->getMessage()]);
        }
    }
    public function updateStockOrderRequest(Request $request){

        $validator = Validator::make($request->all(), [
            'stock_order_id' => 'required | numeric'           
        ]);

        \Log::info($request->all());

        if ($validator->fails()) {
            return $this->handleError($validator->errors(),'');
        }
        try{
            $store_order_item_count = 0;
            $store_order_item_all_received_count = 0;
            $sub_user = Auth::user();
            $seller_id = $sub_user->seller_id;
            $seller_sub_user_id = $sub_user->id;
            $is_over_delivery_allowed = false;
            $processing_allowed = true;

            $stock_order = StockOrder::where('id', $request->stock_order_id)->first();

            foreach($request->products as $data){
                $stock_order_item = StockOrderItem::where('stock_order_id', $request->stock_order_id)->where('id',$data['id'] );

                if (Setting::overDelivery($seller_id)) {
                    $stock_order_item = $stock_order_item->whereRaw('(qty - qty_received) > 0');          
                    $is_over_delivery_allowed = true;          
                }

                $stock_order_item = $stock_order_item->first();

                if($stock_order_item){
                    $qty_received = $stock_order_item->qty_received;
                    $actual_qty  = $stock_order_item->qty;
                    $temp_qty = $qty_received + $data['qty'];
                    
                    // ensuring over delivery is rejected in case it is not allowed from settings
                    if(!$is_over_delivery_allowed){ $processing_allowed = ($temp_qty <= $actual_qty ? true: false );}

                    if($temp_qty != 0 && $processing_allowed){
                        //to verify if complete inventory was received for an item
                        if($temp_qty >= $actual_qty){
                            $store_order_item_all_received_count = $store_order_item_all_received_count + 1;
                        }
                        //only save if there was quantity
                        if($data['qty'] > 0){
                            $stock_order_item->qty_received = $qty_received + $data['qty'];
                            $stock_order_item->save();

                            GRNPostingReceipt::add(
                                $seller_id,
                                $request->stock_order_id,
                                $stock_order_item->id,
                                $stock_order->location_id,
                                $stock_order_item->product_id,
                                $data['qty'],
                                $seller_sub_user_id
                            );

                            /// If putaway is also trigger at the time of receiving
                            if ($request->putaway_location) {

                                $putaway_service = new PutawayService;
                                $putaway_service->execute($sub_user, $stock_order_item->product_id, $request->putaway_location, $data['qty'], $request->stock_order_id);
                            }

                        }
                        //increment if atleast some or complete inventory was recieved
                        $store_order_item_count = $store_order_item_count + 1;
                    }
                }  
            }

            // empty unique id generated from ffc app when the transaction was successfull
            if(isset($request->unique_id) && $request->unique_id != "" ){
                Cache::forget($request->unique_id);
            }

            if(!StockOrderItem::where('stock_order_id', $request->stock_order_id)->whereRaw('qty_received < qty')->first()){
                $stock_order->status = "Received";
                $stock_order->save();
                return $this->handleResponse('', null);
            }elseif($store_order_item_all_received_count < $stock_order->items->count() && $store_order_item_count !=0){
                $stock_order->status = "Partial";
                $stock_order->save();
                return $this->handleResponse('', 'Security scan Completed Partially.');
            }
        
            return $this->handleError('Error.', ['error'=>"No or Incorrect Quantity Found!"]);
        }catch (\Throwable $e) {
            return $this->handleError('Exception.', ['error'=>$e->getMessage()]);
       }
    }
    public function getStockOrderRequestItems(Request $request){

        $validator = Validator::make($request->all(), [
            'stock_order_id' => 'required | numeric'           
        ]);
        $sub_user = Auth::user();

        if ($validator->fails()) {
            return $this->handleError($validator->errors(),'');
        }

        try{           
            $seller_id=$sub_user->seller_id;
            $stock_order_items = StockOrderItem::where('stock_order_id', $request->stock_order_id)
                                    ->join('products','stock_order_items.product_id','products.id')
                                    ->selectRaw('stock_order_items.id,stock_order_items.qty,stock_order_items.qty_received,products.sku,products.barcode')
                                    ->get();
            $stock_order = StockOrder::where('id', $request->stock_order_id)->whereNotIn('status',["Received","Transfer Pending",'Cancelled'])->where('seller_id', $seller_id)->whereNotIn('status',["Received","Transfer Pending",'Cancelled'])->where('seller_id', $seller_id)->first(['type']);

            $result = [];
            $over_delivery_stock_orders_settings = Setting::where('seller_id', $sub_user->seller_id )->where('key',config('enum.settings')['OVER-DELIVERY-STOCK-ORDERS']);
            $item_wise_at_receving_desk_settings = Setting::where('seller_id', $sub_user->seller_id)->where('key',config('enum.settings')['ITEM-WISE-SCANNING-AT-RECEIVING-DESK']);

            if($stock_order_items && $stock_order) {
                $result['stock_order_items'] = $stock_order_items; 
                $result['over_delivery_settings'] = 0;
                // this is to make sure that over delivery is only allowed on purchase orders FLH-1613
                if($over_delivery_stock_orders_settings->exists() && $over_delivery_stock_orders_settings->value('value') == 1 && $stock_order->type == config('enum.stock_order_types')['NORMAL']){
                    $result['over_delivery_settings'] = 1;
                }
                $result['item_wise_settings'] =  ($item_wise_at_receving_desk_settings->exists()) ? $item_wise_at_receving_desk_settings->value('value') : 0;

                if ($permission = SellerUserPermission::where('seller_user_id',$sub_user->id)->where('ffc_app_item_counting_mode',1)->first(['item_counting_mode']) ) {
                    $result['item_wise_settings'] = $permission->item_counting_mode;
                }


                $barcodes_arr = [];
                $counter = 100;
                foreach($stock_order_items as $stock_order_item){
                    $barcodes_arr[$stock_order_item->barcode]['counter'] = $counter++;
                    $barcodes_arr[$stock_order_item->barcode]['order_item_id'] = $stock_order_item->id;    
                }

                $result['barcodes_arr'] =  $barcodes_arr;

                 return $this->handleResponse($result, 'Stock order items request Retrieved.');
         
             }else{
                 return $this->handleError('Error.', ['error'=>"No stock orders request Found"]);
             }
        }catch (\Throwable $e) {
            return $this->handleError('Exception.', ['error'=>$e->getMessage()]);
        }
    }
    public function updatePutaway(Request $request){

        $validator = Validator::make($request->all(), [
            'product_barcode' => 'required ',
            'location_barcode' => 'required',           
            'qty' => 'required | numeric' ,
        
        ]);

        if ($validator->fails()) {
            return $this->handleError($validator->errors(),'');
        }
        $sub_user = Auth::user();
        $sub_user_location_id =$sub_user->order_pickup_location;

        $permission = SellerUserPermission::where('seller_user_id',$sub_user->id)->value('ffc_app_putaway_inbound');
        if($permission == 0){
            $message = "Permission Denied";
            return $this->handleError('Unauthorised.', ['error'=>$message]);
        }
        try{       
        
            $product = Product::where('barcode', $request->product_barcode)->where('seller_id', $sub_user->seller_id)->first();
            $seller_ffc_location = SellerFfcLocation::where('barcode', $request->location_barcode)->where('seller_id', $sub_user->seller_id)->where('location_id',$sub_user_location_id)->first();

            if($product && $seller_ffc_location){

                // Writing direct query to avoid any performance clash
                $stock_order = DB::table('stock_orders')
                ->join('stock_order_items', 'stock_orders.id', '=', 'stock_order_items.stock_order_id')
                ->select(DB::raw('(sum(qty_received) - sum(putaway_qty))  as total_qty'))->where('stock_order_items.product_id',  $product->id)->where('stock_orders.location_id', $seller_ffc_location->location_id)
                ->get();
                $quantity_available = ($stock_order[0]->total_qty != null ? (int)$stock_order[0]->total_qty : 0);
                if($quantity_available >= $request->qty){
                    $seller_ffc_inventory = SellerFfcInventory::where('seller_ffc_location_id', $seller_ffc_location->id)->where('product_id', $product->id)->where('seller_id', $sub_user->seller_id)->first();
                    if($seller_ffc_inventory){
                        $seller_ffc_inventory->qty = $seller_ffc_inventory->qty + $request->qty;
                        $seller_ffc_inventory->uncommitted_qty = $seller_ffc_inventory->uncommitted_qty + $request->qty;
                    }else{
                        $seller_ffc_inventory = new SellerFfcInventory;
                        $seller_ffc_inventory->seller_id = $sub_user->seller_id;
                        $seller_ffc_inventory->seller_location_id =$seller_ffc_location->location_id;
                        $seller_ffc_inventory->seller_ffc_location_id = $seller_ffc_location->id;
                        $seller_ffc_inventory->product_id = $product->id;
                        $seller_ffc_inventory->qty = $request->qty;
                        $seller_ffc_inventory->uncommitted_qty = $request->qty;
                    }
                    $product_id =$product->id;
                    $putaway_qty = $request->qty;
                    $seller_ffc_inventory->save();

                    hold_data_for_internal_logger(
                        $sub_user->id,
                        $sub_user->seller_id,
                        Null,
                        'Ledger Inventory',
                        'stock, uncommitted_stock',
                        'Inventory Item'
                    );

                    Inventory::updateStock($product->id, $seller_ffc_location->location_id, $request->qty);

                    $stock_orders = StockOrder::where('seller_id', $sub_user->seller_id)->where('location_id',$seller_ffc_location->location_id)
                                                ->with(['items' => function($q) use( $product_id) {
                                                    $q->where('product_id', '=', $product_id); 
                                                }])->get();
                    foreach($stock_orders as $stock_order){
                        foreach($stock_order->items as $item){
                            if($putaway_qty != 0){
                                //this to validate if stock item putaway was completed or qty was not received against a single stock item
                                if($item->qty_received ==0 || $item->qty_received == $item->putaway_qty){
                                    continue;
                                }else{
                                    $qty_inhand = $item->qty_received - $item->putaway_qty;
                                    //this to validate if complete stock is available againt a single stock item
                                    if( $qty_inhand >= $putaway_qty){
                                        $item->putaway_qty = $item->putaway_qty + $putaway_qty;
                                        $item->save();
                                        $putaway_qty=0;
                                    }else{
                                        //this else if some of stock is available against a single item
                                        $putaway_qty = $putaway_qty - $qty_inhand;
                                        $item->putaway_qty = $item->putaway_qty + $qty_inhand;
                                        $item->save();
                                    }
                                }
                            }
                        }
                    }
                }else{
                    return $this->handleError('Error.', ['error'=>"Quantity available after security is not greater then ".$quantity_available." !"]);
                }
                
            }else{
                return $this->handleError('Error.', ['error'=>"Product or Barcode doesnt exist!"]);
            }
            
            return $this->handleResponse('', 'Putaway updated.');
         
        }catch (\Throwable $e) {
            return $this->handleError('Exception.', ['error'=>$e->getMessage()]);
        }
    }
    // public function getFFCOrdersAssignedByDom(Request $request){

    //     $sub_user = Auth::user();
    //     $permission = SellerUserPermission::where('seller_user_id',$sub_user->id)->value('ffc_app_picking');
    //     if($permission == 0){
    //         $message = "Permission Denied";
    //         return $this->handleError('Unauthorised.', ['error'=>$message]);
    //     }
    //     try {

    //         $assign_orders = FulfillmentOrder::subUserOpenPendingPDOrders($sub_user->id);

    //         $count = 1;
    //         $ids = "";
    //         foreach( $assign_orders as $ao){
    //             $ids = ($count == 1 ? $ao->id  : $ids . "," .$ao->id); 
    //             $count ++;
    //         }

    //         foreach ($assign_orders as $pick_list_order) {

    //             $data[] = [
    //                 "order_picking_ids" =>  $ids,
    //                 "order_ref_id" => Order::whereId($pick_list_order->order_id)->value('marketplace_reference_id') , 
    //                 "is_picked" => $pick_list_order->is_picked , 
    //                 "order_id" => $pick_list_order->id, 
    //                 "items_count" => $pick_list_order->items->count() ,
    //                 "created_at" => ($pick_list_order->created_at)->toDateTimeString()
    //             ];
    //         }
    
    //         if($data) {               
    //             return $this->handleResponse($data, 'FFC Order assigned by dom.');
    //         }else {
    //             return $this->handleError('No Orders Found.','');
    //         }
         
    //     }catch (\Throwable $e) {
    //         return $this->handleError('Exception.', ['error'=>$e->getMessage()]);
    //     }
    // }
    public function getFFCOrdersAssignedToUser(Request $request){

        $sub_user = Auth::user();
        $result = [];
        $all_picked = false;
        $current_pick_items = Null;
        $picking_mode = 0;
        $item_wise_scan_at_picking_settings = Setting::where('seller_id', $sub_user->seller_id)->where('key',config('enum.settings')['ITEM-WISE-SCANNING-AT-PICKING']);
        $item_wise_scan_at_picking_settings = $item_wise_scan_at_picking_settings->value('value');

        if ($permission = SellerUserPermission::where('seller_user_id',$sub_user->id)->where('ffc_app_item_counting_mode',1)->first(['item_counting_mode']) ) {
            $item_wise_scan_at_picking_settings = $permission->item_counting_mode;
        }

        try {
            
            $fulfillment_orders = FulfillmentOrder::subUserOpenPendingPDOrders($sub_user->id);


            foreach ($fulfillment_orders as $fulfillment_order) {

                if($fulfillment_order && $fulfillment_order->is_picked != 1 && in_array($fulfillment_order->seller_location_id, explode(',', $sub_user->order_pickup_location)) && $fulfillment_order->status != config('enum.fulfillment_order_status')['UNAVAILABLE']) {
    
                    $picked_items_count = 0;
                    $picking_mode = $fulfillment_order->picking_mode;

                    foreach ($fulfillment_order->items as $key => $item) {

                        $item_committed_inventory = FulfillmentOrderItemFFCLocation::where('fulfillment_order_item_id', $item->id)->get(['quantity', 'is_picked']);
                        $required_qty = $item->quantity - $item_committed_inventory->where('is_picked',1)->sum('quantity');

                        if (!$required_qty) {
                            $picked_items_count++;

                        } elseif($required_qty != $item_committed_inventory->where('is_picked',0)->sum('quantity')) {
                            Log::critical('Fulfillment order item has some uncommitted quantity | '.$item->id);
                        }
                    }

                    foreach (FulfillmentOrderItemFFCLocation::where('fulfillment_order_id', $fulfillment_order->id)->where('is_picked',0)->get() as $committed_entry) {
                        $result[] = ['tote_no' => $fulfillment_order->tote_no, 'reference_id' => $fulfillment_order->reference_id, 'fulfillment_order_id' => $fulfillment_order->id, 'seller_location_id' => $fulfillment_order->seller_location_id, 'inventory_id' => (SellerFfcInventory::whereProductId($committed_entry->product_id)->where('seller_ffc_location_id', $committed_entry->ffc_location_id)->value('id')), 'item_id' => $committed_entry->fulfillment_order_item_id,'product_id' => $committed_entry->product_id, 'product_name' => $committed_entry->product->product_name  ,'sku' => $committed_entry->product->SKU, 'barcode' => $committed_entry->product->barcode, 'quantity' => $committed_entry->quantity, 'ffc_location_id' => $committed_entry->ffc_location_id, 'inventory_path' => SellerFfcLocation::getInventoryPath($committed_entry->ffc_location_id), 'distance' => SellerFfcLocation::whereId($committed_entry->ffc_location_id)->value('sequence')];
                    }

                    if ($picked_items_count == $fulfillment_order->items->count()) {
                        $fulfillment_order->picked();
                        AutoShipped::auto_shipped_picked_order($fulfillment_order->order_id);
                    }
                }
            }

            if($result) {

                $result = collect($result)->sortBy([
                    ['distance', 'asc'],
                    ['sku', 'asc'],
                    ['ffc_location_id', 'asc'],
                ])->values()->all();

                $current_pick_items = [
                    'sku' => $result[0]['sku'],
                    'product_name' => $result[0]['product_name'],
                    'barcode' => $result[0]['barcode'],
                    'product_id' => $result[0]['product_id'],
                    'ffc_location_id' => $result[0]['ffc_location_id'],
                    'inventory_path' => $result[0]['inventory_path'],
                ];

                $result = collect($result);

                $current_pick_items['items'] = array_values($result->where('sku', $current_pick_items['sku'])->where('barcode', $current_pick_items['barcode'])->where('ffc_location_id', $current_pick_items['ffc_location_id'])->toArray());
                $current_pick_items['quantity'] = $result->where('sku', $current_pick_items['sku'])->where('barcode', $current_pick_items['barcode'])->where('ffc_location_id', $current_pick_items['ffc_location_id'])->sum('quantity');

                foreach (FulfillmentOrderItemFFCLocation::whereIn('fulfillment_order_id', $fulfillment_orders->pluck('id'))->where('is_picked',1)->get() as $key => $value) {
                    ($result)->push([ 'reference_id' => $fulfillment_orders->where('id', $value->fulfillment_order_id)->first()->reference_id,'sku' => $value->product->SKU,  'quantity' => $value->quantity, 'inventory_path' => SellerFfcLocation::getInventoryPath($value->ffc_location_id), 'picked' => 1]);
                }

            } else {

                $all_picked = true;
                $result = collect($result);
                
                foreach (FulfillmentOrderItemFFCLocation::whereIn('fulfillment_order_id', $fulfillment_orders->pluck('id'))->where('is_picked',1)->get() as $key => $value) {
                    ($result)->push([  'reference_id' => $fulfillment_orders->where('id', $value->fulfillment_order_id)->first()->reference_id, 'sku' => $value->product->SKU, 'quantity' => $value->quantity, 'inventory_path' => SellerFfcLocation::getInventoryPath($value->ffc_location_id), 'picked' => 1]);
                }
            }

            if ($result->isNotEmpty()) {
                return $this->handleResponse(['data' => $result, 'all_picked' => $all_picked, 'picking_mode' => $picking_mode, 'current_pick_items' => $current_pick_items, 'ItemwiseScanPicking' => $item_wise_scan_at_picking_settings], 'FFC Order item inventory.');

            } else {
                return $this->handleError('No Order Found.','');
            }
            
         
        }catch (\Throwable $e) {
            Log::critical($e->getMessage());
            Log::critical($e->getTraceAsString());
            return $this->handleError('Exception.', ['error'=>$e->getMessage()]);
        }
    }

    public function itemPicked(Request $request)
    {
        $current_item = null;

        try {

            $sub_user = Auth::user();
            $pick_item =  json_decode($request->pick_item);
            $message = "Item Picked";

            foreach ($pick_item as $item) {
                $current_item = $item->item_id;

                if (FulfillmentOrderItem::pickForPicking($item->item_id)) {

                    $required_qty = FulfillmentOrderItem::whereId($item->item_id)->value('quantity') - FulfillmentOrderItemFFCLocation::where('fulfillment_order_item_id', $item->item_id)->where('is_picked',1)->sum('quantity');

                    if (!$required_qty) {
                        $message .= " | Item (".$item->sku.") all quantity is already picked, quantity (".$item->quantity.")";

                    } elseif ($required_qty < $item->quantity) {
                        $message .= " | Item (".$item->sku.") required quantity is less than picked quantity , quantity (".$item->quantity.")";

                    } else {

                        $previous = SellerFfcInventory::whereId($item->inventory_id)->first();

                        if (SellerFfcInventory::whereId($item->inventory_id)->where('qty','>=',$item->quantity)->decrement('qty', $item->quantity)) {                    

                            $fulfillment_order = FulfillmentOrder::whereId($item->fulfillment_order_id)->first(['order_id','reference_id']);

                            hold_data_for_internal_logger(
                                $sub_user->seller_id,
                                'Sub User',
                                $sub_user->id,
                                $sub_user->full_name,
                                'FFC Inventory',
                                'qty',
                                'Fulfillment Order',
                                $fulfillment_order->id,
                                $fulfillment_order->reference_id,
                                'Fulfillment Order Item Picked',
                                'Fulfillment order item has been picked and FFC inventory has been deducted.'
                            );

                            $self = new SellerFfcInventory();
                            $self->createInternalLog($previous, SellerFfcInventory::whereId($item->inventory_id)->first());

                            FulfillmentOrderItemFFCLocation::commitInventory($item->fulfillment_order_id, $item->item_id, $item->product_id, $item->ffc_location_id, $item->quantity, true);

                            $binwise_required_qty = FulfillmentOrderItemFFCLocation::where('fulfillment_order_item_id', $item->item_id)->where('ffc_location_id',$item->ffc_location_id)->where('is_picked',0)->sum('quantity');

                            // \Log::info($binwise_required_qty.">".$item->quantity);
                            if($binwise_required_qty > $item->quantity){
                                $ffoi_ffc_location = FulfillmentOrderItemFFCLocation::where('fulfillment_order_item_id', $item->item_id)->where('ffc_location_id',$item->ffc_location_id)->where('is_picked',0)->first();
                                if($ffoi_ffc_location){
                                    $ffoi_ffc_location->quantity = $ffoi_ffc_location->quantity - $item->quantity;
                                    $ffoi_ffc_location->save();
                                }
                            }
                            // \Log::info($binwise_required_qty."==".$item->quantity);
        
                            if($binwise_required_qty == $item->quantity){
                                $ffoi_ffc_location = FulfillmentOrderItemFFCLocation::where('fulfillment_order_item_id', $item->item_id)->where('ffc_location_id',$item->ffc_location_id)->where('is_picked',0)->delete();
                            }


                            $ffc_location_name = SellerFfcLocation::whereId($item->ffc_location_id)->value('location_name');
                            OrderComments::add($fulfillment_order->order_id, 'FFC Order Process', 'Fulfillment Order (<b>'.$fulfillment_order->reference_id.'</b>) Item (<b>'.$item->sku.' | '.$item->barcode.'</b>) (<b> Quantity : '.$item->quantity.'</b>) has been picked by sub user (<b>'.$sub_user->full_name.'</b>) from FFC bin location (<b>'.$ffc_location_name.'</b>)', 'Success', $sub_user->id);

                            PickAndPutawayLog::add(
                                $sub_user->seller_id,
                                $item->product_id,
                                $item->barcode,
                                'Fulfillment Order',
                                'Picking',
                                'Fulfillment Order',
                                $item->fulfillment_order_id,
                                $sub_user->id,
                                $item->ffc_location_id,
                                $ffc_location_name,
                                $previous->uncommitted_qty,
                                $previous->uncommitted_qty,
                                $previous->qty,
                                $previous->qty - $item->quantity
                            );

                        } else {
                            FulfillmentOrderItem::pickingComplete($item->item_id);
                            return $this->handleError("Item (".$item->sku.") stock not available in FFC inventory, quantity (".$item->quantity.")", "");
                        }
                    }

                } else {
                    return $this->handleError('Picking failed please retry again', "");
                }

                FulfillmentOrderItem::pickingComplete($item->item_id);
            }
            return $this->handleResponse('', $message);

        } catch (Exception $e) {

            FulfillmentOrderItem::pickingComplete($current_item);
            return $this->handleError($e->getMessage(), "");
        }
    }

    public function getFFCItemsInventory(Request $request){
        $sub_user = Auth::user();


        $validator = Validator::make($request->all(), [
            'order_id' => 'required | numeric'                             
        ]);

        if ($validator->fails()) {
            return $this->handleError($validator->errors()->messages()['order_id'][0],'');
        }

        try {
            
            $fulfillment_order = FulfillmentOrder::find($request->order_id);

            if($fulfillment_order && $fulfillment_order->seller_location_id == $sub_user->order_pickup_location) {

                if (!$fulfillment_order->is_picked) {

                    $order_data['marketplace_reference_id'] = $fulfillment_order->order->marketplace_reference_id;
                    $items_temp_count = 0;

                    foreach ($fulfillment_order->items as $item) {

                        $product_id = $item->order_item->product_id;
                        $seller_ffc_inventory = SellerFfcInventory::where('product_id',$product_id)->where('seller_location_id', $fulfillment_order->seller_location_id)->where('seller_id', $fulfillment_order->seller_id)->where('qty','!=', 0);
                        $required_qty = $item->quantity;
    
                        if($seller_ffc_inventory->sum('qty') >= $required_qty) {

                            $t_array = [];
                            $count = 0;
                            
                            foreach($seller_ffc_inventory->orderBy('qty', 'DESC')->get() as $sfi) {
                                    
                                if($required_qty > 0) {
                                        
                                    if($sfi->qty == $required_qty) {

                                        $t_array[$product_id][$count]['seller_ffc_location_id'] = $sfi->seller_ffc_location_id;
                                        $t_array[$product_id][$count]['qty'] = $sfi->qty;    
                                        $required_qty = 0;
                                        break;

                                    } elseif($sfi->qty > $required_qty) {

                                        $t_array[$product_id][$count]['seller_ffc_location_id'] = $sfi->seller_ffc_location_id;
                                        $t_array[$product_id][$count]['qty'] = $required_qty;    
                                        $required_qty = 0;
                                        break;
                                    
                                    } elseif($sfi->qty < $required_qty) {

                                        $t_array[$product_id][$count]['seller_ffc_location_id'] = $sfi->seller_ffc_location_id;
                                        $t_array[$product_id][$count]['qty'] = $sfi->qty;    
                                        $required_qty = $required_qty - $sfi->qty;
                                    }

                                    $count++;
                                }
                            }
            
                            for($i=0;$i<count($t_array[$product_id]);$i++) {

                                $seller_location = SellerLocation::where('id',$fulfillment_order->seller_location_id)->value('location_name');
                                $seller_ffc_location = SellerFfcLocation::where('id',$t_array[$product_id][$i]['seller_ffc_location_id'])->first();

                                if($seller_location && $seller_ffc_location) {
                                    $all_ancestors = $seller_ffc_location->allAncestors();

                                    if(count($all_ancestors) > 0) {
                                        $inventory_path = $all_ancestors->implode('location_name', ' -> ') ." -> ". $seller_ffc_location->location_name;

                                    } else {
                                        $inventory_path = $all_ancestors->implode('location_name', ' -> ') . $seller_ffc_location->location_name;
                                    }

                                    $result['data'][$items_temp_count][] = [
                                        "order_ref_id" => $order_data['marketplace_reference_id'], 
                                        "product_id" => $product_id,
                                        "sku" => $item->order_item->SKU,
                                        "location" => $seller_location." -> ".$inventory_path, 
                                        "available_qty" => $t_array[$product_id][$i]['qty'],
                                        "required_qty" => $item->quantity,
                                        "order_item_id" => $item->order_item->id,
                                    ];

                                    FulfillmentOrderItemFFCLocation::commitInventory($fulfillment_order->id, $item->id, $product_id, $t_array[$product_id][$i]['seller_ffc_location_id'], $t_array[$product_id][$i]['qty']);
                                }  
                            }
                            $items_temp_count = $items_temp_count+1;
                        }            
                    }

                } else {
                    return $this->handleError('Items are already picked','');
                }

                $result['order_data'] = $order_data; 

                if($items_temp_count > 0) {
                    return $this->handleResponse($result, 'FFC Order item inventory.');
                }else{
                    return $this->handleError('No Inventory Found.','');
                }
            }else{
                return $this->handleError('No Order Found.','');
            }
            
         
        }catch (\Throwable $e) {
            return $this->handleError('Exception.', ['error'=>$e->getMessage()]);
        }
    }
    // public function updateOrderTempLocations(Request $request){
    //     $sub_user = Auth::user();
    //     $validator = Validator::make($request->all(), [
    //         'order_item_ids' => 'required',
    //         'status' => 'required'                                                        
    //     ]);

    //     if ($validator->fails()) {
    //         return $this->handleError($validator->errors(),'');
    //     }

    //     try{       
    //         $arr_order_items = explode(",",$request->order_item_ids);
    //         if(count($arr_order_items) > 0){

    //             //in case of Remove
    //             if($request->status == 0){
    //                 $order_id = 0;

    //                 $order_id = FulfillmentOrder::rejectByItems($arr_order_items, $sub_user);

    //                 //This is to cater FLH-1421
    //                 if(AddOn::omniLocationAssignment($sub_user->seller_id)){
    //                     if($order_id != 0){
    //                         $order = Order::where('id', $order_id)->first();
    //                         $order->assignLocationToOrderItems();
    //                     }
    //                 }

    //                 return $this->handleResponse('', 'Order location updated.');
    //             //in case of Next
    //             }elseif($request->status == 1){

    //                 FulfillmentOrder::acceptByItems($arr_order_items, $sub_user);

    //                 return $this->handleResponse('', 'Order Fulfilled.');
    //             }
               
    //         }else{
    //             return $this->handleError('No Order Found.','');
    //         }
         
    //     }catch (\Throwable $e) {
    //         return $this->handleError('Exception.', ['error'=>$e->getMessage()]);
    //     }
    // }
    public function getInventoryAgainstProductBarcode(Request $request){

        $validator = Validator::make($request->all(), [
            'product_barcode' => 'required'
        ]);

        $sub_user = Auth::user();


        if ($validator->fails()) {
            return $this->handleError($validator->errors(),'');
        }

        try{           
                     
            $product = Product::where('barcode', $request->product_barcode)->where('seller_id', $sub_user->seller_id)->first();
            $product_id=0;
            if($product){
                $seller_ffc_inventory = SellerFfcInventory::where('product_id',$product->id)
                                                            ->where('seller_location_id', $sub_user->order_pickup_location)
                                                            ->where('seller_id',  $sub_user->seller_id)
                                                            ->where('qty','!=', 0);

                if($seller_ffc_inventory->sum('qty') > 0){
                        $message = "success";
                        $error = 0;
                        $product_id = $product->id;
                        $result['product_id'] = $product_id;
                        $result['product'] = $product;
                        return $this->handleResponse($result, 'Order temp locations updated.');
                }else{
                    return $this->handleError('No Inventory Found.','',200);
                }
            }else{
                return $this->handleError('No Product Found.','',200);
            }
                     
        }catch (\Throwable $e) {
            return $this->handleError('Exception.', ['error'=>$e->getMessage()]);
        }
    }

    public function getFFCInventoryAgainstProductBarcode(Request $request){

        $validator = Validator::make($request->all(), [
            'product_barcode' => 'required',
            'ffc_location_id' => 'required',
            'quantity' => 'required'
        ]);

        $sub_user = Auth::user();


        if ($validator->fails()) {
            return $this->handleError($validator->errors(),'');
        }

        try{           
                     
            $product = Product::where('barcode', $request->product_barcode)->where('seller_id', $sub_user->seller_id)->first();
            $product_id=0;
            if($product){
                $seller_ffc_inventory = SellerFfcInventory::where('seller_ffc_location_id',$request->ffc_location_id)->where('product_id',$product->id)->first();
                if($seller_ffc_inventory && $seller_ffc_inventory->uncommitted_qty >= $request->quantity){ 
                    $product_id = $product->id;
                    $result['product_id'] = $product_id;
                    $result['product'] = $product;
                    return $this->handleResponse($result, 'Product Details.');                               
                }else{
                    return $this->handleError('No Inventory Found.','',200);
                }
            }else{
                return $this->handleError('No Product Found.','',200);
            }
                     
        }catch (\Throwable $e) {
            return $this->handleError('Exception.', ['error'=>$e->getMessage()]);
        }
    }


    public function changeInventorySync()
    {
        $count = 0;
        $ffc_sellers = AddOn::where('key',config('enum.add_ons')['FFC'])->where('value','1')->pluck('seller_id');
        $data = SellerFfcInventory::whereIn('seller_id', $ffc_sellers)->whereIsUpdated(1)->get()->groupBy(['seller_location_id','product_id']);

        foreach ($data as $location_data) {
            
            foreach ($location_data as $product_data) {
                
                if ($product_data) {
                    
                    Inventory::updateOrCreate(
                        [
                            'product_id' => $product_data[0]->product_id,
                            'seller_location_id' => $product_data[0]->seller_location_id
                        ],
                        [
                            'stock' => $product_data->sum('qty')
                        ]
                    );

                    SellerFfcInventory::whereIn('id', $product_data->pluck('id'))->update(['is_updated' => 0]);
                    $count++;
                }
            }
        }

        return $count.' inventory rows updated or created';
    }

    public function putawaySuggestLocation($product_barcode)
    {
        $error = 1;
        $barcode = null;
        $sub_user = Auth::user();


        $product_id = Product::where('barcode', $product_barcode)->where('seller_id', $sub_user->seller_id)->value('id');

        if ($product_id) {
            $ffc_location_barcode = SellerFfcInventory::where('product_id',$product_id)->orderBy('updated_at', 'desc')->with('ffc_location')->first();

            if ($ffc_location_barcode) {
                
                $error = 0;
                $message = 'location suggested';
                $barcode = $ffc_location_barcode->ffc_location['barcode'];

            } else {
                $message = 'None location available for suggestion';
            }
            
        } else {
            $message = 'Product not found';
        }

        return response()->json( compact('error','message','barcode') );
    }

    public function assignPicklist(Request $request)
    {
        try {

            $sub_user = Auth::user();
            $data_found = false;
            $assign_orders_count = 0;
            $assign_order_items_count = 0;
            $assign_orders_id = null;

            if ($sub_user->order_pickup_location) {

                if (!FulfillmentOrder::subUserOpenPendingPDOrdersExists($sub_user->id)) {

                    $picking_mode = Setting::pickListPickingMode($sub_user->seller_id);

                    if ($picking_mode) {
                        $batch_size = Setting::pickListOrderItemBatchSize($sub_user->seller_id);

                        if (!$batch_size) {
                            return $this->handleError('Pick list order item batch size setting is empty');
                        }
                        
                    } else {
                        $batch_size = Setting::pickListOrderBatchSize($sub_user->seller_id);

                        if (!$batch_size) {
                            return $this->handleError('Pick list order batch size setting is empty');
                        }
        
                    }

                    if (!SellerUserProcessFlag::addFlag('picklist', $sub_user->seller_id, $sub_user->id)) {
                        return $this->handleError('Sub user another picklist creation is already in process');
                    }

                    /// Getting unassigned fulfillment order
                    $fulfillment_orders = FulfillmentOrder::getOrderedLocationOpenUnassignedOrder($sub_user->order_pickup_location, $sub_user->seller_id);

                    foreach ($fulfillment_orders as $fulfillment_order) {

                        if($fulfillment_order && in_array($fulfillment_order->seller_location_id, explode(',', $sub_user->order_pickup_location))) {
                            $pick_items = [];

                            foreach ($fulfillment_order->items as $item) {

                                $required_qty = $item->quantity;

                                if ($required_qty) {

                                    $order_item = $item->order_item;
                                    $product_id = $order_item->product_id;

                                    if(!$product_id) {
                                        $product_id = Product::where('SKU', $item->SKU)->where('seller_id', $sub_user->seller_id)->where('barcode', $item->barcode)->value('id');
                                    }

                                    if ($product_id) {
                                    
                                        $seller_ffc_inventory = SellerFfcInventory::where('product_id',$product_id)
                                            ->where('seller_location_id', $fulfillment_order->seller_location_id)
                                            ->where('uncommitted_qty','!=', 0)
                                            ->whereHas('ffc_location' , function($query) {
                                                $query->where('location_type', config('enum.storage_location_types')['Processing']);
                                            });
                                
                                        if($seller_ffc_inventory->sum('uncommitted_qty') >= $required_qty) {
                                
                                            foreach($seller_ffc_inventory->orderBy('uncommitted_qty', 'DESC')->get() as $sfi) {

                                                $inventory_quantity = $sfi->uncommitted_qty;
                                                $search_keys = array_keys(array_column($pick_items, 'inventory_id'), $sfi->id);

                                                foreach ($search_keys as $key => $value) {
                                                    $inventory_quantity -= $pick_items[$value]['quantity'];
                                                }

                                                if($required_qty > 0) {
                                                        
                                                    if($inventory_quantity == $required_qty) {
                                
                                                        $inventory_path = SellerFfcLocation::getInventoryPath($sfi->seller_ffc_location_id);
                                                        $pick_items[] = ['tote_no' => $fulfillment_order->tote_no, 'reference_id' => $fulfillment_order->reference_id, 'fulfillment_order_id' => $fulfillment_order->id, 'seller_location_id' => $fulfillment_order->seller_location_id, 'inventory_id' => $sfi->id, 'item_id' => $item->id, 'product_id' => $item->order_item->product_id, 'sku' => $item->order_item->product->SKU, 'barcode' => $item->order_item->product->barcode, 'quantity' => $inventory_quantity, 'ffc_location_id' => $sfi->seller_ffc_location_id, 'inventory_path' => $inventory_path, 'distance' => $sfi->ffc_location->sequence];
                                                        $required_qty = 0;
                                                        break;
                                
                                                    } elseif($inventory_quantity > $required_qty) {
                                
                                                        $inventory_path = SellerFfcLocation::getInventoryPath($sfi->seller_ffc_location_id);
                                                        $pick_items[] = ['tote_no' => $fulfillment_order->tote_no, 'reference_id' => $fulfillment_order->reference_id, 'fulfillment_order_id' => $fulfillment_order->id, 'seller_location_id' => $fulfillment_order->seller_location_id, 'inventory_id' => $sfi->id, 'item_id' => $item->id, 'product_id' => $item->order_item->product_id, 'sku' => $item->order_item->product->SKU, 'barcode' => $item->order_item->product->barcode, 'quantity' => $required_qty, 'ffc_location_id' => $sfi->seller_ffc_location_id, 'inventory_path' => $inventory_path, 'distance' => $sfi->ffc_location->sequence];
                                                        $required_qty = 0;
                                                        break;
                                                    
                                                    } elseif($inventory_quantity && $inventory_quantity < $required_qty) {
                                
                                                        $inventory_path = SellerFfcLocation::getInventoryPath($sfi->seller_ffc_location_id);
                                                        $pick_items[] = ['tote_no' => $fulfillment_order->tote_no, 'reference_id' => $fulfillment_order->reference_id, 'fulfillment_order_id' => $fulfillment_order->id, 'seller_location_id' => $fulfillment_order->seller_location_id, 'inventory_id' => $sfi->id, 'item_id' => $item->id, 'product_id' => $item->order_item->product_id, 'sku' => $item->order_item->product->SKU, 'barcode' => $item->order_item->product->barcode, 'quantity' => $inventory_quantity, 'ffc_location_id' => $sfi->seller_ffc_location_id, 'inventory_path' => $inventory_path, 'distance' => $sfi->ffc_location->sequence];
                                                        $required_qty = $required_qty - $inventory_quantity;
                                                    }
                                                }
                                            }

                                        } else {
                                            $pick_items = [];
                                            break;
                                        }
                                    }
                                }
                            }

                            if ($pick_items) {

                                if ($fulfillment_order->assignedUser($sub_user->id)) {

                                    $data_found = true;
                                    $assign_order_items_count += count($pick_items); 
                                    $assign_orders_count ++;
                                    $assign_orders_id .= ' | '.$fulfillment_order->id;

                                    $fulfillment_order->assignedTote($assign_orders_count);

                                    if ($picking_mode) {
                                        $fulfillment_order->assignedPickingMode($picking_mode);
                                    }

                                    hold_data_for_internal_logger(
                                        $sub_user->seller_id,
                                        'Sub User',
                                        $sub_user->id,
                                        $sub_user->full_name,
                                        'FFC Inventory',
                                        'uncommitted_qty',
                                        'Fulfillment Order',
                                        $fulfillment_order->id,
                                        $fulfillment_order->reference_id,
                                        'Sub User Assigned',
                                        'Sub user has been assigned to fulfillment order through automated FFC app process and FFC inventory has been committed.'
                                    );

                                    foreach ($pick_items as $pick_item) {

                                        FulfillmentOrderItemFFCLocation::commitInventory(
                                            $pick_item['fulfillment_order_id'],
                                            $pick_item['item_id'],
                                            $pick_item['product_id'],
                                            $pick_item['ffc_location_id'],
                                            $pick_item['quantity']
                                        );

                                        SellerFfcInventory::deductUncommitted($pick_item['inventory_id'], $pick_item['quantity']);
                                    }
                                }

                                /// Checking Batch Size 
                                if ($picking_mode && $assign_order_items_count >= $batch_size) {
                                    break;
                                } else if ($assign_orders_count >= $batch_size) {
                                    break;
                                }
                            }
                        }
                    }

                    if ($assign_orders_count) {
                        Log::info($assign_orders_count.' orders assign to sub user. ('.$sub_user->full_name.') by automated process'.$assign_orders_id);
                        FulfillmentOrder::validatePicklistCommitInventory(explode('|', str_replace(' ', '', $assign_orders_id)));
                    }

                    SellerUserProcessFlag::removeFlag('picklist', $sub_user->seller_id, $sub_user->id);


                    if($data_found) {
                        return $this->handleResponse($data_found, 'FFC Order assigned by DOM.');
                    } else {
                        return $this->handleError('No Orders Found.');
                    }

                } else {
                    return $this->handleError('Process the already assign orders first');
                }

                

            } else {
                return $this->handleError('You didnt have any location assigned with your account contact your admin');
            }

        } catch (Exception $e) {
            Log::critical($e->getTraceAsString());
            SellerUserProcessFlag::removeFlag('picklist', $sub_user->seller_id, $sub_user->id);
            return $this->handleError('Exception | '.$e->getMessage(), ['error'=>$e->getMessage()]);
        } catch (Error $e) {
            Log::critical($e->getTraceAsString());
            SellerUserProcessFlag::removeFlag('picklist', $sub_user->seller_id, $sub_user->id);
            return $this->handleError('Exception | '.$e->getMessage(), ['error'=>$e->getMessage()]);
        }
    }

    public function releaseAssignedPickList(Request $request)
    {
        $sub_user = Auth::user();
        $count = FulfillmentOrder::where('is_picked',0)->where('sub_user_id',$sub_user->id)->update(['sub_user_id' => Null, 'sub_user_assigned_date' => Null, 'sub_user_assigned_by' => Null]);

        if ($count) {
            return $this->handleResponse(Null, $count.' SKU(s) has been removed from assigned orders pick list');
        } else {
            return $this->handleError('yours today assign orders pick list is already empty');
        }
        
    }

    public function submitToPD(Request $request)
    {
        $sub_user = Auth::user();
        $checkTrolleyExists = FulfillmentOrder::openFulfillmentOrder()
        ->where('seller_id', $sub_user->seller_id)
        ->where('trolley_no',$request->trolley_number)
        ->where('is_packed',0)
        ->exists();

        if ($checkTrolleyExists) {
            return $this->handleError('Trolley no already assigned');
        } else {

            $checkTrolleyExists = StockTransferFulfillmentOrder::openFulfillmentTransferOrder()
            ->where('seller_id', $sub_user->seller_id)
            ->where('trolley_no',$request->trolley_number)
            ->where('is_packed',0)
            ->exists();

            if ($checkTrolleyExists) {
                return $this->handleError('Trolley no already assigned');
            }
        }

        $fulfillment_order = FulfillmentOrder::where('sub_user_id', $sub_user->id)->whereNull('submitted_to_packing_desk')->update(['trolley_no' => $request->trolley_number, 'submitted_to_packing_desk' => 1]);
        
        if( $fulfillment_order ) {
            return $this->handleResponse('', 'List Submitted to Packing Desk.');
        }else{
            return $this->handleError('List was unable to Submit to Packing Desk');
        }
        
        
    }
      public function getPendingPutAwayItems(Request $request){
        $sub_user = Auth::user();
        $location_id =$sub_user->order_pickup_location;
        try{   
            if($location_id){
                $stock_orders = StockOrder::where('seller_id', $sub_user->seller_id)->whereNotIn('status',['Cancelled'])->where('location_id',$location_id)->pluck('id')->implode(',');

                $stock_order_items = StockOrderItem::whereIn('stock_order_id', explode(',',$stock_orders))
                                        ->where('qty_received','>',0)
                                        ->whereColumn('putaway_qty', '<', 'qty_received')
                                        ->join('products','stock_order_items.product_id','products.id')
                                        ->selectRaw('stock_order_items.id,stock_order_items.qty,stock_order_items.qty_received,stock_order_items.putaway_qty,products.sku,stock_order_items.product_id')
                                        ->get();
                $data = [];
                foreach($stock_order_items as $stock_order_item){
                    $product = Product::find($stock_order_item->product_id);
                    if(!isset($data[$product->id])){
                        if($product){
                            $data[$product->id]['qty_received'] = $stock_order_item->qty_received;
                            $data[$product->id]['putaway_qty'] = $stock_order_item->putaway_qty;
                            $data[$product->id]['product_id'] = $stock_order_item->product_id;
                            $data[$product->id]['barcode'] = $product->barcode;
                            $data[$product->id]['product_name'] = (strlen($product->product_name) > 40 ? substr($product->product_name,0,40) . "..." : $product->product_name );
                            $data[$product->id]['sku'] = $product->SKU;
                            $data[$product->id]['pending_putaway'] = $data[$product->id]['qty_received'] - $data[$product->id]['putaway_qty'];
                        }
                    }else{
                        if($product){
                            $data[$product->id]['qty_received'] = $data[$product->id]['qty_received'] +  $stock_order_item->qty_received;
                            $data[$product->id]['putaway_qty'] = $data[$product->id]['putaway_qty'] +  $stock_order_item->putaway_qty;   
                            $data[$product->id]['product_id'] = $stock_order_item->product_id; 
                            $data[$product->id]['barcode'] = $product->barcode;
                            $data[$product->id]['product_name'] =(strlen($product->product_name) > 40 ? substr($product->product_name,0,40) . "..." : $product->product_name );
                            $data[$product->id]['sku'] = $product->SKU;
                            $data[$product->id]['pending_putaway'] = $data[$product->id]['qty_received'] - $data[$product->id]['putaway_qty'];
                        }
                    }
                }
                                       
                 if($stock_order_items->count() > 0) {
                     return $this->handleResponse($data, 'Pending Putaway Items Retrieved.');
             
                 }else{
                     return $this->handleError('Error.', ['error'=>"No Pending Putaway Found"]);
                 }
            }else{
                return $this->handleError('Error.', ['error'=>"Location is not assigned to the user"]);
            }      
        }catch (\Throwable $e) {
            return $this->handleError('Exception.', ['error'=>$e->getMessage()]);
        }
    }

    public function getPutwayPendingItem(Request $request){
        $sub_user = Auth::user();
        $sub_user_location_id =$sub_user->order_pickup_location;

        \Log::info("The request is ");
        \Log::info($request);


        $validator = Validator::make($request->all(), [
            'product_id' => 'required | numeric'                             
        ]);

        if ($validator->fails()) {
            return $this->handleError("Product Id is incorrect or mssing",'');
        }

        try{           
            
            $stock_orders = StockOrder::where('seller_id', $sub_user->seller_id)->where('location_id',$sub_user_location_id)->whereNotIn('status',['Cancelled'])->pluck('id')->implode(',');

            $stock_order_items = StockOrderItem::whereIn('stock_order_id', explode(',',$stock_orders))
            ->where('product_id', $request->product_id)
            ->where('qty_received','>',0)
            ->whereColumn('putaway_qty', '<', 'qty_received')
            ->join('products','stock_order_items.product_id','products.id')
            ->selectRaw('stock_order_items.id,stock_order_items.qty,stock_order_items.qty_received,stock_order_items.putaway_qty,products.sku,stock_order_items.product_id')
            ->get();
            $item_wise_at_putaway_desk_settings = Setting::where('seller_id', $sub_user->seller_id)->where('key',config('enum.settings')['ITEM-WISE-SCANNING-AT-PUTAWAY-DESK']);
            $data['ItemwisePutaway'] = $item_wise_at_putaway_desk_settings->value('value');

            if ($permission = SellerUserPermission::where('seller_user_id',$sub_user->id)->where('ffc_app_item_counting_mode',1)->first(['item_counting_mode']) ) {
                $data['ItemwisePutaway'] = $permission->item_counting_mode;
            }

            $item_detail = [];
            if($stock_order_items->count() > 0) {

                foreach($stock_order_items as $stock_order_item){
                    $product = Product::find($stock_order_item->product_id);
                    if(!isset($item_detail[$stock_order_item->sku])){
                        $item_detail[$stock_order_item->sku]['qty_received'] = $stock_order_item->qty_received;
                        $item_detail[$stock_order_item->sku]['product_id'] = $stock_order_item->product_id;
                        $item_detail[$stock_order_item->sku]['putaway_qty'] = $stock_order_item->putaway_qty;
                        $item_detail[$stock_order_item->sku]['barcode'] = $product->barcode;
                        $item_detail[$stock_order_item->sku]['pending_putaway'] = $item_detail[$stock_order_item->sku]['qty_received'] - $item_detail[$stock_order_item->sku]['putaway_qty'];

                    }else{
                        $item_detail[$stock_order_item->sku]['qty_received'] = $item_detail[$stock_order_item->sku]['qty_received'] +  $stock_order_item->qty_received;
                        $item_detail[$stock_order_item->sku]['product_id'] = $stock_order_item->product_id;
                        $item_detail[$stock_order_item->sku]['putaway_qty'] = $item_detail[$stock_order_item->sku]['putaway_qty'] +  $stock_order_item->putaway_qty;    
                        $item_detail[$stock_order_item->sku]['barcode'] = $product->barcode;
                        $item_detail[$stock_order_item->sku]['pending_putaway'] = $item_detail[$stock_order_item->sku]['qty_received'] - $item_detail[$stock_order_item->sku]['putaway_qty'];

                    }
                }

                $data['item_detail'] = $item_detail;
                $pending_putaway = $item_detail[$stock_order_item->sku]['pending_putaway'];


                $location_where_inventory_already_exist = SellerFfcInventory::where('product_id',$request->product_id)
                ->where('seller_id', $sub_user->seller_id)
                ->where('qty','>', 0)
                ->where('seller_location_id',$sub_user_location_id)
                ->with(['ffc_location'])
                ->whereHas('ffc_location', function ($query) {
                    $query->where('location_type', '!=', config('enum.storage_location_types')['Unsellable']); 
                })                 
                ->get();   
                
                if($location_where_inventory_already_exist->count() > 0){
                    $final_location_where_inventory_already_exist = [];
                    $location_where_capacity_exist = [];
        
                    $capacity_count = 0;
                    $counter = 0;
                    $sequence_of_first_location = 0;
                    $location_ids_where_inventory = [];
                    foreach($location_where_inventory_already_exist as $item){
                        Log::info("I am hereee");
                        $location_current_capacity = $item->ffc_location->getCurrentUtilization();

                        if(isset($location_current_capacity[0]) && $location_current_capacity[0]->capacity != NULL){
                            Log::info("I am hereee 1111".$pending_putaway);

                            if($item->ffc_location->total_capacity > $location_current_capacity[0]->capacity){
                                Log::info("I am hereee 222");

                                $current_capacity = $item->ffc_location->total_capacity - $location_current_capacity[0]->capacity;
                                $item->ffc_location = collect($item->ffc_location);
                                $item->ffc_location->put('available_capacity' , $current_capacity);                            if($counter == 0){
                                    $sequence_of_first_location = $item->ffc_location['sequence'];
                                    $counter++;
                                    Log::info("Sequenc of first location". $sequence_of_first_location );
                                }
                                Log::info("Actual Capacity");
                                Log::info($location_current_capacity);
                                $capacity_count = $capacity_count + $current_capacity;
                                Log::info($item->current_capcity);
                                Log::info($pending_putaway);

                                if($capacity_count < $pending_putaway){
                                    Log::info(" if less then ".$capacity_count);
                                    $location_where_capacity_exist[config('enum.storage_location_types_name')[$item->ffc_location['location_type']]][] =  $item->ffc_location;
                                    $location_ids_where_inventory[] = $item->ffc_location['id'];
                                }elseif($capacity_count == $pending_putaway){
                                    Log::info(" if equal to ".$capacity_count);

                                    $location_where_capacity_exist[config('enum.storage_location_types_name')[$item->ffc_location['location_type']]][] =  $item->ffc_location;
                                    $location_ids_where_inventory[] = $item->ffc_location['id'];
                                    break;
                                }else{
                                    Log::info(" if greater then ".$capacity_count);

                                    $location_where_capacity_exist[config('enum.storage_location_types_name')[$item->ffc_location['location_type']]][] =  $item->ffc_location;
                                    $location_ids_where_inventory[] = $item->ffc_location['id'];
                                    break;
                                }
                            }
                        }
                    }

        
                    if($capacity_count < $pending_putaway){
                        $locations_available = SellerFfcLocation::whereNotIn('id',$location_ids_where_inventory)->where('seller_id', $sub_user->seller_id)->where('total_capacity','>',0)->where('is_leaf',1)->where('location_id',$sub_user_location_id)->where('location_type','!=',config('enum.storage_location_types')['Unsellable'])->orderBy('sequence', 'ASC')->get();
                        $pending_putaway =  $pending_putaway - $capacity_count;
                        $capacity_count =  0;
                        foreach($locations_available as $item){
                            Log::info($item->current_capacity);
                            $location_current_capacity = $item->getCurrentUtilization();
                            Log::info("The new capacity");
                            Log::info($location_current_capacity);

                            if(isset($location_current_capacity[0]) && $location_current_capacity[0]->capacity != NULL){                          
                                if($item->total_capacity > $location_current_capacity[0]->capacity){
                                    $current_capacity = $item->total_capacity - $location_current_capacity[0]->capacity;
                                    $capacity_count = $capacity_count +  $current_capacity;
                                    Log::info("I am here");
                                    $item = collect($item);
                                    $item->put('distance' , abs($sequence_of_first_location - $item['sequence']));
                                    $item->put('available_capacity' , $current_capacity);
                                    Log::info($item);
                                    Log::info(" 1111if less then ".$pending_putaway);

                                    if($capacity_count < $pending_putaway){
                                        Log::info(" 1111if less then ".$capacity_count);

                                        $location_where_capacity_exist[config('enum.storage_location_types_name')[$item['location_type']]][] = $item;
                                    }elseif($capacity_count == $pending_putaway){
                                        Log::info("11111 if equal to ".$capacity_count);

                                        $location_where_capacity_exist[config('enum.storage_location_types_name')[$item['location_type']]][] = $item;
                                        break;
                                    }else{
                                        Log::info(" 1111if greater then ".$capacity_count);

                                        $location_where_capacity_exist[config('enum.storage_location_types_name')[$item['location_type']]][] = $item;
                                        break;
                                    }
                                }
                            }
                        }
                    }

                    if(count( $location_where_capacity_exist) > 0){
                        $location_where_capacity_exist = collect($location_where_capacity_exist);
                        $location_where_capacity_exist = $location_where_capacity_exist->sortBy('distance');
                        // $data['location_where_inventory_already_exist'] = $final_location_where_inventory_already_exist;
                        $data['location_where_capacity_exist'] = collect($location_where_capacity_exist);
                    }else{
                        return $this->handleError('Error.', ['error'=>"Capacity to putaway the item was not found"]);
                    }
                    
                }else{
                        $location_where_capacity_exist = [];
                        $locations_available = SellerFfcLocation::where('seller_id', $sub_user->seller_id)->where('total_capacity','>',0)->where('location_id',$sub_user_location_id)->where('is_leaf',1)->where('location_type','!=',config('enum.storage_location_types')['Unsellable'])->orderBy('sequence', 'ASC')->get();
                        $capacity_count =  0;
                        if($locations_available->count() > 0){
                            foreach($locations_available as $item){
                                $location_current_capacity = $item->getCurrentUtilization();
                
                                Log::info("The new capacity");
                                Log::info($location_current_capacity);
                                if(isset($location_current_capacity[0]) && $location_current_capacity[0]->capacity != NULL){                          
                                    if($item->total_capacity > $location_current_capacity[0]->capacity){   
                                        $current_capacity = $item->total_capacity - $location_current_capacity[0]->capacity; 
                                        $item = collect($item);
                                        $item->put('available_capacity' , $current_capacity);                
                                        $capacity_count = $capacity_count + $current_capacity;
                                        if($capacity_count < $pending_putaway){
                                            $location_where_capacity_exist[config('enum.storage_location_types_name')[$item['location_type']]][] = $item;
                                        }elseif($capacity_count == $pending_putaway){
                                            $location_where_capacity_exist[config('enum.storage_location_types_name')[$item['location_type']]][] = $item;
                                            break;
                                        }else{
                                            $location_where_capacity_exist[config('enum.storage_location_types_name')[$item['location_type']]][] = $item;
                                            break;
                                        }
                                    }
                                }
                            }
                            $location_where_capacity_exist = collect($location_where_capacity_exist);
                            $location_where_capacity_exist = $location_where_capacity_exist->sortBy('sequence');
                            $data['location_where_capacity_exist'] = collect($location_where_capacity_exist);
                        }else{
                            return $this->handleError('Error.', ['error'=>"Capacity to putaway the item was not found"]);
                        }

                    }

                 return $this->handleResponse($data, 'Pending Putaway Item Retrieved.');
            
            }else{
                return $this->handleError('Error.', ['error'=>"Pending Putaway for this Item was not Found"]);
            }
         
        }catch (\Throwable $e) {
            return $this->handleError('Exception.', ['error'=>$e->getMessage()]);
        }
    }

    public function getLocationDetailsFromBarcode(Request $request){

        $validator = Validator::make($request->all(), [
            'location_barcode' => 'required'
        ]);

        $sub_user = Auth::user();
        $sub_user_location_id =$sub_user->order_pickup_location;


        if ($validator->fails()) {
            return $this->handleError($validator->errors(),'');
        }

        try{           
                     
            $seller_ffc_location = SellerFfcLocation::where('barcode', $request->location_barcode)->where('seller_id', $sub_user->seller_id)->where('location_id',$sub_user_location_id)->where('is_leaf', 1)->first();

            if($seller_ffc_location){
                $location_current_capacity = $seller_ffc_location->getCurrentUtilization();
                $current_capacity = $seller_ffc_location->total_capacity - $location_current_capacity[0]->capacity;
              
                $seller_ffc_location = collect($seller_ffc_location);
                $seller_ffc_location->put('available_capacity' , $current_capacity);

                $ffc_inventories = SellerFfcInventory::where('seller_ffc_location_id',$seller_ffc_location['id'])->with('product')->get();

                $bin_ffc_inventories = $ffc_inventories->map(function ($inventory) {
                    if ($inventory->product) {
                        return [
                            'uncommitted_qty' => $inventory->uncommitted_qty,
                            'barcode' => $inventory->product->barcode,
                            'product' => $inventory->product, // Include all product data
                        ];
                    } else {
                        return [
                            'uncommitted_qty' => $inventory->uncommitted_qty,
                            'barcode' => null,
                            'product' => null,
                        ];
                    }
                })->toArray();
                
                return $this->handleResponse(compact('seller_ffc_location', 'bin_ffc_inventories'), 'Location Data Retrieved.');

            }else{
                return $this->handleError('No Location Found.','',200);
            }
                     
        }catch (\Throwable $e) {
            return $this->handleError('Exception.', ['error'=>$e->getMessage()]);
        }
    }
    public function updatePutawayWithScript(Request $request){

        $validator = Validator::make($request->all(), [
            'product_id' => 'required ',
            'location_id' => 'required',           
            'qty' => 'required | numeric' ,
        
        ]);

        if ($validator->fails()) {
            return $this->handleError($validator->errors(),'');
        }
        $seller_id = $request->seller_id;
        $sub_user = SellerUser::whereSellerId($seller_id)->first();
 
        try{       

            $putaway_service = new PutawayService;
            return $putaway_service->execute($sub_user, $request->product_id, $request->location_id, $request->qty);
         
        }catch (\Throwable $e) {
            return $this->handleError('Exception.', ['error'=>$e->getMessage()]);
        }
    }

    public function updatePutawayWithScriptOptimised(Request $request){

        $validator = Validator::make($request->all(), [
            'product_id' => 'required ',
            'location_id' => 'required',           
            'qty' => 'required | numeric' ,
        
        ]);

        if ($validator->fails()) {
            return $this->handleError($validator->errors(),'');
        }
 
        $seller_id = $request->seller_id;
 
        try{       
            Log::info("-------------updatePutawayWithScriptOptimised------------------->>>>>>>>-------Started---------->>>>>---");
            $product = Product::where('id', $request->product_id)->first();
            $seller_ffc_location = SellerFfcLocation::where('id', $request->location_id)->first();
            
            if($product && $seller_ffc_location){

                if($request->qty > 0){ // FLH-1588
                    // Writing direct query to avoid any performance clash
               
                    Log::info($request->qty);
                        $seller_ffc_inventory = SellerFfcInventory::where('seller_ffc_location_id', $seller_ffc_location->id)->where('product_id', $product->id)->where('seller_id', $seller_id)->first();
                        if($seller_ffc_inventory){
                            $seller_ffc_inventory->qty = $seller_ffc_inventory->qty + $request->qty;
                            $seller_ffc_inventory->uncommitted_qty = $seller_ffc_inventory->uncommitted_qty + $request->qty;
                        }else{
                            $seller_ffc_inventory = new SellerFfcInventory;
                            $seller_ffc_inventory->seller_id = $seller_id;
                            $seller_ffc_inventory->seller_location_id =$seller_ffc_location->location_id;
                            $seller_ffc_inventory->seller_ffc_location_id = $seller_ffc_location->id;
                            $seller_ffc_inventory->product_id = $product->id;
                            $seller_ffc_inventory->qty = $request->qty;
                            $seller_ffc_inventory->uncommitted_qty = $request->qty;
                        }

                        $seller_ffc_inventory->save();

                        if ($seller_ffc_location->location_type == config('enum.storage_location_types')['Unsellable']) {
                            Log::critical("Inside if condition".$product->id."---".$seller_ffc_location->location_id);
                            Inventory::updateOnlyStock($product->id, $seller_ffc_location->location_id, $request->qty);
                        } else {
                            Log::critical("Inside else condition".$product->id."---".$seller_ffc_location->location_id);
                            Inventory::updateStock($product->id, $seller_ffc_location->location_id, $request->qty);
                        }
                }

                Log::info("-------------updatePutawayWithScriptOptimised------------------->>>>>>>>-------Completed---------->>>>>---");

                
            }else{
                return $this->handleError('Error.', ['error'=>"Product or Barcode doesnt exist!"]);
            }
            
            return $this->handleResponse('', 'Putaway updated.');
         
        }catch (\Throwable $e) {
            return $this->handleError('Exception.', ['error'=>$e->getMessage()]);
        }
    }

    public function updatePutawayNew(Request $request){

        $validator = Validator::make($request->all(), [
            'product_id' => 'required ',
            'location_id' => 'required',           
            'qty' => 'required | numeric' ,
        
        ]);

        if ($validator->fails()) {
            return $this->handleError($validator->errors(),'');
        }
        $sub_user = Auth::user();

        try{       

            $putaway_service = new PutawayService;
            return $putaway_service->execute($sub_user, $request->product_id, $request->location_id, $request->qty);
         
        }catch (\Throwable $e) {
            return $this->handleError('Exception.', ['error'=>$e->getMessage()]);
        }
    }

    public function getProductDetailsFromBarcode(Request $request){

        $validator = Validator::make($request->all(), [
            'product_barcode' => 'required'
        ]);

        $sub_user = Auth::user();


        if ($validator->fails()) {
            return $this->handleError($validator->errors(),'');
        }

        try{           
            $product = Product::where('barcode', $request->product_barcode)->where('seller_id', $sub_user->seller_id)->first();
            if($product){
                return $this->handleResponse($product, 'Product Data Retrieved.');

            }else{
                return $this->handleError('No Location Found.','',200);
            }
                     
        }catch (\Throwable $e) {
            return $this->handleError('Exception.', ['error'=>$e->getMessage()]);
        }
    }



    ///--- Transfer Order ---\\\


    public function getUnassignedTransferOrder(Request $request){

        $sub_user = Auth::user();

        try {

            if ($sub_user->order_pickup_location) {
                
                $assign_orders = StockTransferFulfillmentOrder::unAssignedOrders($sub_user->seller_id, $sub_user->order_pickup_location, $sub_user->id);

                $count = 1;
                $ids = "";
                foreach( $assign_orders as $ao){
                    $ids = ($count == 1 ? $ao->id  : $ids . "," .$ao->id); 
                    $count ++;
                }

                $data['all_order_ids'] = implode(",",$assign_orders->pluck('id')->toArray());
                $data['orders'] = [];

                foreach ($assign_orders as $pick_list_order) {

                    $destination_location_id = $pick_list_order->destination_location_id ?? StockOrder::whereId($pick_list_order->stock_order_id)->value('location_id');
                    $destination_location = SellerLocation::whereId($destination_location_id)->value('location_name');

                    $data['orders'][] = [
                        "order_ref_id" => $pick_list_order->reference_id , 
                        "is_picked" => $pick_list_order->is_picked , 
                        "order_id" => $pick_list_order->id, 
                        "sub_user_id" => $pick_list_order->sub_user_id, 
                        "items_count" => $pick_list_order->items->count() ,
                        "destination_location" => $destination_location,
                        "quantity" => $pick_list_order->items->sum('quantity') ,
                        "created_at" => ($pick_list_order->created_at)->toDateTimeString()
                    ];

                    if ($pick_list_order->sub_user_id) {
                        break;
                    }
                }
        
                if($data) {               
                    return $this->handleResponse($data, 'FFC stock transfer order.');
                }else {
                    return $this->handleError('No Stock Transfer Orders Found.','');
                }



            } else {
                return $this->handleError('You didnt have any location assigned with your account, contact your admin');
            }
            

            
         
        }catch (\Throwable $e) {
            return $this->handleError('Exception.', ['error'=>$e->getMessage()]);
        }
    }

    public function getDetailForPickingTransferOrder(Request $request){
        $sub_user = Auth::user();
        $all_picked = false;

        $validator = Validator::make($request->all(), [
            'order_id' => 'required | numeric'                             
        ]);

       
        if ($validator->fails()) {
            return $this->handleError($validator->errors()->messages()['order_id'][0],'');
        }

        try {

            $item_wise_scan_at_transfer_order_settings = Setting::where('seller_id', $sub_user->seller_id)->where('key',config('enum.settings')['ITEM-WISE-SCANNING-AT-TRANSFER-ORDER'])->value('value');

            if ($permission = SellerUserPermission::where('seller_user_id',$sub_user->id)->where('ffc_app_item_counting_mode',1)->first(['item_counting_mode']) ) {
                $item_wise_scan_at_transfer_order_settings = $permission->item_counting_mode;
            }
            
            $stock_transfer_fulfillment_order = StockTransferFulfillmentOrder::find($request->order_id);

            if (StockTransferFulfillmentOrder::openFulfillmentTransferOrder()->where('submitted_to_packing_desk',0)->where('sub_user_id', $sub_user->id)->exists() && $stock_transfer_fulfillment_order->sub_user_id != $sub_user->id) {
                return $this->handleError('Process the already assigned Transfer Order.','');
            }

            if($stock_transfer_fulfillment_order && in_array($stock_transfer_fulfillment_order->seller_location_id, explode(',', $sub_user->order_pickup_location) )) {

                if (!$stock_transfer_fulfillment_order->submitted_to_packing_desk) {

                    if (!$stock_transfer_fulfillment_order->sub_user_id || $stock_transfer_fulfillment_order->sub_user_id == $sub_user->id) {

                        /// adding piclist is in process for sub user to prevent duplicate commitment 
                        if (!SellerUserProcessFlag::addFlag('picklist', $sub_user->seller_id, $sub_user->id)) {
                            return $this->handleError('Sub user picklist creation is already in process');
                        }

                        if (!$stock_transfer_fulfillment_order->sub_user_id) {

                            /// Check for uncommitted quantity stock in FFC inventories for automated selection
                            if ($request->automated_selection) {
                                $stock_transfer_fulfillment_order->validateFFCInventories();
                            }

                            $stock_transfer_fulfillment_order->assignedUser($sub_user->id);
                        }
    
                        $order_data['reference_id'] = $stock_transfer_fulfillment_order->reference_id;
                        $order_data['stock_transfer_fulfillment_order_id'] = $stock_transfer_fulfillment_order->id;
                        $order_data['seller_location_id'] = $stock_transfer_fulfillment_order->seller_location_id;
                        $order_data['picklist_created'] = true;
                        $ffc_inventory_deduct_entries = [];
                        $picked_items_list = [];

                        foreach ($stock_transfer_fulfillment_order->items as $key => $item) {

                            if (isset($item->product->SKU)) {

                                $required_quantity = $item->quantity - StockTransferCommittedInventory::where('stock_transfer_fulfillment_order_item_id', $item->id)->sum('quantity');

                                /// For Automated Location Selection
                                if ($request->automated_selection) {

                                    if ($required_quantity) {
                                        $stock_type = $item->stock_type;
                                        if ($request->automated_selection == 2) { // FLH-1880
                                            $seller_ffc_inventory = SellerFfcInventory::where('product_id',$item->product_id)->where('seller_location_id', $stock_transfer_fulfillment_order->seller_location_id)->where('uncommitted_qty','!=', 0)->whereHas('ffc_location' , function($query) {
                                               $query->where('location_type', config('enum.storage_location_types')['Reserve']);
                                            });

                                            if($required_quantity > $seller_ffc_inventory->sum('uncommitted_qty')) {
                                                $seller_ffc_inventory = SellerFfcInventory::where('product_id',$item->product_id)->where('seller_location_id', $stock_transfer_fulfillment_order->seller_location_id)->where('uncommitted_qty','!=', 0)->whereHas('ffc_location' , function($query) use ($stock_type) {
                                                    if ($stock_type == 1) {
                                                        $query->where('location_type', config('enum.storage_location_types')['Unsellable']);
                                                    } else {
                                                        $query->where('location_type', '!=', config('enum.storage_location_types')['Unsellable']);
                                                    }
                                                });
                                            }
                                        }else{
                                            $seller_ffc_inventory = SellerFfcInventory::where('product_id',$item->product_id)->where('seller_location_id', $stock_transfer_fulfillment_order->seller_location_id)->where('uncommitted_qty','!=', 0)->whereHas('ffc_location' , function($query) use ($stock_type) {
                                                if ($stock_type == 1) {
                                                    $query->where('location_type', config('enum.storage_location_types')['Unsellable']);
                                                } else {
                                                    $query->where('location_type', '!=', config('enum.storage_location_types')['Unsellable']);
                                                }
                                            });
                                        }

                                        if($seller_ffc_inventory->sum('uncommitted_qty') >= $required_quantity) {

                                            foreach($seller_ffc_inventory->orderBy('uncommitted_qty', 'DESC')->get() as $sfi) {

                                                if($required_quantity > 0) {

                                                    if($sfi->uncommitted_qty == $required_quantity) {

                                                        $inventory_path = SellerFfcLocation::getInventoryPath($sfi->seller_ffc_location_id);
                                                        $ffc_inventory_deduct_entries[] = ['inventory_id' => $sfi->id, 'item_id' => $item->id, 'product_id' => $item->product_id, 'sku' => $item->product->SKU, 'barcode' => $item->product->barcode, 'quantity' => $sfi->uncommitted_qty, 'ffc_location_id' => $sfi->seller_ffc_location_id, 'inventory_path' => $inventory_path, 'distance' => $sfi->ffc_location->sequence];
                                                        $required_quantity = 0;
                                                        break;

                                                    } elseif($sfi->uncommitted_qty > $required_quantity) {

                                                        $inventory_path = SellerFfcLocation::getInventoryPath($sfi->seller_ffc_location_id);
                                                        $ffc_inventory_deduct_entries[] = ['inventory_id' => $sfi->id, 'item_id' => $item->id, 'product_id' => $item->product_id, 'sku' => $item->product->SKU, 'barcode' => $item->product->barcode, 'quantity' => $required_quantity, 'ffc_location_id' => $sfi->seller_ffc_location_id, 'inventory_path' => $inventory_path, 'distance' => $sfi->ffc_location->sequence];
                                                        $required_quantity = 0;
                                                        break;
                                                    
                                                    } elseif($sfi->uncommitted_qty < $required_quantity) {

                                                        $inventory_path = SellerFfcLocation::getInventoryPath($sfi->seller_ffc_location_id);
                                                        $ffc_inventory_deduct_entries[] = ['inventory_id' => $sfi->id, 'item_id' => $item->id, 'product_id' => $item->product_id, 'sku' => $item->product->SKU, 'barcode' => $item->product->barcode, 'quantity' => $sfi->uncommitted_qty, 'ffc_location_id' => $sfi->seller_ffc_location_id, 'inventory_path' => $inventory_path, 'distance' => $sfi->ffc_location->sequence];
                                                        $required_quantity = $required_quantity - $sfi->uncommitted_qty;
                                                    }
                                                }
                                            }
                                        }
                                    }

                                    if ($required_quantity) {
                                        $this->handleError("Product (".$item->product->product_name.") stock not available in FFC inventory", "");
                                    }

                                /// For Manual Location Selection
                                } else {

                                    if ($required_quantity) {

                                        $order_data['picklist_created'] = false;

                                        $ffc_inventory_deduct_entries[] = [
                                            'inventory_id' => 1,
                                            'item_id' => $item->id,
                                            'product_id' => $item->product_id,
                                            'sku' => $item->product->SKU,
                                            'barcode' => $item->product->barcode,
                                            'product_name' => $item->product->product_name,
                                            'quantity' => $required_quantity,
                                            'ffc_location_id' => 1,
                                            'inventory_path' => Null,
                                            'distance' => 0
                                        ];
                                    }
                                }

                            } else {

                                SellerUserProcessFlag::removeFlag('picklist', $sub_user->seller_id, $sub_user->id);
                                return $this->handleError('Item Product not found in unity','');
                            }
                        }

                    } else {
                        SellerUserProcessFlag::removeFlag('picklist', $sub_user->seller_id, $sub_user->id);
                        return $this->handleError('Another user is already picking','');
                    }

                } else {
                    SellerUserProcessFlag::removeFlag('picklist', $sub_user->seller_id, $sub_user->id);
                    return $this->handleError('Items are already submitted to Packing Desk','');
                }

                $result['order_data'] = $order_data; 
                
                if($ffc_inventory_deduct_entries) {

                    if ($order_data['picklist_created']) {

                        $result['data'] = collect();

                        hold_data_for_internal_logger(
                            $sub_user->seller_id,
                            'Sub User',
                            $sub_user->id,
                            $sub_user->full_name,
                            'FFC Inventory',
                            'uncommitted_qty',
                            'Stock Transfer Order',
                            $stock_transfer_fulfillment_order->id,
                            $stock_transfer_fulfillment_order->reference_id,
                            'Sub User Assigned',
                            'Sub user has been assigned to stock transfer order through FFC app process and FFC inventory has been committed.'
                        );

                        foreach ($ffc_inventory_deduct_entries as $key => $item_ffc_inventory) {
                            $item_ffc_inventory = (object) $item_ffc_inventory;

                            if (SellerFfcInventory::whereId($item_ffc_inventory->inventory_id)->where('uncommitted_qty','>=',$item_ffc_inventory->quantity)->exists()) {

                                SellerFfcInventory::deductUncommitted($item_ffc_inventory->inventory_id, $item_ffc_inventory->quantity);

                                $committed_inventory = new StockTransferCommittedInventory;
                                $committed_inventory->stock_transfer_fulfillment_order_id = $stock_transfer_fulfillment_order->id;
                                $committed_inventory->stock_transfer_fulfillment_order_item_id = $item_ffc_inventory->item_id;
                                $committed_inventory->inventory_id = $item_ffc_inventory->inventory_id;
                                $committed_inventory->quantity = $item_ffc_inventory->quantity;
                                $committed_inventory->ffc_location_id = $item_ffc_inventory->ffc_location_id;
                                $committed_inventory->product_id = $item_ffc_inventory->product_id;
                                $committed_inventory->inventory_path = $item_ffc_inventory->inventory_path;
                                $committed_inventory->save();
                            }
                        }

                    } else {
                        $result['data'] = collect($ffc_inventory_deduct_entries)->sortByDesc('distance');
                    }

                } else {
                    $result['data'] = collect();
                }
                
                foreach (StockTransferCommittedInventory::where('stock_transfer_fulfillment_order_id', $stock_transfer_fulfillment_order->id)->get() as $key => $value) {

                    if (!$key) {
                        $all_picked = true;
                    }

                    ($result['data'])->push([
                        'inventory_id' => $value->inventory_id,
                        'item_id' => $value->stock_transfer_fulfillment_order_item_id,
                        'product_id' => $value->product_id,
                        'sku' => $value->product->SKU,
                        'barcode' => $value->product->barcode,
                        'product_name' => $value->product->product_name,
                        'quantity' => $value->quantity,
                        'inventory_path' => $value->inventory_path,
                        'ffc_location_id' => $value->ffc_location_id,
                        'picked' => $value->is_picked,
                        'distance' => $value->ffc_location->sequence,
                        'location_type' => $value->ffc_location->location_type,

                    ]);

                    if (!$value->is_picked) {
                        $all_picked = false;
                    }
                }

                $result['item_wise_scan_at_transfer_order'] = $item_wise_scan_at_transfer_order_settings;


                if($all_picked) {

                    StockTransferFulfillmentOrder::picked($stock_transfer_fulfillment_order->id);
                    SellerUserProcessFlag::removeFlag('picklist', $sub_user->seller_id, $sub_user->id);
                    return $this->handleResponse($result, 'picked');

                } elseif ($result['data']->isNotEmpty()) {
                    if ($request->automated_selection == 2) { // FLH-1880
                        $result['data'] = $result['data']->sortBy([
                            ['picked', 'asc'],
                            ['location_type', 'desc'],
                            ['distance', 'asc'],
                            ['sku', 'asc'],
                        ])->values()->all();
                    }else{
                        $result['data'] = $result['data']->sortBy([
                            ['picked', 'asc'],
                            ['distance', 'asc'],
                            ['sku', 'asc'],
                            ['ffc_location_id', 'asc'],
                        ])->values()->all();
                    }
                   
                    SellerUserProcessFlag::removeFlag('picklist', $sub_user->seller_id, $sub_user->id);
                    return $this->handleResponse($result, 'FFC Order item inventory.');

                } else {
                    SellerUserProcessFlag::removeFlag('picklist', $sub_user->seller_id, $sub_user->id);
                    return $this->handleError('No Inventory Found.','');
                }

            } else {
                SellerUserProcessFlag::removeFlag('picklist', $sub_user->seller_id, $sub_user->id);
                return $this->handleError('No Order Found.','');
            }
            
         
        }catch (\Throwable $e) {
            SellerUserProcessFlag::removeFlag('picklist', $sub_user->seller_id, $sub_user->id);
            return $this->handleError('Exception | '.$e->getMessage(), ['error'=>$e->getMessage()]);
        }
    }

    public function getDetailForPickingTransferOrderItem(Request $request){

        $validator = Validator::make($request->all(), [
            'item_id' => 'required'                             
        ]);

        if ($validator->fails()) {
            return $this->handleError($validator->errors()->messages(),'');
        }

        try {
            
            $stock_transfer_fulfillment_order_item = StockTransferFulfillmentOrderItem::find($request->item_id);

            if($stock_transfer_fulfillment_order_item) {

                $result['item_data'] = [
                    'sku' => $stock_transfer_fulfillment_order_item->product->SKU,
                    'barcode' => $stock_transfer_fulfillment_order_item->product->barcode,
                    'quantity' => $stock_transfer_fulfillment_order_item->quantity,
                    'product_name' => $stock_transfer_fulfillment_order_item->product->product_name
                ];

                $item_ffc_inventories = [];
                $required_qty = $stock_transfer_fulfillment_order_item->quantity - StockTransferCommittedInventory::where('stock_transfer_fulfillment_order_item_id', $stock_transfer_fulfillment_order_item->id)->sum('quantity');

                if (!$required_qty) {
                    $this->handleError("Product (".$stock_transfer_fulfillment_order_item->product->product_name.") location is already assigned", "");

                } else {

                    $stock_type = $stock_transfer_fulfillment_order_item->stock_type;
                    $seller_ffc_inventory = SellerFfcInventory::where('product_id',$stock_transfer_fulfillment_order_item->product_id)->where('seller_location_id', $stock_transfer_fulfillment_order_item->fulfillment_order->seller_location_id)->where('qty','!=', 0)->whereHas('ffc_location' , function($query) use ($stock_type) {
                        if ($stock_type == 1) {
                            $query->where('location_type', config('enum.storage_location_types')['Unsellable']);
                        } else {
                            $query->where('location_type', '!=', config('enum.storage_location_types')['Unsellable']);
                        }
                    });

                    if($seller_ffc_inventory->sum('qty') >= $required_qty) {
            
                        foreach($seller_ffc_inventory->orderBy('qty', 'DESC')->get() as $sfi) {

                            $inventory_path = SellerFfcLocation::getInventoryPath($sfi->seller_ffc_location_id);
                            $item_ffc_inventories[] = [
                                'inventory_id' => $sfi->id,
                                'item_id' => $stock_transfer_fulfillment_order_item->id,
                                'product_id' => $stock_transfer_fulfillment_order_item->product_id,
                                'sku' => $stock_transfer_fulfillment_order_item->product->SKU,
                                'barcode' => $stock_transfer_fulfillment_order_item->product->barcode,
                                'item_quantity' => $stock_transfer_fulfillment_order_item->quantity,
                                'quantity' => $sfi->qty,
                                'ffc_location_id' => $sfi->seller_ffc_location_id,
                                'inventory_path' => $inventory_path,
                                'location_type' => $sfi->ffc_location->location_type
                            ];
                        }

                    } else {
                        $this->handleError("Product (".$stock_transfer_fulfillment_order_item->product->product_name.") stock not available in FFC inventory", "");
                    }
                }
                
                if($item_ffc_inventories) {

                    $result['data'] = collect($item_ffc_inventories)->sortBy('location_type');
                    return $this->handleResponse($result, 'FFC Order item inventory.');

                } else {
                    return $this->handleError('No Inventory Found.','');
                }

            } else {
                return $this->handleError('No Stock transfer Order item Found.','');
            }
            
         
        }catch (\Throwable $e) {
            return $this->handleError('Exception.', ['error'=>$e->getMessage()]);
        }
    }

    public function assignFfcLocationToTransferOrderItem(Request $request){

        $sub_user = Auth::user();
        
        $validator = Validator::make($request->all(), [
            'data' => 'required'                             
        ]);

        if ($validator->fails()) {
            return $this->handleError($validator->errors()->messages(),'');
        }

        try {

            $item_ffc_inventories = json_decode($request->data);
            $stock_transfer_fulfillment_order_item = StockTransferFulfillmentOrderItem::find($item_ffc_inventories[0]->item_id);

            if($stock_transfer_fulfillment_order_item) {

                hold_data_for_internal_logger(
                    $sub_user->seller_id,
                    'Sub User',
                    $sub_user->id,
                    $sub_user->full_name,
                    'FFC Inventory',
                    'uncommitted_qty',
                    'Stock Transfer Order',
                    $stock_transfer_fulfillment_order_item->stock_transfer_fulfillment_order_id,
                    StockTransferFulfillmentOrder::whereId($stock_transfer_fulfillment_order_item->stock_transfer_fulfillment_order_id)->value('reference_id'),
                    'FFC Location Assigned',
                    'Manually assigning FFC bin location to stock transfer order through FFC app process and FFC inventory has been committed.'
                );

                foreach ($item_ffc_inventories as $key => $item_ffc_inventory) {
                    $required_qty = $stock_transfer_fulfillment_order_item->quantity - StockTransferCommittedInventory::where('stock_transfer_fulfillment_order_item_id', $item_ffc_inventory->item_id)->sum('quantity');

                    if ($required_qty) {

                        if (SellerFfcInventory::whereId($item_ffc_inventory->ffc_inventory_id)->where('uncommitted_qty','>=',$item_ffc_inventory->quantity)->exists()) {

                            SellerFfcInventory::deductUncommitted($item_ffc_inventory->ffc_inventory_id, $item_ffc_inventory->quantity);
    
                            $committed_inventory = new StockTransferCommittedInventory;
                            $committed_inventory->stock_transfer_fulfillment_order_id = $stock_transfer_fulfillment_order_item->stock_transfer_fulfillment_order_id;
                            $committed_inventory->stock_transfer_fulfillment_order_item_id = $item_ffc_inventory->item_id;
                            $committed_inventory->inventory_id = $item_ffc_inventory->ffc_inventory_id;
                            $committed_inventory->quantity = $item_ffc_inventory->quantity;
                            $committed_inventory->ffc_location_id = $item_ffc_inventory->ffc_location_id;
                            $committed_inventory->product_id = $item_ffc_inventory->product_id;
                            $committed_inventory->inventory_path = $item_ffc_inventory->inventory_path;
                            $committed_inventory->save();
                        }
                    }

                }

                return $this->handleResponse(['stock_transfer_fulfillment_order_id' => $stock_transfer_fulfillment_order_item->stock_transfer_fulfillment_order_id], 'success.');

            } else {
                return $this->handleError('No Stock transfer Order item Found.','');
            }
            
         
        }catch (\Throwable $e) {
            return $this->handleError('Exception.', ['error'=>$e->getMessage()]);
        }
    }


    public function pickedTransferOrder(Request $request){
        
        $sub_user = Auth::user();
        $validator = Validator::make($request->all(), [
            'id' => 'required',
            'status' => 'required'                                                        
        ]);

        if ($validator->fails()) {
            return $this->handleError($validator->errors(),'');
        }

        try{       
            if($request->id){

                //in case of Remove
                if($request->status == 0){
                    // $order_id = 0;

                    // $order_id = FulfillmentOrder::rejectByItems($arr_order_items, $sub_user);

                    // //This is to cater FLH-1421
                    // if($order_id != 0){
                    //     $order = Order::where('id', $order_id)->first();
                    //     $order->assignLocationToOrderItems();
                    // }

                    return $this->handleResponse('', 'Order location updated.');
                //in case of Next
                }elseif($request->status == 1){

                    hold_data_for_internal_logger(
                        $sub_user->seller_id,
                        'Sub User',
                        $sub_user->id,
                        $sub_user->full_name,
                        'FFC Inventory',
                        'uncommitted_qty',
                        'Stock Transfer Order',
                        $request->id,
                        StockTransferFulfillmentOrder::whereId($request->id)->value('reference_id'),
                        'Stock Transfer Order Picked',
                        'Stock Transfer Order has been picked and FFC inventory has been deducted.'
                    );

                    StockTransferFulfillmentOrder::picked($request->id);
                    StockTransferFulfillmentOrder::deductFFCInventory($request->id);

                    return $this->handleResponse('', 'Transfer Order Picked.');
                }
               
            }else{
                return $this->handleError('No Transfer Order Found.','');
            }
         
        }catch (\Throwable $e) {
            return $this->handleError('Exception.', ['error'=>$e->getMessage()]);
        }
    }

    public function submitToPDTransferOrder(Request $request)
    {
        $sub_user = Auth::user();

        $checkTrolleyExists = FulfillmentOrder::openFulfillmentOrder()
        ->where('seller_id', $sub_user->seller_id)
        ->where('trolley_no',$request->trolley_number)
        ->where('is_packed',0)
        ->exists();

        if ($checkTrolleyExists) {
            return $this->handleError('Trolley no already assigned');
        } else {

            $checkTrolleyExists = StockTransferFulfillmentOrder::openFulfillmentTransferOrder()
            ->where('seller_id', $sub_user->seller_id)
            ->where('trolley_no',$request->trolley_number)
            ->where('is_packed',0)
            ->exists();
    
            if ($checkTrolleyExists) {
                return $this->handleError('Trolley no already assigned');
            }
        }


        StockTransferFulfillmentOrder::where('id', $request->tso_id )->where('is_picked',1)->where('is_packed',0)->where('submitted_to_packing_desk',0)->update(['submitted_to_packing_desk' => 1, 'trolley_no' => $request->trolley_number]);
        return $this->handleResponse('', 'List Submitted to Packing Desk.');
    }

    public function itemPickedTransferOrder(Request $request)
    {
        try {
            $sub_user = Auth::user();
            $pick_item =  json_decode($request->pick_item);

            Log::info("STO PIcking || ".json_encode($pick_item));

            $stock_transfer_order_id = StockTransferFulfillmentOrderItem::whereId($pick_item->item_id)->value('stock_transfer_fulfillment_order_id');

            hold_data_for_internal_logger(
                $sub_user->seller_id,
                'Sub User',
                $sub_user->id,
                $sub_user->full_name,
                'FFC Inventory',
                'qty',
                'Stock Transfer Order',
                $stock_transfer_order_id,
                StockTransferFulfillmentOrder::whereId($stock_transfer_order_id)->value('reference_id'),
                'Stock Transfer Order Item Picked',
                'Stock Transfer Order item has been picked and FFC inventory has been deducted.'
            );

            $previous = SellerFfcInventory::whereId($pick_item->inventory_id)->first();
            
            if (SellerFfcInventory::whereId($pick_item->inventory_id)->where('qty','>=',$request->quantity)->decrement('qty', $request->quantity)) {

                $self = new SellerFfcInventory();
                $self->createInternalLog($previous, SellerFfcInventory::whereId($pick_item->inventory_id)->first());

                if ( StockTransferCommittedInventory::where('inventory_id', $pick_item->inventory_id)
                    ->where('quantity', $request->quantity)
                    ->where('stock_transfer_fulfillment_order_item_id', $pick_item->item_id)
                    ->where('is_picked',0)
                    ->update(['is_picked' => 1])) {

                    PickAndPutawayLog::add(
                        $sub_user->seller_id,
                        $pick_item->product_id,
                        $pick_item->barcode,
                        'Stock Transfer Fulfillment Order',
                        'Picking',
                        'Stock Transfer Fulfillment Order',
                        StockTransferFulfillmentOrderItem::whereId($pick_item->item_id)->value('stock_transfer_fulfillment_order_id'),
                        $sub_user->id,
                        $previous->seller_ffc_location_id,
                        SellerFfcLocation::whereId($previous->seller_ffc_location_id)->value('location_name'),
                        $previous->uncommitted_qty,
                        $previous->uncommitted_qty,
                        $previous->qty,
                        $previous->qty - $request->quantity
                    );

                    return $this->handleResponse('', 'Item Picked.');

                } else {

                    $previous = SellerFfcInventory::whereId($pick_item->inventory_id)->first();

                    SellerFfcInventory::whereId($pick_item->inventory_id)->increment('qty', $request->quantity);

                    $self = new SellerFfcInventory();
                    $self->createInternalLog($previous, SellerFfcInventory::whereId($pick_item->inventory_id)->first());

                    return $this->handleError("Item is already picked", "");
                }

            } else {
                return $this->handleError("Item stock not available in FFC inventory", "");
            }
        } catch (\Throwable $e) {
            Log::critical("STO PIcking Exception || ".$e->getMessage(), $e->getTrace());
            return $this->handleError('Exception.', ['error'=>$e->getMessage()]);
        }
    }

    public function createStockMovementOld(Request $request){

        $validator = Validator::make($request->all(), [
            'product_id' => 'required',
            'location_id' => 'required',
            'quantity' => 'required|numeric|min:1',

        ]);

        $sub_user = Auth::user();


        if ($validator->fails()) {
            return $this->handleError($validator->errors(),'',200);
        }

        try{           
            $seller_ffc_inventory = SellerFfcInventory::where('seller_ffc_location_id',$request->location_id)->where('product_id',$request->product_id)->first();
            
            if($seller_ffc_inventory && $seller_ffc_inventory->uncommitted_qty >= $request->quantity){ // fixing the condition as encountered a case where location was assigned but stock was moved from that location to damag

                hold_data_for_internal_logger(
                    $sub_user->id,
                    $sub_user->seller_id,
                    $seller_ffc_inventory->id,
                    'FFC Inventory',
                    'qty',
                    'Inventory Item'
                );

                SellerFfcInventory::deduct($seller_ffc_inventory->id,$request->quantity);
                SellerFfcInventory::deductUncommitted($seller_ffc_inventory->id,$request->quantity);
                $seller_stock_movement = new SellerStockMovement;
                $seller_stock_movement->seller_id = $sub_user->seller_id;
                $seller_stock_movement->seller_sub_user_id = $sub_user->id;
                $seller_stock_movement->seller_ffc_location_id = $request->location_id;
                $seller_stock_movement->seller_product_id = $request->product_id;
                $seller_stock_movement->qty_picked = $request->quantity;
                $seller_stock_movement->save();

                return $this->handleResponse('', 'Stock movement created succesfully.');
            }else{
                return $this->handleError('Inventory doesnt exist!.', $request->all(),200);
            }
                     
        }catch (\Throwable $e) {
            return $this->handleError('Exception.', ['error'=>$e->getMessage()]);
        }
    }

    public function createStockMovement(Request $request){

        $validator = Validator::make($request->all(), [
            'products' => ['required', 'array'],
            'location_id' => 'required'
        ]);

        $sub_user = Auth::user();
        $failed['failed_check'] = [];
        if ($validator->fails()) {
            return $this->handleError($validator->errors(),'',200);
        }

        try{        
            if(count($request->products)){

                hold_data_for_internal_logger(
                    $sub_user->seller_id,
                    'Sub User',
                    $sub_user->id,
                    $sub_user->full_name,
                    'FFC Inventory',
                    'qty',
                    'Stock Movement',
                    Null,
                    Null,
                    'Stock Movement Created',
                    'Stock movement created and FFC inventory has been committed and deducted.'
                );

                foreach($request->products as $product){
                    $product = (object) $product;
                    $req['product_barcode'] = $product->barcode;
                    $req['ffc_location_id'] = $request->location_id;
                    $req['quantity'] = $product->quantity;
                    $stillValid = $this->getFFCInventoryAgainstProductBarcode(new Request($req))->getData()->success;
                    if($stillValid){
                            $seller_ffc_inventory = SellerFfcInventory::where('seller_ffc_location_id',$request->location_id)->where('product_id',$product->id)->first();
            
                            SellerFfcInventory::deduct($seller_ffc_inventory->id,$product->quantity);
                            SellerFfcInventory::deductUncommitted($seller_ffc_inventory->id,$product->quantity);

                            $seller_stock_movement = SellerStockMovement::where('seller_sub_user_id',$sub_user->id)
                                                        ->where('seller_product_id',$product->id)
                                                        ->where('seller_ffc_location_id',$request->location_id)
                                                        ->where('status',0)->first();
                            if($seller_stock_movement){
                                $seller_stock_movement->qty_picked = $seller_stock_movement->qty_picked + $product->quantity;
                            }else{
                                $seller_stock_movement = new SellerStockMovement;
                                $seller_stock_movement->seller_id = $sub_user->seller_id;
                                $seller_stock_movement->seller_sub_user_id = $sub_user->id;
                                $seller_stock_movement->seller_ffc_location_id = $request->location_id;
                                $seller_stock_movement->seller_product_id = $product->id;
                                $seller_stock_movement->qty_picked = $product->quantity;
                            }
                            $seller_stock_movement->save();

                            PickAndPutawayLog::add(
                                $sub_user->seller_id,
                                $product->id,
                                $product->barcode,
                                'Stock Movement',
                                'Picking',
                                'Stock Movement',
                                $seller_stock_movement->id,
                                $sub_user->id,
                                $request->location_id,
                                SellerFfcLocation::whereId($request->location_id)->value('location_name'),
                                $seller_ffc_inventory->uncommitted_qty,
                                $seller_ffc_inventory->uncommitted_qty - $product->quantity,
                                $seller_ffc_inventory->qty,
                                $seller_ffc_inventory->qty - $product->quantity
                            );
            
                        }
                        else {
                            $failed['failed_check'] [] = $product->barcode;
                        }
                }
                        return $this->handleResponse($failed, 'Stock movement created succesfully.');
                        

            }else{
                return $this->handleError('None products were picked!.', $request->all(),200);
            }
            
            
                     
        }catch (\Throwable $e) {
            return $this->handleError('Exception.', ['error'=>$e->getMessage()]);
        }
    }

    public function checkIfProductHasStockMovement(Request $request){

        $validator = Validator::make($request->all(), [
            'product_barcode' => 'required',
            'ffc_location_id' => 'required',
            'quantity' => 'required',

        ]);

        $sub_user = Auth::user();


        if ($validator->fails()) {
            return $this->handleError($validator->errors(),'',200);
        }

        try{        
            $product = Product::where('barcode', $request->product_barcode)->where('seller_id', $sub_user->seller_id)->first();

            if(!$product)
            {
                return $this->handleError('Invalid Barcode','',200);
            }

            $seller_stock_movement = SellerStockMovement::where('seller_sub_user_id',$sub_user->id)
                                        ->where('seller_product_id',$product->id)
                                        ->where('status',0)->get();
            $qty_picked = 0;
            $qty_putaway = 0;
            if(count($seller_stock_movement) > 0){

                foreach($seller_stock_movement as $sm){
                    
                    $qty_picked = $qty_picked + $sm->qty_picked;
                    $qty_putaway = $qty_putaway + $sm->qty_putaway;
                    
                }

                if( ($qty_picked - $qty_putaway) < $request->quantity){
                    return $this->handleError('Putaway quantity cannot exceed picked quantity','',200);
                }

                $result['product_id'] = $product->id;
                $result['barcode'] = $product->barcode;
                $result['sku'] = $product->SKU;
                $result['qty_picked'] = $qty_picked;
                $result['qty_putaway'] = $qty_putaway;
                return $this->handleResponse($result, 'Product Details.');                               

            }else{
                return $this->handleError('No Stockmovement Found!','',200);
            }
        
            return $this->handleResponse('', 'Stock movement created succesfully.');
            
                     
        }catch (\Throwable $e) {
            return $this->handleError('Exception.', ['error'=>$e->getMessage()]);
        }
    }

    public function checkForStockMovement(){
        $sub_user = Auth::user();
        try{    

            $data['item_wise_scan_at_stock_movement'] = Setting::where('seller_id', $sub_user->seller_id)->where('key',config('enum.settings')['ITEM-WISE-SCANNING-AT-STOCK-MOVEMENT'])->value('value');

            if ($permission = SellerUserPermission::where('seller_user_id',$sub_user->id)->where('ffc_app_item_counting_mode',1)->first(['item_counting_mode']) ) {
                $data['item_wise_scan_at_stock_movement'] = $permission->item_counting_mode;
            }

            $seller_stock_movement = SellerStockMovement::where('seller_sub_user_id',$sub_user->id)->where('status',0)->get();
            $item_arr = [];
            if(count($seller_stock_movement) > 0){
                foreach($seller_stock_movement as $stm){
                    $product = Product::find($stm->seller_product_id);
                    $seller_ffc_location = SellerFfcLocation::where('id', $stm->seller_ffc_location_id)->first();
                    $temp_data = [];
                    $temp_data['product'] = $product;
                    $temp_data['qty_picked'] = $stm->qty_picked;
                    $temp_data['qty_putaway'] = $stm->qty_putaway;
                    $temp_data['location_name'] = $seller_ffc_location->location_name;
                    $item_arr[] = $temp_data;
                    $data['products'] = $item_arr;
                }
                return $this->handleResponse($data, 'Stock movement exist.');
            }else{
                return $this->handleError('Stock movement doesnt exist!.',$data,200);
            }
                     
        }catch (\Throwable $e) {
            return $this->handleError('Exception.', ['error'=>$e->getMessage()]);
        }
    }
    public function getLocationDetailsFromBarcodeIfCapacity(Request $request){

        $validator = Validator::make($request->all(), [
            'location_barcode' => 'required',
            'capacity_required' => 'required',

        ]);

        $sub_user = Auth::user();
        $sub_user_location_id =$sub_user->order_pickup_location;


        if ($validator->fails()) {
            return $this->handleError($validator->errors(),'');
        }

        try{           
                     
            $seller_ffc_location = SellerFfcLocation::where('barcode', $request->location_barcode)->where('seller_id', $sub_user->seller_id)->where('location_id',$sub_user_location_id)->first();
            if($seller_ffc_location){
                //The below is commented to address FLH-1588
                // $location_current_capacity = $seller_ffc_location->getCurrentUtilization();
                // $current_capacity = $seller_ffc_location->total_capacity - $location_current_capacity[0]->capacity;
                // if($current_capacity >= 1){
                    $data['location'] = $seller_ffc_location;
                    // $data['location_capacity'] = ( $current_capacity >= $request->capacity_required ? $request->capacity_required : $current_capacity) ;
                    return $this->handleResponse($data, 'Location Data Retrieved.');
                // }else{
                //     return $this->handleError('The desired location doesnt have the capacity requested.','',200);
                // }
            }else{
                return $this->handleError('No Location Found.','',200);
            }
                     
        }catch (\Throwable $e) {
            return $this->handleError('Exception.', ['error'=>$e->getMessage()]);
        }
    }
    public function moveStockOld(Request $request){

        $validator = Validator::make($request->all(), [
            'product_id' => 'required',
            'location_id' => 'required',
            'quantity' => 'required',

        ]);

        $sub_user = Auth::user();


        if ($validator->fails()) {
            return $this->handleError($validator->errors(),'');
        }

        try{    
            $seller_stock_movement = SellerStockMovement::where('seller_sub_user_id',$sub_user->id)->where('seller_product_id',$request->product_id)->where('status',0)->first();
            if($seller_stock_movement){
                $qty_putway_pending = $seller_stock_movement->qty_picked - $seller_stock_movement->qty_putaway;

                if($request->quantity > $qty_putway_pending){
                    return $this->handleError('Quantity to putway cannot be greater than picked quantity.','',200);
                }

                $update_columns = [
                    'qty_putaway' => $seller_stock_movement->qty_putaway + $request->quantity
                ];

                if($qty_putway_pending <= $request->quantity) {
                    $update_columns['status'] = 1; 
                }

                /// Update Stock Movement
                if ( !SellerStockMovement::where('id',$seller_stock_movement->id)->where('seller_sub_user_id',$sub_user->id)->where('seller_product_id',$request->product_id)->where('qty_putaway','!=',$update_columns['qty_putaway'])->where('qty_picked','>=',$update_columns['qty_putaway'])->where('status',0)->update($update_columns) ) {
                    return $this->handleError('Stock movement failed','',200);
                }


                $seller_ffc_location = SellerFfcLocation::where('id', $request->location_id)->first();
                $seller_ffc_inventory = SellerFfcInventory::where('seller_ffc_location_id', $seller_ffc_location->id)->where('product_id', $request->product_id)->where('seller_id', $sub_user->seller_id)->first();
                if($seller_ffc_inventory){
                    $seller_ffc_inventory->qty = $seller_ffc_inventory->qty + $request->quantity;
                    $seller_ffc_inventory->uncommitted_qty = $seller_ffc_inventory->uncommitted_qty + $request->quantity;
                }else{
                    $seller_ffc_inventory = new SellerFfcInventory;
                    $seller_ffc_inventory->seller_id = $sub_user->seller_id;
                    $seller_ffc_inventory->seller_location_id = $seller_ffc_location->location_id;
                    $seller_ffc_inventory->seller_ffc_location_id = $request->location_id;
                    $seller_ffc_inventory->product_id = $request->product_id;
                    $seller_ffc_inventory->qty = $request->quantity;
                    $seller_ffc_inventory->uncommitted_qty = $request->quantity;
                }

                hold_data_for_internal_logger(
                    $sub_user->id,
                    $sub_user->seller_id,
                    $seller_ffc_inventory->id ?? Null,
                    'FFC Inventory',
                    'qty, uncommitted_qty',
                    'Inventory Item'
                );

                $seller_ffc_inventory->save();
                

                $seller_item_picked_ffc_location = SellerFfcLocation::where('id', $seller_stock_movement->seller_ffc_location_id)->first();

                hold_data_for_internal_logger(
                    $sub_user->id,
                    $sub_user->seller_id,
                    Null,
                    'Ledger Inventory',
                    'stock, uncommitted_stock',
                    'Inventory Item'
                );
                
                if($seller_item_picked_ffc_location->location_type == config('enum.storage_location_types')['Processing']){
                    if($seller_ffc_location->location_type == config('enum.storage_location_types')['Unsellable'] ){
                        Inventory::decrementUncommittedStock($seller_item_picked_ffc_location->location_id, $request->product_id ,$request->quantity);
                        Inventory::updateOnlyUnsellableStock($seller_item_picked_ffc_location->location_id, $request->product_id ,$request->quantity);
                    }
                }

                if($seller_item_picked_ffc_location->location_type == config('enum.storage_location_types')['Unsellable'] ){
                    if($seller_ffc_location->location_type == config('enum.storage_location_types')['Processing']){
                        Inventory::incrementUncommittedStock($seller_ffc_location->location_id,$request->product_id , $request->quantity);
                        Inventory::updateOnlyUnsellableStock($seller_ffc_location->location_id, $request->product_id , ((-1)*($request->quantity)));
                    }
                }

                return $this->handleResponse('', 'Stock moved succesfully.');
            }else{
                return $this->handleError('Stock picked was not found.','',200);
            }
                     
        }catch (\Throwable $e) {
            return $this->handleError('Exception.', ['error'=>$e->getMessage()]);
        }
    }

    public function moveStock(Request $request){

        $validator = Validator::make($request->all(), [
            'products' => ['required', 'array'],
            'location_id' => 'required'
        ]);

        $sub_user = Auth::user();


        if ($validator->fails()) {
            return $this->handleError($validator->errors(),'');
        }

        try{    
            
            if(count($request->products) > 0){
                foreach($request->products as $product){
                    $product = (object) $product;
                    $qty_available  = $product->quantity;
                    $qty_available_ffc  = 0;
                    $seller_stock_movement = SellerStockMovement::where('seller_sub_user_id',$sub_user->id)->where('seller_product_id',$product->product_id)->where('status',0)->get();
                
                    if(count($seller_stock_movement) > 0){
                       
                        foreach($seller_stock_movement as $sm){
                            $qty_required = $sm->qty_picked - $sm->qty_putaway;
                           \Log::info($qty_available."-----".$qty_required); 
                        

                            if($qty_available < $qty_required){
                                $sm->qty_putaway = $sm->qty_putaway + $qty_available;
                                if($qty_available > 0){
                                    $qty_available_ffc = $qty_available;
                                }else{
                                    $qty_available_ffc = 0;
                                }
                                $qty_available = 0;
                                $sm->save();
                            }

                            if($qty_available == $qty_required){
                                $sm->qty_putaway = $sm->qty_putaway + $qty_available;
                                $sm->status = 1;
                                if($qty_available > 0){
                                    $qty_available_ffc = $qty_available;
                                }else{
                                    $qty_available_ffc = 0;
                                }
                                $qty_available = 0;
                                $sm->save();
                            }

                            if($qty_available > $qty_required){
                                $sm->qty_putaway = $sm->qty_putaway + $qty_required;
                                $sm->status = 1;
                                $qty_available = $qty_available - $qty_required;
                                $qty_available_ffc = $qty_required;
                                $sm->save();
                            }

                            $before_qty = 0;
                            $before_uncommitted = 0;

                            $seller_ffc_location = SellerFfcLocation::where('id', $request->location_id)->first();
                            $seller_ffc_inventory = SellerFfcInventory::where('seller_ffc_location_id', $seller_ffc_location->id)->where('product_id', $product->product_id)->where('seller_id', $sub_user->seller_id)->first();
                            if($seller_ffc_inventory){

                                $before_qty = $seller_ffc_inventory->qty;
                                $before_uncommitted = $seller_ffc_inventory->uncommitted_qty;

                                $seller_ffc_inventory->qty = $seller_ffc_inventory->qty + $qty_available_ffc;
                                $seller_ffc_inventory->uncommitted_qty = $seller_ffc_inventory->uncommitted_qty + $qty_available_ffc;
                            }else{
                                $seller_ffc_inventory = new SellerFfcInventory;
                                $seller_ffc_inventory->seller_id = $sub_user->seller_id;
                                $seller_ffc_inventory->seller_location_id = $seller_ffc_location->location_id;
                                $seller_ffc_inventory->seller_ffc_location_id = $request->location_id;
                                $seller_ffc_inventory->product_id = $product->product_id;
                                $seller_ffc_inventory->qty = $qty_available_ffc;
                                $seller_ffc_inventory->uncommitted_qty = $qty_available_ffc;
                            }

                            hold_data_for_internal_logger(
                                $sub_user->seller_id,
                                'Sub User',
                                $sub_user->id,
                                $sub_user->full_name,
                                'FFC Inventory',
                                'qty, uncommitted_qty',
                                'Stock Movement',
                                $sm->id,
                                $sm->id,
                                'Stock Movement Completed',
                                'Stock movement process completed and FFC inventory has been incremented.'
                            );

                            $seller_ffc_inventory->save();
                            

                            $seller_item_picked_ffc_location = SellerFfcLocation::where('id', $sm->seller_ffc_location_id)->first();

                            hold_data_for_internal_logger(
                                $sub_user->seller_id,
                                'Sub User',
                                $sub_user->id,
                                $sub_user->full_name,
                                'Ledger Inventory',
                                'stock, uncommitted_stock',
                                'Stock Movement',
                                $sm->id,
                                $sm->id,
                                'Stock Movement Completed',
                                'Stock movement process completed and Ledger inventory has been updated.'
                            );

                            $sellable = [
                                config('enum.storage_location_types')['Processing'],
                                config('enum.storage_location_types')['Reserve']
                            ];
                            
                            if( in_array($seller_item_picked_ffc_location->location_type, $sellable) ){
                                if($seller_ffc_location->location_type == config('enum.storage_location_types')['Unsellable'] ){
                                    Inventory::decrementUncommittedStock($seller_item_picked_ffc_location->location_id, $product->product_id ,$qty_available_ffc);
                                    Inventory::updateOnlyUnsellableStock($seller_item_picked_ffc_location->location_id, $product->product_id ,$qty_available_ffc);
                                }
                            }

                            if($seller_item_picked_ffc_location->location_type == config('enum.storage_location_types')['Unsellable'] ){
                                if( in_array($seller_ffc_location->location_type, $sellable) ){
                                    Inventory::incrementUncommittedStock($seller_ffc_location->location_id,$product->product_id , $qty_available_ffc);
                                    Inventory::updateOnlyUnsellableStock($seller_ffc_location->location_id, $product->product_id , ((-1)*($qty_available_ffc)));
                                }
                            }



                            PickAndPutawayLog::add(
                                $sub_user->seller_id,
                                $product->product_id,
                                $product->barcode,
                                'Stock Movement',
                                'Putaway',
                                'Stock Movement',
                                $sm->id,
                                $sub_user->id,
                                $request->location_id,
                                SellerFfcLocation::whereId($request->location_id)->value('location_name'),
                                $before_uncommitted,
                                $seller_ffc_inventory->uncommitted_qty,
                                $before_qty,
                                $seller_ffc_inventory->qty
                            );


                        }

                        
                    }else{
                        return $this->handleError('Stock picked was not found for product .','',200);
                    }
                }
                return $this->handleResponse('', 'Stock moved succesfully');
            }else{
                return $this->handleError('No Products Found .','',200);
            }
                     
        }catch (\Throwable $e) {
            return $this->handleError('Exception.', ['error'=>$e->getMessage()]);
        }
    }

    public function getLocationIfProductInventoryExist(Request $request){

        $validator = Validator::make($request->all(), [
            'location_barcode' => 'required',
            'product_id' => 'required'

        ]);

        $sub_user = Auth::user();
        $sub_user_location_id =$sub_user->order_pickup_location;


        if ($validator->fails()) {
            return $this->handleError($validator->errors(),'');
        }

        try{           
                     
            $seller_ffc_location = SellerFfcLocation::where('barcode', $request->location_barcode)->where('seller_id', $sub_user->seller_id)->where('location_id',$sub_user_location_id)->first();

            if($seller_ffc_location){
                $seller_ffc_inventory = SellerFfcInventory::where('product_id',$request->product_id)
                ->where('seller_location_id', $sub_user->order_pickup_location)
                ->where('seller_ffc_location_id', $seller_ffc_location->id)
                ->where('seller_id',  $sub_user->seller_id)
                ->where('qty','!=', 0)->first();

                if($seller_ffc_inventory){
                    if($seller_ffc_inventory->qty > 0){
                        return $this->handleResponse($seller_ffc_location, 'Location Data Retrieved.');
                    }else{
                        return $this->handleError('Product does not exist in the Location scanned!','',200);

                    }
                }else{
                    return $this->handleError('Product does not exist in the Location scanned!.','',200);
                }
            }else{
                return $this->handleError('Location does not exist!','',200);

            }
                     
        }catch (\Throwable $e) {
            return $this->handleError('Exception.', ['error'=>$e->getMessage()]);
        }
        
    }

    public function saveLocationFirstPutaway(Request $request){
        $validator = Validator::make($request->all(), [
            'products' => 'required ',
            'location_id' => 'required',           
        
        ]);

        if ($validator->fails()) {
            return $this->handleError($validator->errors(),'');
        }
        $sub_user = Auth::user();
        $products = array_count_values($request->products);
        $location_id = $request->location_id;
        $mergeArray = [];
        $response = [];
        try{
            foreach($products as $product => $qty)
            {
                $mergeArray['product_id'] = $product;
                $mergeArray['qty'] = $qty;
                $request->merge($mergeArray);
                $response[$product] = $this->updatePutawayNew($request);
            }
            return $this->handleResponse($response, 'Bulk Putaway Completed.');
        }
        catch(\Throwable $e) {
            return $this->handleError('Exception.', ['error'=>$e->getMessage()]);
            
        }

    }

    public function getPendingPutAwayItemFromBarcode(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'product_barcode' => 'required'
        ]);
        try{
            $sub_user = Auth::user();
            $sub_user_location_id =$sub_user->order_pickup_location;
            $product = Product::where('barcode', $request->product_barcode)->where('seller_id', $sub_user->seller_id)->first();
            if(!$product)
            {
                return $this->handleError('Invalid Barcode','',200);
            }
            $stock_orders = StockOrder::where('seller_id', $sub_user->seller_id)->where('location_id',$sub_user_location_id)->pluck('id')->implode(',');
            $stock_order_items = StockOrderItem::whereIn('stock_order_id', explode(',',$stock_orders))
            ->where('product_id', $product->id)
            ->where('qty_received','>',0)
            ->whereColumn('putaway_qty', '<', 'qty_received')
            ->join('products','stock_order_items.product_id','products.id')
            ->selectRaw('stock_order_items.id,stock_order_items.qty,stock_order_items.qty_received,stock_order_items.putaway_qty,products.sku,stock_order_items.product_id')
            ->get();
            $item_detail = [];
            if($stock_order_items->count() > 0) {
                foreach($stock_order_items as $stock_order_item){
                    $product = Product::find($stock_order_item->product_id);
                    if(!isset($item_detail[$stock_order_item->sku])){
                        $item_detail[$stock_order_item->sku]['qty_received'] = $stock_order_item->qty_received;
                        $item_detail[$stock_order_item->sku]['product_id'] = $stock_order_item->product_id;
                        $item_detail[$stock_order_item->sku]['putaway_qty'] = $stock_order_item->putaway_qty;
                        $item_detail[$stock_order_item->sku]['barcode'] = $product->barcode;
                        $item_detail[$stock_order_item->sku]['pending_putaway'] = $item_detail[$stock_order_item->sku]['qty_received'] - $item_detail[$stock_order_item->sku]['putaway_qty'];

                    }else{
                        $item_detail[$stock_order_item->sku]['qty_received'] = $item_detail[$stock_order_item->sku]['qty_received'] +  $stock_order_item->qty_received;
                        $item_detail[$stock_order_item->sku]['product_id'] = $stock_order_item->product_id;
                        $item_detail[$stock_order_item->sku]['putaway_qty'] = $item_detail[$stock_order_item->sku]['putaway_qty'] +  $stock_order_item->putaway_qty;    
                        $item_detail[$stock_order_item->sku]['barcode'] = $product->barcode;
                        $item_detail[$stock_order_item->sku]['pending_putaway'] = $item_detail[$stock_order_item->sku]['qty_received'] - $item_detail[$stock_order_item->sku]['putaway_qty'];

                    }
                }

                return $this->handleResponse($item_detail, 'Product Data Retrieved.');
            }
            return $this->handleError('Pending Putaway for this Item was not Found','', 200);
        }
        catch (\Throwable $e) {
            return $this->handleError('Exception.', ['error'=>$e->getMessage()]);
        }
    }

    public function removeCommittedFOunpickedItem(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'fo_id' => 'required',
            'fo_item_id' => 'required',
            'ffc_location_id' => 'required',
            'quantity' => 'required'

        ]);
        \Log::info($request);

        try{
            $sub_user = Auth::user();
            $sub_user_location_id =$sub_user->order_pickup_location;
            $fulfillment_order = FulfillmentOrder::where('id',$request->fo_id)->first();

            if($fulfillment_order){

                $fulfillment_order_item = FulfillmentOrderItem::where('id',$request->fo_item_id)->first();
                $order_item = $fulfillment_order_item->order_item;

                hold_data_for_internal_logger(
                    $sub_user->seller_id,
                    'Sub User',
                    $sub_user->id,
                    $sub_user->full_name,
                    'FFC Inventory',
                    'qty',
                    'Fulfillment Order',
                    $fulfillment_order->id,
                    $fulfillment_order->reference_id,
                    'Fulfillment Order Item Not Available',
                    'Fulfillment order item (Barcode : '.$order_item->barcode.') is not available during picking and unpicked committed FFC inventory has been reverted.'
                );

                FulfillmentOrderItemFFCLocation::revertNonPickedCommitInventoryItemwise($fulfillment_order->id,$fulfillment_order_item->id);

                $required_qty = $request->quantity;
                $seller_ffc_inventory = SellerFfcInventory::where('product_id',$fulfillment_order_item->order_item->product_id)
                                            ->where('seller_location_id', $fulfillment_order->seller_location_id)
                                            ->where('uncommitted_qty','!=', 0)
                                            ->where('seller_ffc_location_id','!=', $request->ffc_location_id)
                                            ->whereHas('ffc_location' , function($query) {
                                                $query->where('location_type', config('enum.storage_location_types')['Processing']);
                                            });

                $product_id = $order_item->product_id;
                $product = Product::find($product_id); // this is to confirm if the product id exist in order_itens table was not deleted in products table


                PickListInventoryNotAvailableLogs::add(
                    $sub_user->seller_id,
                    $fulfillment_order->id, 
                    $sub_user->id, 
                    $fulfillment_order->sub_user_assigned_by, 
                    $fulfillment_order->sub_user_assigned_date, 
                    $order_item->barcode, 
                    $request->quantity, 
                    $request->ffc_location_id
                );

                OrderComments::add($fulfillment_order->order_id, 'Picking Process', 'Inventory is not available for item <b>'.$order_item->barcode.'</b> with quantity <b>'.$request->quantity.'</b> on bin location <b>'.SellerFfcLocation::getInventoryPath($request->ffc_location_id).'</b>', 'Success', $sub_user->id);		


                if(!$product) {
                    $product = Product::where('SKU', $order_item->SKU)->where('seller_id', $sub_user->seller_id)->where('barcode', $order_item->barcode)->first();
                }
                $pick_items = [];

                if ($product) {
                    if($seller_ffc_inventory->sum('uncommitted_qty') >= $required_qty) {

                        foreach($seller_ffc_inventory->orderBy('qty', 'DESC')->get() as $sfi) {
                            $inventory_quantity = $sfi->uncommitted_qty;
                            $search_keys = array_keys(array_column($pick_items, 'inventory_id'), $sfi->id);

                            foreach ($search_keys as $key => $value) {
                                $inventory_quantity -= $pick_items[$value]['quantity'];
                            }

                            if($required_qty > 0) {
                                
                                if($inventory_quantity == $required_qty) {
        
                                    $inventory_path = SellerFfcLocation::getInventoryPath($sfi->seller_ffc_location_id);
                                    $pick_items[] = ['tote_no' => $fulfillment_order->tote_no, 'reference_id' => $fulfillment_order->reference_id, 'fulfillment_order_id' => $fulfillment_order->id, 'seller_location_id' => $fulfillment_order->seller_location_id, 'inventory_id' => $sfi->id, 'item_id' => $fulfillment_order_item->id,'product_id' => $product->id, 'product_name' => $product->product_name  ,'sku' => $product->SKU, 'barcode' => $product->barcode, 'quantity' => $inventory_quantity, 'ffc_location_id' => $sfi->seller_ffc_location_id, 'inventory_path' => $inventory_path, 'distance' => $sfi->ffc_location->sequence];
                                    $required_qty = 0;
                                    break;
        
                                } elseif($inventory_quantity > $required_qty) {
        
                                    $inventory_path = SellerFfcLocation::getInventoryPath($sfi->seller_ffc_location_id);
                                    $pick_items[] = ['tote_no' => $fulfillment_order->tote_no, 'reference_id' => $fulfillment_order->reference_id, 'fulfillment_order_id' => $fulfillment_order->id, 'seller_location_id' => $fulfillment_order->seller_location_id, 'inventory_id' => $sfi->id, 'item_id' => $fulfillment_order_item->id,'product_id' => $product->id, 'product_name' => $product->product_name  ,'sku' => $product->SKU, 'barcode' => $product->barcode, 'quantity' => $required_qty, 'ffc_location_id' => $sfi->seller_ffc_location_id, 'inventory_path' => $inventory_path, 'distance' => $sfi->ffc_location->sequence];
                                    $required_qty = 0;
                                    break;
                            
                                } elseif($inventory_quantity && $inventory_quantity < $required_qty) {
        
                                    $inventory_path = SellerFfcLocation::getInventoryPath($sfi->seller_ffc_location_id);
                                    $pick_items[] = ['tote_no' => $fulfillment_order->tote_no, 'reference_id' => $fulfillment_order->reference_id, 'fulfillment_order_id' => $fulfillment_order->id, 'seller_location_id' => $fulfillment_order->seller_location_id, 'inventory_id' => $sfi->id, 'item_id' => $fulfillment_order_item->id,'product_id' => $product->id, 'product_name' => $product->product_name  ,'sku' => $product->SKU, 'barcode' => $product->barcode, 'quantity' => $inventory_quantity, 'ffc_location_id' => $sfi->seller_ffc_location_id, 'inventory_path' => $inventory_path, 'distance' => $sfi->ffc_location->sequence];
                                    $required_qty = $required_qty - $inventory_quantity;
                                }
                            }
                        }
                    }
                }

                //** Start Here - This section of code is related to stock creation and movmement  */
                $products = [];
                $data['location_id'] = $request->ffc_location_id;
                if ($product) {
                    $products[0]['id'] = $product->id;
                    $products[0]['product_id'] = $product->id;
                    $products[0]['barcode'] = $product->barcode;
                }
                $products[0]['quantity'] = $request->quantity;
                $data['products'] = $products;
                $ffc_controller = new self;
                //** End Here - This section of code is related to stock creation and movmement  */

                \Log::info($pick_items);
                if ($pick_items) { // this means that the inventory is available in other sellable bin
                    foreach ($pick_items as $pick_item) {
                        FulfillmentOrderItemFFCLocation::commitInventory(
                            $pick_item['fulfillment_order_id'],
                            $pick_item['item_id'],
                            $pick_item['product_id'],
                            $pick_item['ffc_location_id'],
                            $pick_item['quantity']
                        );
                        SellerFfcInventory::deductUncommitted($pick_item['inventory_id'], $pick_item['quantity']);
                        $ffc_controller->createAndMoveStock($data,$sub_user);
                    }
                }else{
                    $fulfillment_order_item_ffc_locations = FulfillmentOrderItemFFCLocation::where('fulfillment_order_id', $fulfillment_order->id)->where('is_picked',1)->get();                   
                    
                    if(count($fulfillment_order_item_ffc_locations) > 0){
                        $fulfillment_order->picked(); //marking FO picked to ensure the FO is allowed initate packing as it only allows to go into the packing screen in case the FO is marked as picked
                        $fulfillment_order->unavailable();
                        FulfillmentOrderItemFFCLocation::revertNonPickedCommitInventory($fulfillment_order->id);
                        $ffc_controller->createAndMoveStock($data,$sub_user);
                                                
                    }else{
                        $fulfillment_order->reject();
                        FulfillmentOrderItemFFCLocation::revertNonPickedCommitInventory($fulfillment_order->id);
                        foreach ($fulfillment_order->items as $key => $item) {
                            Inventory::reStock($sub_user->seller_id, $item->order_item_id);
                        }

                        $ffc_controller->createAndMoveStock($data,$sub_user);

                        $order = Order::findOrFail($fulfillment_order->order_id);
                        $order->assignLocationToOrderItems();
                    }

                }

                return $this->handleResponse("", 'FO Item committment was removed and re assigned .');
            }else{
                return $this->handleError('Fulfillment Order was not found!','', 200);
            }
            
        }
        catch (\Throwable $e) {
            return $this->handleError('Exception.', ['error'=>$e->getMessage()]);
        }
    }
    
    public function createAndMoveStock($data,$sub_user){

        $sub_user_location_id =$sub_user->order_pickup_location;
        $ffc_controller = new self;
        $request = new Request($data);
        $result = $ffc_controller->createStockMovement($request);
        $result = json_decode($result->getContent(),true);
        $data['location_id'] = SellerFfcLocation::where('barcode', config('enum.UNAVAILABLE_PRODUCT_BARCODE'))->where('seller_id', $sub_user->seller_id)->where('location_id',$sub_user_location_id)->value('id');
        $request = new Request($data);
        $result = $ffc_controller->moveStock($request);
        $result = json_decode($result->getContent(),true);

    }



    ///--- Setting ---\\\


    public function itemCountingMode(Request $request) 
    {
        try {

            $sub_user = Auth::user();

            SellerUserPermission::where('seller_user_id',$sub_user->id)->update(['item_counting_mode' => $request->item_counting_mode]);
            return $this->handleResponse([], 'Item Counting mode successfully switch.');

        } catch (\Throwable $e) {
            return $this->handleError('Exception.', ['error'=>$e->getMessage()]);
        }
    }
}